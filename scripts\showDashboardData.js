import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

async function showDashboardData() {
  let driver = null;

  try {
    console.log('🎯 D1NAMO Dashboard Data Preview');
    console.log('═══════════════════════════════════════');

    driver = neo4j.driver(
      process.env.VITE_NEO4J_URI,
      neo4j.auth.basic(process.env.VITE_NEO4J_USERNAME, process.env.VITE_NEO4J_PASSWORD)
    );

    const session = driver.session();

    try {
      // Show D1NAMO patients with data (what the dashboard will load)
      console.log('\n👥 D1NAMO Patients (as seen in dashboard):');
      const patientsResult = await session.run(`
        MATCH (p:Patient:D1NAMOSubject)
        OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
        OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
        WITH p, count(DISTINCT e) as ecgCount, count(DISTINCT g) as glucoseCount
        WHERE ecgCount > 0 OR glucoseCount > 0
        RETURN p.patientId as patientId,
               p.name as name,
               p.condition as condition,
               p.age as age,
               p.gender as gender,
               ecgCount,
               glucoseCount
        ORDER BY p.patientId
      `);

      console.log('┌─────────┬──────────────────────┬─────────────────┬─────┬────────┬─────────┬─────────┐');
      console.log('│ Patient │ Name                 │ Condition       │ Age │ Gender │ ECG     │ Glucose │');
      console.log('├─────────┼──────────────────────┼─────────────────┼─────┼────────┼─────────┼─────────┤');

      patientsResult.records.forEach(record => {
        const patientId = record.get('patientId');
        const name = record.get('name').substring(0, 20);
        const condition = record.get('condition').substring(0, 15);
        const age = record.get('age') || 'N/A';
        const gender = record.get('gender') || 'N/A';
        const ecgCount = record.get('ecgCount').toNumber();
        const glucoseCount = record.get('glucoseCount').toNumber();

        console.log(`│ ${patientId.padEnd(7)} │ ${name.padEnd(20)} │ ${condition.padEnd(15)} │ ${age.toString().padStart(3)} │ ${gender.padStart(6)} │ ${ecgCount.toString().padStart(7)} │ ${glucoseCount.toString().padStart(7)} │`);
      });

      console.log('└─────────┴──────────────────────┴─────────────────┴─────┴────────┴─────────┴─────────┘');

      // Show recent ECG-Glucose correlations (what dashboard charts will show)
      console.log('\n💓🍯 ECG-Glucose Correlations (last 24 hours):');
      const correlationResult = await session.run(`
        MATCH (p:Patient:D1NAMOSubject {patientId: '001'})
        MATCH (p)-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
        OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
        WHERE abs(duration.between(e.timestamp, g.timestamp).minutes) <= 30
        RETURN e.timestamp as ecgTime,
               f.heartRate as heartRate,
               f.hrv_rmssd as hrv,
               g.timestamp as glucoseTime,
               g.value as glucoseValue,
               g.trend as trend
        ORDER BY e.timestamp DESC
        LIMIT 10
      `);

      if (correlationResult.records.length > 0) {
        console.log('┌──────────────────┬─────┬─────┬──────────────────┬─────────┬────────┐');
        console.log('│ ECG Time         │ HR  │ HRV │ Glucose Time     │ Glucose │ Trend  │');
        console.log('├──────────────────┼─────┼─────┼──────────────────┼─────────┼────────┤');

        correlationResult.records.forEach(record => {
          const ecgTime = new Date(record.get('ecgTime')).toLocaleTimeString();
          const heartRate = record.get('heartRate');
          const hrv = record.get('hrv');
          const glucoseTime = record.get('glucoseTime') ? new Date(record.get('glucoseTime')).toLocaleTimeString() : 'N/A';
          const glucoseValue = record.get('glucoseValue') || 'N/A';
          const trend = record.get('trend') || 'N/A';

          console.log(`│ ${ecgTime.padEnd(16)} │ ${heartRate.toString().padStart(3)} │ ${hrv.toString().padStart(3)} │ ${glucoseTime.padEnd(16)} │ ${glucoseValue.toString().padStart(7)} │ ${trend.padEnd(6)} │`);
        });

        console.log('└──────────────────┴─────┴─────┴──────────────────┴─────────┴────────┘');
      } else {
        console.log('   No correlations found within 30-minute windows');
      }

      // Show ECG analytics (what dashboard will calculate)
      console.log('\n📊 ECG Analytics Summary:');
      const analyticsResult = await session.run(`
        MATCH (p:Patient:D1NAMOSubject)-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
        RETURN p.patientId as patientId,
               avg(f.heartRate) as avgHeartRate,
               min(f.heartRate) as minHeartRate,
               max(f.heartRate) as maxHeartRate,
               avg(f.hrv_rmssd) as avgHRV,
               count(e) as totalReadings
        ORDER BY p.patientId
      `);

      console.log('┌─────────┬─────────────┬─────────────┬─────────────┬─────────┬──────────┐');
      console.log('│ Patient │ Avg HR      │ Min HR      │ Max HR      │ Avg HRV │ Readings │');
      console.log('├─────────┼─────────────┼─────────────┼─────────────┼─────────┼──────────┤');

      analyticsResult.records.forEach(record => {
        const patientId = record.get('patientId');
        const avgHR = Math.round(record.get('avgHeartRate'));
        const minHR = record.get('minHeartRate');
        const maxHR = record.get('maxHeartRate');
        const avgHRV = Math.round(record.get('avgHRV'));
        const readings = record.get('totalReadings').toNumber();

        console.log(`│ ${patientId.padEnd(7)} │ ${avgHR.toString().padStart(11)} │ ${minHR.toString().padStart(11)} │ ${maxHR.toString().padStart(11)} │ ${avgHRV.toString().padStart(7)} │ ${readings.toString().padStart(8)} │`);
      });

      console.log('└─────────┴─────────────┴─────────────┴─────────────┴─────────┴──────────┘');

      // Show glucose patterns
      console.log('\n🍯 Glucose Patterns:');
      const glucoseResult = await session.run(`
        MATCH (p:Patient:D1NAMOSubject)-[:HAD_READING]->(g:GlucoseReading)
        RETURN p.patientId as patientId,
               avg(g.value) as avgGlucose,
               min(g.value) as minGlucose,
               max(g.value) as maxGlucose,
               count(g) as totalReadings
        ORDER BY p.patientId
      `);

      console.log('┌─────────┬─────────────┬─────────────┬─────────────┬──────────┐');
      console.log('│ Patient │ Avg Glucose │ Min Glucose │ Max Glucose │ Readings │');
      console.log('├─────────┼─────────────┼─────────────┼─────────────┼──────────┤');

      glucoseResult.records.forEach(record => {
        const patientId = record.get('patientId');
        const avgGlucose = Math.round(record.get('avgGlucose'));
        const minGlucose = record.get('minGlucose');
        const maxGlucose = record.get('maxGlucose');
        const readings = record.get('totalReadings').toNumber();

        console.log(`│ ${patientId.padEnd(7)} │ ${avgGlucose.toString().padStart(11)} │ ${minGlucose.toString().padStart(11)} │ ${maxGlucose.toString().padStart(11)} │ ${readings.toString().padStart(8)} │`);
      });

      console.log('└─────────┴─────────────┴─────────────┴─────────────┴──────────┘');

      console.log('\n✅ Dashboard Integration Ready!');
      console.log('🚀 Your D1NAMODashboard will now display:');
      console.log('   • Real patient data from Neo4j database');
      console.log('   • ECG readings with heart rate and HRV analysis');
      console.log('   • Glucose readings with trends and patterns');
      console.log('   • ECG-Glucose correlations and synchronized data');
      console.log('   • Interactive charts and analytics');

    } finally {
      await session.close();
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (driver) {
      await driver.close();
    }
  }
}

showDashboardData();
