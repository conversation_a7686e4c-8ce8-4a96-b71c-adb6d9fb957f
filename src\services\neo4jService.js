/**
 * Neo4j Service - Direct Database Connection
 * Connects directly to Neo4j AuraDB for real-time data access
 * Removes all mock data and fallbacks
 */

import neo4j from 'neo4j-driver';

class Neo4jService {
  constructor() {
    this.driver = null;
    this.isConnected = false;
    this.lastError = null;
    this.queryCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    // Prefer API proxy when running in browser/Pages environments
    const isPagesHost = (typeof window !== 'undefined') && /\.pages\.dev$/.test(window.location?.hostname || '')
    const envFlag = (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.VITE_NEO4J_USE_API === 'true')
    this.useApiProxy = Boolean(envFlag || isPagesHost); // Flag for API proxy mode
    this.queryStats = {
      totalQueries: 0,
      totalExecutionTime: 0,
      cacheHits: 0,
      avgExecutionTime: 0,
      slowQueries: [], // Track queries > 1000ms
      recentQueries: [] // Track last 10 queries
    };
  }

  // API proxy execution method for HTTP environments
  async executeViaApi(cypher, parameters = {}, options = {}) {
    const startTime = performance.now();

    try {
      console.log('🔄 Executing via Neo4j API proxy:', cypher.substring(0, 100) + '...');

      // Sanitize limit parameters before sending to API
      const sanitizedParameters = {};
      for (const [key, value] of Object.entries(parameters)) {
        if (key === 'limit' || key.toLowerCase().includes('limit')) {
          let intValue;

          // Log the original value for debugging
          console.log(`🔧 Processing limit parameter ${key}: ${value} (type: ${typeof value})`);

          if (Number.isInteger(value)) {
            intValue = value;
          } else if (typeof value === 'string') {
            intValue = parseInt(value, 10);
          } else if (typeof value === 'number') {
            // Handle floats by rounding to nearest integer
            intValue = Math.round(value);
            console.log(`🔧 Converted float ${value} to integer ${intValue}`);
          } else {
            intValue = parseInt(String(value), 10);
          }

          // Ensure positive integer with reasonable bounds
          intValue = isNaN(intValue) || intValue < 1 ? 1000 : Math.min(intValue, 50000);

          console.log(`🔧 Final sanitized ${key}: ${intValue}`);
          sanitizedParameters[key] = intValue;
        } else {
          sanitizedParameters[key] = value;
        }
      }

      console.log('🟡 Sanitized parameters for API proxy:', sanitizedParameters);

      const requestBody = { query: cypher, parameters: sanitizedParameters };
      const jsonBody = JSON.stringify(requestBody);
      console.log('🔧 JSON body being sent:', jsonBody);

      const response = await fetch('/api/neo4j', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: jsonBody
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`API request failed: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      const endTime = performance.now();

      console.log(`✅ API query completed in ${(endTime - startTime).toFixed(0)}ms`);

      // Update stats
      this.queryStats.totalQueries++;
      this.queryStats.totalExecutionTime += (endTime - startTime);
      this.queryStats.avgExecutionTime = this.queryStats.totalExecutionTime / this.queryStats.totalQueries;

      return {
        success: true,
        records: result.records || [],
        summary: result.summary || {}
      };

    } catch (error) {
      console.error('❌ API query execution failed:', error.message);
      throw new Error(`API query failed: ${error.message}`);
    }
  }

  // Removed simple convertNeo4jValue (keeping comprehensive version below)

  async connect(config = null) {
    try {
      console.log('🔗 Connecting to Neo4j AuraDB...');

      // Prefer API proxy when flagged or in Pages
      if (this.useApiProxy) {
        console.log('🔄 Using Neo4j API proxy for client connections');
        this.isConnected = true;
        return {
          success: true,
          message: 'Connected via Neo4j API proxy',
          server: { test: 1, timestamp: new Date().toISOString() },
          timestamp: new Date().toISOString()
        };
      }

      // Direct driver connection for non-proxy mode
      const uri = import.meta.env.VITE_NEO4J_URI || config?.uri;
      const username = import.meta.env.VITE_NEO4J_USERNAME || config?.username;
      const password = import.meta.env.VITE_NEO4J_PASSWORD || config?.password;

      if (!uri || !username || !password) {
        console.warn('Neo4j connection parameters not provided; falling back to API proxy');
        this.useApiProxy = true;
        this.isConnected = true;
        return {
          success: true,
          message: 'Connected via Neo4j API proxy (fallback)',
          server: { test: 1, timestamp: new Date().toISOString() },
          timestamp: new Date().toISOString()
        };
      }

      // Configure driver with proper options
      const driverConfig = {
        maxConnectionLifetime: 3 * 60 * 60 * 1000, // 3 hours
        maxConnectionPoolSize: 50,
        connectionAcquisitionTimeout: 2 * 60 * 1000, // 2 minutes
        disableLosslessIntegers: true, // Helps with integer conversion
      };

      // For development on HTTP with AuraDB, recommend using API proxy instead
      if (typeof window !== 'undefined' && window.location.protocol === 'http:') {
        console.log('🔧 HTTP detected with Neo4j AuraDB: SSL required for AuraDB connections');
        console.log('� Recommendation: Use API proxy (VITE_NEO4J_USE_API=true) for HTTP development');
        // Still attempt connection but user should use API proxy for better compatibility
      }

      this.driver = neo4j.driver(uri, neo4j.auth.basic(username, password), driverConfig);
      console.log(`🔗 Using Neo4j URI: ${uri.replace(/\/\/.*@/, '//***@')}`); // Hide credentials in logs

      // Test connection
      const session = this.driver.session();
      try {
        const result = await session.run('RETURN 1 as test, datetime() as timestamp');
        const record = result.records[0];

        this.isConnected = true;
        this.lastError = null;

        console.log('✅ Connected to Neo4j AuraDB successfully');

        return {
          success: true,
          message: 'Connected to Neo4j AuraDB',
          server: {
            test: this.convertNeo4jValue(record.get('test')),
            timestamp: record.get('timestamp').toString()
          },
          timestamp: new Date().toISOString()
        };
      } finally {
        await session.close();
      }

    } catch (error) {
      // As a resilience measure in browsers, fall back to API proxy
      console.warn('⚠️ Direct Neo4j connection failed, attempting API proxy fallback:', error.message);
      this.lastError = error;
      this.useApiProxy = true;
      this.isConnected = true;
      return {
        success: true,
        message: 'Connected via Neo4j API proxy (after direct failure)',
        server: { test: 1, timestamp: new Date().toISOString() },
        timestamp: new Date().toISOString()
      };
    }
  }

  async executeQuery(cypher, parameters = {}, options = {}) {
    if (!this.isConnected) {
      throw new Error('Not connected to Neo4j database. Please connect first.');
    }

    // Use API proxy if enabled
    if (this.useApiProxy) {
      return this.executeViaApi(cypher, parameters, options);
    }

    if (!this.driver) {
      throw new Error('Neo4j driver not initialized');
    }

    // Sanitize parameters to ensure proper types for Neo4j
    const sanitizedParameters = {};
    for (const [key, value] of Object.entries(parameters)) {
      if (key === 'limit' || key.toLowerCase().includes('limit')) {
        // Ensure limit parameters are integers - handle floats and string representations
        let intValue;

        // Log the original value for debugging
        console.log(`🔧 Processing limit parameter ${key}: ${value} (type: ${typeof value})`);

        if (Number.isInteger(value)) {
          intValue = value;
        } else if (typeof value === 'string') {
          intValue = parseInt(value, 10);
        } else if (typeof value === 'number') {
          // Handle floats by rounding to nearest integer
          intValue = Math.round(value);
          console.log(`🔧 Converted float ${value} to integer ${intValue}`);
        } else {
          intValue = parseInt(String(value), 10);
        }

        // Ensure positive integer with reasonable bounds
        intValue = isNaN(intValue) || intValue < 1 ? 1000 : Math.min(intValue, 50000);

        // Convert to Neo4j Integer to ensure proper type handling
        sanitizedParameters[key] = neo4j.int(intValue);
        console.log(`🔧 Final sanitized ${key}: ${intValue} (Neo4j Integer)`);
      } else {
        sanitizedParameters[key] = value;
      }
    }

    const startTime = performance.now();

    console.log('🔍 Executing Neo4j query:', cypher.substring(0, 100) + '...');
    console.log('🔍 Original parameters:', parameters);
    console.log('🔍 Sanitized parameters:', sanitizedParameters);

    try {
      // Check cache if enabled
      const cacheKey = `${cypher}-${JSON.stringify(parameters)}`;
      if (!options.skipCache && this.queryCache.has(cacheKey)) {
        const cached = this.queryCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          console.log('📋 Using cached result');
          this.queryStats.cacheHits++;
          return cached.result;
        } else {
          this.queryCache.delete(cacheKey);
        }
      }

      // Execute query
      const session = this.driver.session();
      let result;

      try {
        const queryResult = await session.run(cypher, sanitizedParameters);

        // Process records to handle Neo4j types
        const records = queryResult.records.map(record => {
          const obj = {};
          record.keys.forEach(key => {
            const value = record.get(key);
            obj[key] = this.convertNeo4jValue(value);
          });
          return obj;
        });

        const executionTime = performance.now() - startTime;

        result = {
          success: true,
          records,
          summary: {
            recordCount: records.length,
            executionTime: Math.round(executionTime)
          }
        };

        // Cache result if enabled
        if (!options.skipCache) {
          this.queryCache.set(cacheKey, {
            result,
            timestamp: Date.now()
          });
        }

        this.updateQueryStats(cypher, executionTime, records.length);

        console.log(`✅ Query completed in ${Math.round(executionTime)}ms, returned ${records.length} records`);

        return result;

      } finally {
        await session.close();
      }

    } catch (error) {
      const executionTime = performance.now() - startTime;
      this.updateQueryStats(cypher, executionTime, 0, error);

      console.error('❌ Query execution failed:', error.message);
      console.error('❌ Query parameters:', sanitizedParameters);
      console.error('❌ Cypher query:', cypher);
      throw new Error(`Query failed: ${error.message}`);
    }
  }

  // Convenience wrapper to align with existing components expecting arrays
  async runQuery(cypher, parameters = {}, options = {}) {
    const result = await this.executeQuery(cypher, parameters, options);
    // Normalize shapes: some callers expect plain array
    if (Array.isArray(result)) return result;
    if (result && Array.isArray(result.records)) return result.records;
    return [];
  }

  // Convert Neo4j values to JSON-serializable values
  convertNeo4jValue(value) {
    if (value === null || value === undefined) {
      return null;
    }

    // Handle Neo4j Integer type
    if (neo4j.isInt(value)) {
      return this.convertNeo4jValue(value);
    }

    // Handle Neo4j DateTime type
    if (neo4j.isDateTime(value)) {
      return value.toString();
    }

    // Handle Neo4j Node type
    if (value.constructor && value.constructor.name === 'Node') {
      return {
        identity: this.convertNeo4jValue(value.identity),
        labels: value.labels,
        properties: this.convertNeo4jProperties(value.properties)
      };
    }

    // Handle Neo4j Relationship type
    if (value.constructor && value.constructor.name === 'Relationship') {
      return {
        identity: this.convertNeo4jValue(value.identity),
        start: this.convertNeo4jValue(value.start),
        end: this.convertNeo4jValue(value.end),
        type: value.type,
        properties: this.convertNeo4jProperties(value.properties)
      };
    }

    // Handle arrays
    if (Array.isArray(value)) {
      return value.map(v => this.convertNeo4jValue(v));
    }

    // Handle objects
    if (typeof value === 'object' && value !== null) {
      return this.convertNeo4jProperties(value);
    }

    return value;
  }

  // Helper function to convert object properties
  convertNeo4jProperties(properties) {
    const converted = {};
    for (const [key, value] of Object.entries(properties)) {
      converted[key] = this.convertNeo4jValue(value);
    }
    return converted;
  }

  updateQueryStats(cypher, executionTime, recordCount, error = null) {
    this.queryStats.totalQueries++;
    this.queryStats.totalExecutionTime += executionTime;
    this.queryStats.avgExecutionTime = this.queryStats.totalExecutionTime / this.queryStats.totalQueries;

    // Track slow queries
    if (executionTime > 1000) {
      this.queryStats.slowQueries.push({
        cypher: cypher.substring(0, 100),
        executionTime,
        recordCount,
        timestamp: new Date().toISOString(),
        error: error ? error.message : null
      });

      // Keep only last 20 slow queries
      if (this.queryStats.slowQueries.length > 20) {
        this.queryStats.slowQueries = this.queryStats.slowQueries.slice(-20);
      }
    }

    // Track recent queries
    this.queryStats.recentQueries.push({
      cypher: cypher.substring(0, 100),
      executionTime,
      recordCount,
      timestamp: new Date().toISOString(),
      error: error ? error.message : null
    });

    // Keep only last 10 queries
    if (this.queryStats.recentQueries.length > 10) {
      this.queryStats.recentQueries = this.queryStats.recentQueries.slice(-10);
    }
  }

  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      lastError: this.lastError ? this.lastError.message : null,
      queryStats: this.queryStats,
      cacheSize: this.queryCache.size
    };
  }

  clearCache() {
    this.queryCache.clear();
    console.log('🧹 Query cache cleared');
  }

  async close() {
    if (this.driver) {
      await this.driver.close();
      this.driver = null;
      this.isConnected = false;
      console.log('🔌 Neo4j connection closed');
    }
  }

  // Convenience methods for common queries
  async getAllPatients() {
    return this.executeQuery(`
      MATCH (p:Patient)
      RETURN p.patientId as patientId,
             p.name as name,
             p.age as age,
             p.gender as gender,
             p.condition as condition
      ORDER BY p.patientId
    `);
  }

  async getPatientGlucoseData(patientId, startDate = null, endDate = null) {
    let cypher = `
      MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
    `;

    const parameters = { patientId };

    if (startDate && endDate) {
      cypher += ` WHERE g.timestamp >= datetime($startDate) AND g.timestamp <= datetime($endDate)`;
      parameters.startDate = startDate;
      parameters.endDate = endDate;
    }

    cypher += `
      RETURN g.timestamp as timestamp,
             g.value as value,
             g.trend as trend
      ORDER BY g.timestamp
    `;

    return this.executeQuery(cypher, parameters);
  }

  async getD1NAMOPatients() {
    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
      OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      RETURN p.patientId as patientId,
             p.name as name,
             p.age as age,
             p.gender as gender,
             p.condition as condition,
             p.diabetesDuration as diabetesDuration,
             p.baselineHbA1c as baselineHbA1c,
             count(DISTINCT e) as ecgReadingCount,
             count(DISTINCT f) as ecgFeaturesCount,
             count(DISTINCT g) as glucoseReadingCount,
             CASE
               WHEN count(DISTINCT e) > 0 AND count(DISTINCT g) > 0 THEN 'complete'
               WHEN count(DISTINCT e) > 0 OR count(DISTINCT g) > 0 THEN 'partial'
               ELSE 'no-data'
             END as dataAvailability,
             min(coalesce(e.timestamp, g.timestamp)) as earliestReading,
             max(coalesce(e.timestamp, g.timestamp)) as latestReading
      ORDER BY p.patientId
    `);
  }

  async getECGData(patientId) {
    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})-[:HAD_ECG]->(e:ECGReading)
      OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
      RETURN e.readingId as readingId,
             e.timestamp as timestamp,
             e.duration as duration,
             e.signalQuality as signalQuality,
             f.heartRate as heartRate,
             f.hrv_rmssd as hrv,
             f.qtc_interval as qtc
      ORDER BY e.timestamp
      LIMIT 100
    `, { patientId });
  }

  async getSynchronizedECGGlucoseData(patientId, timeRangeDays = 7, limitRecords = 1000) {
    // Enhanced query with time-based synchronization and better feature extraction
    // Ensure limitRecords is an integer to avoid Neo4j LIMIT errors
    let safeLimit;

    console.log(`🔧 getSynchronizedECGGlucoseData received limitRecords: ${limitRecords} (type: ${typeof limitRecords})`);

    if (Number.isInteger(limitRecords)) {
      safeLimit = limitRecords;
    } else if (typeof limitRecords === 'number') {
      safeLimit = Math.round(limitRecords);
      console.log(`🔧 Converted float limit ${limitRecords} to integer ${safeLimit}`);
    } else {
      safeLimit = parseInt(limitRecords, 10) || 1000;
    }

    // Ensure reasonable bounds
    safeLimit = Math.max(1, Math.min(safeLimit, 50000));
    console.log(`🔧 Final safe limit: ${safeLimit}`);

    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      WHERE (e.timestamp IS NOT NULL OR g.timestamp IS NOT NULL)
      WITH e, f, g, p,
           // Find glucose readings within 15 minutes of ECG readings
           [gr IN [(p)-[:HAD_READING]->(glucose:GlucoseReading) | glucose]
            WHERE abs(duration.between(e.timestamp, gr.timestamp).minutes) <= 15 | gr] as nearbyGlucose
      UNWIND CASE WHEN size(nearbyGlucose) > 0 THEN nearbyGlucose
                  WHEN g IS NOT NULL THEN [g]
                  ELSE [null] END as syncedGlucose
      RETURN DISTINCT
             coalesce(e.timestamp, syncedGlucose.timestamp) as timestamp,
             e.timestamp as ecgTimestamp,
             syncedGlucose.timestamp as glucoseTimestamp,
             f.heartRate as heartRate,
             f.hrv_rmssd as hrv,
             f.qtc_interval as qtcInterval,
             f.rr_interval as rrInterval,
             syncedGlucose.value as glucoseValue,
             e.readingId as ecgId,
             syncedGlucose.readingId as glucoseId,
             e.signalQuality as signalQuality,
             CASE WHEN e.timestamp IS NOT NULL AND syncedGlucose.timestamp IS NOT NULL
                  THEN abs(duration.between(e.timestamp, syncedGlucose.timestamp).minutes)
                  ELSE null END as syncTimeDiff
      ORDER BY timestamp DESC
      LIMIT $limitRecords
    `, {
      patientId,
      limitRecords: safeLimit
    });
  }

  async getECGLeadData(patientId, timestamp, lead = 'II') {
    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})-[:HAD_ECG]->(e:ECGReading)
      WHERE e.timestamp = datetime($timestamp)
      OPTIONAL MATCH (e)-[:HAS_LEAD_DATA]->(l:ECGLead {lead: $lead})
      RETURN l.waveformData as waveformData,
             l.samplingRate as samplingRate,
             l.duration as duration
    `, { patientId, timestamp, lead });
  }

  async getECGGlucoseCorrelation(patientId) {
    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      WHERE abs(duration.between(e.timestamp, g.timestamp).minutes) <= 15
      RETURN f.heartRate as heartRate,
             f.hrv_rmssd as hrv,
             f.qtc_interval as qtc,
             g.value as glucoseValue,
             g.timestamp as timestamp
      ORDER BY g.timestamp
      LIMIT 500
    `, { patientId });
  }

  async getHeartRateVariability(patientId, timeRangeDays = 7) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeRangeDays);

    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
      WHERE e.timestamp >= datetime($startDate) AND e.timestamp <= datetime($endDate)
      RETURN e.timestamp as timestamp,
             f.hrv_rmssd as hrv,
             f.heartRate as heartRate
      ORDER BY e.timestamp
    `, {
      patientId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });
  }

  async getPatientDataQuality(patientId) {
    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      OPTIONAL MATCH (p)-[:HAD_MEASUREMENT]->(w:WeightMeasurement)
      OPTIONAL MATCH (p)-[:HAD_LAB_RESULT]->(l:LabResult)
      WITH p,
           collect(DISTINCT e) as ecgReadings,
           collect(DISTINCT f) as ecgFeatures,
           collect(DISTINCT g) as glucoseReadings,
           collect(DISTINCT w) as weightMeasurements,
           collect(DISTINCT l) as labResults
      WITH p, ecgReadings, ecgFeatures, glucoseReadings, weightMeasurements, labResults,
           size(ecgReadings) as ecgCount,
           size(ecgFeatures) as featuresCount,
           size(glucoseReadings) as glucoseCount,
           size(weightMeasurements) as weightCount,
           size(labResults) as labCount
      RETURN
        p.patientId as patientId,
        p.name as name,
        p.condition as condition,
        ecgCount,
        featuresCount,
        glucoseCount,
        weightCount,
        labCount,
        CASE WHEN ecgCount > 0 THEN min([r IN ecgReadings | r.timestamp]) ELSE null END as earliestECG,
        CASE WHEN ecgCount > 0 THEN max([r IN ecgReadings | r.timestamp]) ELSE null END as latestECG,
        CASE WHEN glucoseCount > 0 THEN min([r IN glucoseReadings | r.timestamp]) ELSE null END as earliestGlucose,
        CASE WHEN glucoseCount > 0 THEN max([r IN glucoseReadings | r.timestamp]) ELSE null END as latestGlucose,
        CASE
          WHEN ecgCount > 0 AND glucoseCount > 0 THEN 'excellent'
          WHEN ecgCount > 0 OR glucoseCount > 0 THEN 'good'
          ELSE 'limited'
        END as dataQuality
    `, { patientId });
  }

  async getD1NAMODataSummary() {
    return this.executeQuery(`
      MATCH (p:Patient:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      WITH
        count(DISTINCT p) as totalPatients,
        count(DISTINCT e) as totalECG,
        count(DISTINCT g) as totalGlucose,
        collect(DISTINCT p.condition) as conditions,
        min(coalesce(e.timestamp, g.timestamp)) as earliestData,
        max(coalesce(e.timestamp, g.timestamp)) as latestData
      RETURN
        totalPatients,
        totalECG,
        totalGlucose,
        conditions,
        earliestData,
        latestData,
        duration.between(earliestData, latestData).days as monitoringDays
    `);
  }

  static getCommonQueries() {
    return {
      // Glucose Analysis Category
      glucose_stats: {
        name: "Glucose Statistics",
        description: "Calculate comprehensive glucose statistics for a patient",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: coalesce($days, 30)})
          RETURN
            min(g.glucose) AS minGlucose,
            max(g.glucose) AS maxGlucose,
            avg(g.glucose) AS avgGlucose,
            stdev(g.glucose) AS stdDevGlucose,
            count(g) AS totalReadings,
            percentileCont(g.glucose, 0.25) AS q1Glucose,
            percentileCont(g.glucose, 0.5) AS medianGlucose,
            percentileCont(g.glucose, 0.75) AS q3Glucose
        `,
        parameters: ['patientId']
      },
      glucose_over_time: {
        name: "Glucose Over Time (AGP)",
        description: "Get glucose readings over time for AGP visualization - Primary AGP Query",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: coalesce($days, 30)})
          RETURN
            g.timestamp as timestamp,
            g.glucose as glucose,
            g.readingType as readingType,
            g.deviceId as deviceId
          ORDER BY g.timestamp ASC
          LIMIT coalesce($limit, 10000)
        `,
        parameters: ['patientId', 'days', 'limit']
      },
      daily_glucose_patterns: {
        name: "Daily Glucose Patterns",
        description: "Analyze glucose patterns by time of day for AGP insights",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: coalesce($days, 30)})
          WITH g, g.timestamp.hour as hour, g.timestamp.minute as minute
          WITH hour, (hour * 60 + minute) as minuteOfDay,
               avg(g.glucose) as avgGlucose,
               count(g) as readingCount,
               stdev(g.glucose) as stdDev
          WHERE readingCount >= 3
          RETURN minuteOfDay, hour, avgGlucose, stdDev, readingCount
          ORDER BY minuteOfDay
        `,
        parameters: ['patientId']
      },
      time_in_range: {
        name: "Time in Range Analysis",
        description: "Calculate time spent in different glucose ranges",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: coalesce($days, 30)})
          WITH count(g) as totalReadings,
               sum(CASE WHEN g.glucose < 54 THEN 1 ELSE 0 END) as veryLow,
               sum(CASE WHEN g.glucose >= 54 AND g.glucose < 70 THEN 1 ELSE 0 END) as low,
               sum(CASE WHEN g.glucose >= 70 AND g.glucose <= 180 THEN 1 ELSE 0 END) as inRange,
               sum(CASE WHEN g.glucose > 180 AND g.glucose <= 250 THEN 1 ELSE 0 END) as high,
               sum(CASE WHEN g.glucose > 250 THEN 1 ELSE 0 END) as veryHigh
          RETURN
            totalReadings,
            veryLow,
            low,
            inRange,
            high,
            veryHigh,
            (veryLow * 100.0 / totalReadings) as veryLowPercent,
            (low * 100.0 / totalReadings) as lowPercent,
            (inRange * 100.0 / totalReadings) as timeInRangePercent,
            (high * 100.0 / totalReadings) as highPercent,
            (veryHigh * 100.0 / totalReadings) as veryHighPercent
        `,
        parameters: ['patientId', 'days']
      },
      glucose_variability: {
        name: "Glucose Variability Metrics",
        description: "Calculate coefficient of variation and other variability metrics",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: coalesce($days, 30)})
          WITH avg(g.glucose) as meanGlucose, stdev(g.glucose) as stdGlucose, count(g) as totalReadings,
               collect(g.glucose ORDER BY g.timestamp) as glucoseValues
          WITH meanGlucose, stdGlucose, totalReadings, glucoseValues,
               (stdGlucose / meanGlucose * 100) as coefficientOfVariation
          RETURN
            meanGlucose,
            stdGlucose,
            coefficientOfVariation,
            totalReadings,
            CASE
              WHEN coefficientOfVariation <= 36 THEN 'Low variability'
              WHEN coefficientOfVariation <= 50 THEN 'Moderate variability'
              ELSE 'High variability'
            END as variabilityCategory
        `,
        parameters: ['patientId', 'days']
      },
      hypoglycemic_events: {
        name: "Hypoglycemic Events",
        description: "Find patterns in low glucose events for safety analysis",
        category: "Glucose Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.glucose < 70
            AND g.timestamp >= datetime() - duration({days: coalesce($days, 30)})
          WITH g
          ORDER BY g.timestamp
          WITH collect(g) as lowReadings
          UNWIND lowReadings as reading
          RETURN
            reading.timestamp as timestamp,
            reading.glucose as glucose,
            reading.readingType as readingType,
            CASE
              WHEN reading.glucose < 54 THEN 'Severe'
              ELSE 'Mild'
            END as severity
          ORDER BY reading.timestamp DESC
          LIMIT coalesce($limit, 100)
        `,
        parameters: ['patientId', 'days', 'limit']
      },

      // Patient Care Category
      patient_overview: {
        name: "Patient Overview",
        description: "Get comprehensive patient information and recent metrics",
        category: "Patient Care",
        query: `
          MATCH (p:Patient {patientId: $patientId})
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: 30})
          OPTIONAL MATCH (p)-[:HAS_CONDITION]->(c:Condition)
          OPTIONAL MATCH (p)-[:TAKES_MEDICATION]->(m:Medication)
          WITH p,
               avg(g.glucose) as avgGlucose30d,
               count(g) as readingCount30d,
               max(g.timestamp) as lastReading,
               collect(DISTINCT c.name) as conditions,
               collect(DISTINCT m.name) as medications
          RETURN
            p.patientId as patientId,
            p.name as name,
            p.age as age,
            p.gender as gender,
            p.condition as primaryCondition,
            p.email as email,
            p.phoneNumber as phone,
            avgGlucose30d,
            readingCount30d,
            lastReading,
            conditions,
            medications
        `,
        parameters: ['patientId']
      },
      recent_appointments: {
        name: "Recent Appointments",
        description: "Get patient appointments with outcomes and glucose trends",
        category: "Patient Care",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:HAD_APPOINTMENT]->(a:Appointment)
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= a.date - duration({days: 7})
            AND g.timestamp <= a.date + duration({days: 7})
          WITH a, avg(g.glucose) as avgGlucoseAroundVisit, count(g) as readingsAroundVisit
          RETURN
            a.date as appointmentDate,
            a.type as appointmentType,
            a.outcome as outcome,
            a.notes as notes,
            avgGlucoseAroundVisit,
            readingsAroundVisit
          ORDER BY a.date DESC
          LIMIT coalesce($limit, 10)
        `,
        parameters: ['patientId', 'limit']
      },

      // Clinical Analysis Category
      medication_effectiveness: {
        name: "Medication Effectiveness",
        description: "Analyze glucose control before and after medication changes",
        category: "Clinical Analysis",
        query: `
          MATCH (p:Patient {patientId: $patientId})-[:TAKES_MEDICATION]->(m:Medication)
          WITH p, m, m.startDate as medStart
          OPTIONAL MATCH (p)-[:HAD_READING]->(before:GlucoseReading)
          WHERE before.timestamp >= medStart - duration({days: 30})
            AND before.timestamp < medStart
          OPTIONAL MATCH (p)-[:HAD_READING]->(after:GlucoseReading)
          WHERE after.timestamp >= medStart
            AND after.timestamp <= medStart + duration({days: 30})
          WITH m,
               avg(before.glucose) as avgBefore,
               count(before) as countBefore,
               stdev(before.glucose) as stdBefore,
               avg(after.glucose) as avgAfter,
               count(after) as countAfter,
               stdev(after.glucose) as stdAfter
          WHERE countBefore > 0 AND countAfter > 0
          RETURN
            m.name as medication,
            m.dosage as dosage,
            m.startDate as startDate,
            avgBefore,
            avgAfter,
            (avgBefore - avgAfter) as improvement,
            stdBefore,
            stdAfter,
            countBefore,
            countAfter
        `,
        parameters: ['patientId']
      },

      // Research Analytics Category
      population_glucose_trends: {
        name: "Population Glucose Trends",
        description: "Compare patient glucose control across patient cohorts",
        category: "Research Analytics",
        query: `
          MATCH (p:Patient)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: coalesce($days, 30)})
          WITH p.condition as condition,
               avg(g.glucose) as avgGlucose,
               stdev(g.glucose) as stdGlucose,
               count(g) as readingCount,
               count(DISTINCT p) as patientCount
          WHERE readingCount > 100
          RETURN
            condition,
            patientCount,
            readingCount,
            avgGlucose,
            stdGlucose,
            (stdGlucose / avgGlucose * 100) as coefficientOfVariation
          ORDER BY avgGlucose DESC
        `,
        parameters: []
      },
      treatment_outcomes: {
        name: "Treatment Outcomes Analysis",
        description: "Analyze treatment effectiveness across different patient groups",
        category: "Research Analytics",
        query: `
          MATCH (p:Patient)-[:TAKES_MEDICATION]->(m:Medication)
          MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= m.startDate + duration({days: 30})
            AND g.timestamp >= datetime() - duration({days: 90})
          WITH m.name as medication, p.condition as condition,
               avg(g.glucose) as avgGlucose,
               count(DISTINCT p) as patientCount,
               count(g) as readingCount,
               sum(CASE WHEN g.glucose >= 70 AND g.glucose <= 180 THEN 1 ELSE 0 END) as inRangeCount
          WHERE patientCount >= 2
          RETURN
            medication,
            condition,
            patientCount,
            readingCount,
            avgGlucose,
            (inRangeCount * 100.0 / readingCount) as timeInRangePercent
          ORDER BY timeInRangePercent DESC
        `,
        parameters: []
      },

      // System Monitoring Category
      data_quality_check: {
        name: "Data Quality Assessment",
        description: "Check for missing data, outliers, and data consistency",
        category: "System Monitoring",
        query: `
          MATCH (p:Patient {patientId: $patientId})
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: 30})
          WITH p, collect(g) as readings, count(g) as totalReadings
          WITH p, readings, totalReadings,
               [r in readings WHERE r.glucose > 400 OR r.glucose < 20] as outliers,
               [r in readings WHERE r.glucose IS NULL] as nullReadings
          RETURN
            p.patientId as patientId,
            p.name as patientName,
            totalReadings,
            size(outliers) as outlierCount,
            size(nullReadings) as nullCount,
            CASE
              WHEN totalReadings = 0 THEN 'No data'
              WHEN totalReadings < 100 THEN 'Insufficient data'
              WHEN size(outliers) > (totalReadings * 0.05) THEN 'High outlier rate'
              ELSE 'Good data quality'
            END as dataQualityStatus
        `,
        parameters: ['patientId']
      },
      system_overview: {
        name: "System Data Overview",
        description: "Get high-level system statistics and data health metrics",
        category: "System Monitoring",
        query: `
          MATCH (p:Patient)
          OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
          WHERE g.timestamp >= datetime() - duration({days: 30})
          WITH count(DISTINCT p) as totalPatients,
               count(g) as totalReadings,
               avg(g.glucose) as systemAvgGlucose,
               min(g.timestamp) as oldestReading,
               max(g.timestamp) as newestReading
          RETURN
            totalPatients,
            totalReadings,
            systemAvgGlucose,
            oldestReading,
            newestReading,
            (totalReadings / totalPatients) as avgReadingsPerPatient
        `,
        parameters: []
      }
    };
  }
}

// Create singleton instance
const neo4jService = new Neo4jService();

export default neo4jService;
