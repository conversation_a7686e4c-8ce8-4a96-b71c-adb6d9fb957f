import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

async function checkAllData() {
  let driver = null;

  try {
    console.log('🔍 Checking all data in database...');

    driver = neo4j.driver(
      process.env.VITE_NEO4J_URI,
      neo4j.auth.basic(process.env.VITE_NEO4J_USERNAME, process.env.VITE_NEO4J_PASSWORD)
    );

    const session = driver.session();

    try {
      // Check all patient IDs
      console.log('\n👥 All Patients:');
      const allPatientsResult = await session.run(`
        MATCH (p:Patient)
        RETURN p.patientId as patientId,
               p.name as name,
               labels(p) as labels
        ORDER BY p.patientId
      `);

      allPatientsResult.records.forEach(record => {
        const patientId = record.get('patientId');
        const name = record.get('name');
        const labels = record.get('labels');
        console.log(`   ${patientId}: ${name} (${labels.join(', ')})`);
      });

      // Check if there are ECG readings not connected
      console.log('\n💓 ECG Readings:');
      const ecgResult = await session.run(`
        MATCH (e:ECGReading)
        OPTIONAL MATCH (p:Patient)-[:HAD_ECG]->(e)
        RETURN e.readingId as readingId,
               e.timestamp as timestamp,
               p.patientId as connectedPatient
        ORDER BY e.timestamp
        LIMIT 10
      `);

      if (ecgResult.records.length > 0) {
        ecgResult.records.forEach(record => {
          const readingId = record.get('readingId');
          const timestamp = new Date(record.get('timestamp')).toLocaleString();
          const connectedPatient = record.get('connectedPatient') || 'NOT CONNECTED';
          console.log(`   ${readingId}: ${timestamp} -> ${connectedPatient}`);
        });
      } else {
        console.log('   No ECG readings found');
      }

      // Check glucose readings
      console.log('\n🍯 Glucose Readings:');
      const glucoseResult = await session.run(`
        MATCH (g:GlucoseReading)
        OPTIONAL MATCH (p:Patient)-[:HAD_READING]->(g)
        RETURN g.readingId as readingId,
               g.timestamp as timestamp,
               g.value as value,
               p.patientId as connectedPatient
        ORDER BY g.timestamp
        LIMIT 10
      `);

      if (glucoseResult.records.length > 0) {
        glucoseResult.records.forEach(record => {
          const readingId = record.get('readingId');
          const timestamp = new Date(record.get('timestamp')).toLocaleString();
          const value = record.get('value');
          const connectedPatient = record.get('connectedPatient') || 'NOT CONNECTED';
          console.log(`   ${readingId}: ${timestamp}, ${value} mg/dL -> ${connectedPatient}`);
        });
      } else {
        console.log('   No glucose readings found');
      }

      // Show total counts
      console.log('\n📊 Total Counts:');
      const countsResult = await session.run(`
        MATCH (p:Patient) WITH count(p) as patientCount
        MATCH (e:ECGReading) WITH patientCount, count(e) as ecgCount
        MATCH (g:GlucoseReading) WITH patientCount, ecgCount, count(g) as glucoseCount
        MATCH (f:ECGFeatures) WITH patientCount, ecgCount, glucoseCount, count(f) as featuresCount
        RETURN patientCount, ecgCount, glucoseCount, featuresCount
      `);

      if (countsResult.records.length > 0) {
        const record = countsResult.records[0];
        console.log(`   Patients: ${record.get('patientCount').toNumber()}`);
        console.log(`   ECG Readings: ${record.get('ecgCount').toNumber()}`);
        console.log(`   Glucose Readings: ${record.get('glucoseCount').toNumber()}`);
        console.log(`   ECG Features: ${record.get('featuresCount').toNumber()}`);
      }

    } finally {
      await session.close();
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (driver) {
      await driver.close();
    }
  }
}

checkAllData();
