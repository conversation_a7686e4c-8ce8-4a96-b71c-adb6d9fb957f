import {
    BarElement,
    CategoryScale,
    Chart as ChartJS,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    RadialLinearScale,
    Title,
    Tooltip,
} from 'chart.js';
import { useEffect, useState } from 'react';
import { Bar, Line } from 'react-chartjs-2';
import './PromptPerformanceAnalytics.css';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    RadialLinearScale
);

const PromptPerformanceAnalytics = ({ testResults = [], patientProfiles = [] }) => {
    const [analyticsData, setAnalyticsData] = useState(null);
    const [selectedTimeframe, setSelectedTimeframe] = useState('7days');
    const [selectedMetric, setSelectedMetric] = useState('effectiveness');
    const [comparisonView, setComparisonView] = useState('techniques');
    const [isLoading, setIsLoading] = useState(false);

    const timeframes = [
        { value: '7days', label: 'Last 7 days' },
        { value: '30days', label: 'Last 30 days' },
        { value: '90days', label: 'Last 90 days' },
        { value: 'all', label: 'All time' }
    ];

    const metrics = [
        { value: 'effectiveness', label: 'Effectiveness Score', color: '#007bff' },
        { value: 'readability', label: 'Readability Score', color: '#28a745' },
        { value: 'actionability', label: 'Actionability Score', color: '#ffc107' },
        { value: 'composite', label: 'Composite Score', color: '#6f42c1' }
    ];

    const techniques = [
        { id: 'structured', name: 'Structured', color: '#007bff', icon: '📋' },
        { id: 'conversational', name: 'Conversational', color: '#28a745', icon: '💬' },
        { id: 'motivational', name: 'Motivational', color: '#ffc107', icon: '🚀' },
        { id: 'clinical', name: 'Clinical', color: '#6c757d', icon: '🏥' }
    ];

    useEffect(() => {
        processAnalyticsData();
    }, [testResults, selectedTimeframe, selectedMetric]);

    const processAnalyticsData = () => {
        if (!testResults || testResults.length === 0) return;

        setIsLoading(true);

        try {
            const filteredResults = filterByTimeframe(testResults, selectedTimeframe);

            const analytics = {
                performanceOverTime: generatePerformanceOverTime(filteredResults),
                techniqueComparison: generateTechniqueComparison(filteredResults),
                patientTypeAnalysis: generatePatientTypeAnalysis(filteredResults),
                correlationMatrix: generateCorrelationMatrix(filteredResults),
                summary: generateSummaryStats(filteredResults),
                recommendations: generateOptimizationRecommendations(filteredResults)
            };

            setAnalyticsData(analytics);
        } catch (error) {
            console.error('Error processing analytics data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const filterByTimeframe = (results, timeframe) => {
        if (timeframe === 'all') return results;

        const now = new Date();
        const cutoffDate = new Date();

        switch (timeframe) {
            case '7days':
                cutoffDate.setDate(now.getDate() - 7);
                break;
            case '30days':
                cutoffDate.setDate(now.getDate() - 30);
                break;
            case '90days':
                cutoffDate.setDate(now.getDate() - 90);
                break;
            default:
                return results;
        }

        return results.filter(result => new Date(result.timestamp) >= cutoffDate);
    };

    const generatePerformanceOverTime = (results) => {
        const dailyData = {};

        results.forEach(result => {
            const date = new Date(result.timestamp).toISOString().split('T')[0];
            if (!dailyData[date]) {
                dailyData[date] = { structured: [], conversational: [], motivational: [], clinical: [] };
            }

            result.techniqueResults.forEach(tech => {
                if (dailyData[date][tech.technique]) {
                    dailyData[date][tech.technique].push(tech[selectedMetric] || 0);
                }
            });
        });

        const labels = Object.keys(dailyData).sort();
        const datasets = techniques.map(technique => ({
            label: technique.name,
            data: labels.map(date => {
                const scores = dailyData[date][technique.id];
                return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
            }),
            borderColor: technique.color,
            backgroundColor: technique.color + '20',
            tension: 0.4,
        }));

        return { labels, datasets };
    };

    const generateTechniqueComparison = (results) => {
        const techniqueStats = {};

        techniques.forEach(tech => {
            techniqueStats[tech.id] = {
                effectiveness: [],
                readability: [],
                actionability: [],
                composite: []
            };
        });

        results.forEach(result => {
            result.techniqueResults.forEach(tech => {
                if (techniqueStats[tech.technique]) {
                    techniqueStats[tech.technique].effectiveness.push(tech.effectiveness || 0);
                    techniqueStats[tech.technique].readability.push(tech.readability || 0);
                    techniqueStats[tech.technique].actionability.push(tech.actionability || 0);
                    techniqueStats[tech.technique].composite.push(
                        (tech.effectiveness + tech.readability + tech.actionability) / 3 || 0
                    );
                }
            });
        });

        const labels = techniques.map(t => t.name);
        const datasets = [{
            label: metrics.find(m => m.value === selectedMetric)?.label || 'Score',
            data: techniques.map(tech => {
                const scores = techniqueStats[tech.id][selectedMetric];
                return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
            }),
            backgroundColor: techniques.map(t => t.color + '80'),
            borderColor: techniques.map(t => t.color),
            borderWidth: 2,
        }];

        return { labels, datasets };
    };

    const generatePatientTypeAnalysis = (results) => {
        const patientTypes = {
            'Type 1': { structured: [], conversational: [], motivational: [], clinical: [] },
            'Type 2': { structured: [], conversational: [], motivational: [], clinical: [] },
            'Unknown': { structured: [], conversational: [], motivational: [], clinical: [] }
        };

        results.forEach(result => {
            const diabetesType = result.patientProfile?.demographics?.diabetes_type || 'Unknown';
            const typeKey = diabetesType.includes('1') ? 'Type 1' :
                diabetesType.includes('2') ? 'Type 2' : 'Unknown';

            result.techniqueResults.forEach(tech => {
                if (patientTypes[typeKey][tech.technique]) {
                    patientTypes[typeKey][tech.technique].push(tech[selectedMetric] || 0);
                }
            });
        });

        const labels = Object.keys(patientTypes);
        const datasets = techniques.map(technique => ({
            label: technique.name,
            data: labels.map(type => {
                const scores = patientTypes[type][technique.id];
                return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
            }),
            backgroundColor: technique.color + '80',
        }));

        return { labels, datasets };
    };

    const generateCorrelationMatrix = (results) => {
        // Calculate correlations between different metrics
        const allScores = {
            effectiveness: [],
            readability: [],
            actionability: []
        };

        results.forEach(result => {
            result.techniqueResults.forEach(tech => {
                allScores.effectiveness.push(tech.effectiveness || 0);
                allScores.readability.push(tech.readability || 0);
                allScores.actionability.push(tech.actionability || 0);
            });
        });

        return {
            effectiveness_readability: calculateCorrelation(allScores.effectiveness, allScores.readability),
            effectiveness_actionability: calculateCorrelation(allScores.effectiveness, allScores.actionability),
            readability_actionability: calculateCorrelation(allScores.readability, allScores.actionability)
        };
    };

    const generateSummaryStats = (results) => {
        const totalTests = results.length;
        const bestTechniques = results.map(r => r.bestTechnique?.technique).filter(Boolean);
        const techniqueWins = {};

        techniques.forEach(t => techniqueWins[t.id] = 0);
        bestTechniques.forEach(tech => {
            if (techniqueWins[tech] !== undefined) techniqueWins[tech]++;
        });

        const winPercentages = {};
        techniques.forEach(t => {
            winPercentages[t.id] = totalTests > 0 ? (techniqueWins[t.id] / totalTests * 100).toFixed(1) : 0;
        });

        return {
            totalTests,
            techniqueWins,
            winPercentages,
            averageScores: calculateAverageScores(results),
            trendAnalysis: analyzeTrends(results)
        };
    };

    const generateOptimizationRecommendations = (results) => {
        const recommendations = [];
        const summary = generateSummaryStats(results);

        // Find best overall technique
        const bestTechnique = techniques.reduce((best, current) => {
            return summary.winPercentages[current.id] > (summary.winPercentages[best.id] || 0) ? current : best;
        }, techniques[0]);

        recommendations.push({
            type: 'primary',
            icon: '🏆',
            title: 'Best Overall Technique',
            description: `${bestTechnique.name} technique performs best overall with ${summary.winPercentages[bestTechnique.id]}% win rate`,
            action: `Consider using ${bestTechnique.name} as default for new patients`
        });

        // Identify improvement opportunities
        const lowestScoring = techniques.reduce((lowest, current) => {
            return summary.averageScores[current.id] < (summary.averageScores[lowest.id] || 100) ? current : lowest;
        }, techniques[0]);

        if (summary.averageScores[lowestScoring.id] < 60) {
            recommendations.push({
                type: 'improvement',
                icon: '📈',
                title: 'Improvement Opportunity',
                description: `${lowestScoring.name} technique has low average score (${summary.averageScores[lowestScoring.id].toFixed(1)})`,
                action: 'Review and optimize this prompting approach'
            });
        }

        // Patient type specific recommendations
        if (results.length > 10) {
            recommendations.push({
                type: 'insight',
                icon: '🎯',
                title: 'Patient Segmentation',
                description: 'Different techniques perform better for different patient types',
                action: 'Implement patient-type-specific technique selection'
            });
        }

        return recommendations;
    };

    const calculateCorrelation = (x, y) => {
        if (x.length !== y.length || x.length < 2) return 0;

        const n = x.length;
        const sumX = x.reduce((a, b) => a + b, 0);
        const sumY = y.reduce((a, b) => a + b, 0);
        const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
        const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
        const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0);

        const numerator = n * sumXY - sumX * sumY;
        const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

        return denominator === 0 ? 0 : numerator / denominator;
    };

    const calculateAverageScores = (results) => {
        const averages = {};
        techniques.forEach(t => averages[t.id] = 0);

        const counts = {};
        techniques.forEach(t => counts[t.id] = 0);

        results.forEach(result => {
            result.techniqueResults.forEach(tech => {
                if (averages[tech.technique] !== undefined) {
                    averages[tech.technique] += tech[selectedMetric] || 0;
                    counts[tech.technique]++;
                }
            });
        });

        techniques.forEach(t => {
            if (counts[t.id] > 0) {
                averages[t.id] = averages[t.id] / counts[t.id];
            }
        });

        return averages;
    };

    const analyzeTrends = (results) => {
        if (results.length < 2) return 'insufficient_data';

        const sortedResults = results.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        const firstHalf = sortedResults.slice(0, Math.floor(sortedResults.length / 2));
        const secondHalf = sortedResults.slice(Math.floor(sortedResults.length / 2));

        const firstAvg = firstHalf.reduce((sum, r) => sum + (r.bestTechnique?.composite_score || 0), 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, r) => sum + (r.bestTechnique?.composite_score || 0), 0) / secondHalf.length;

        if (secondAvg > firstAvg + 5) return 'improving';
        if (firstAvg > secondAvg + 5) return 'declining';
        return 'stable';
    };

    const formatDate = (dateStr) => {
        return new Date(dateStr).toLocaleDateString();
    };

    if (isLoading) {
        return (
            <div className="analytics-loading">
                <div className="loading-spinner"></div>
                <p>Processing analytics data...</p>
            </div>
        );
    }

    if (!testResults || testResults.length === 0) {
        return (
            <div className="analytics-empty">
                <div className="empty-icon">📊</div>
                <h3>Prompt Performance Analytics</h3>
                <p>No test data available yet. Run some prompt comparisons to see analytics.</p>
            </div>
        );
    }

    return (
        <div className="prompt-performance-analytics">
            <div className="analytics-header">
                <h2>📊 Prompt Performance Analytics</h2>
                <p>Analyze and optimize prompting techniques based on test results and patient outcomes.</p>
            </div>

            <div className="analytics-controls">
                <div className="control-group">
                    <label>Timeframe:</label>
                    <select value={selectedTimeframe} onChange={(e) => setSelectedTimeframe(e.target.value)}>
                        {timeframes.map(tf => (
                            <option key={tf.value} value={tf.value}>{tf.label}</option>
                        ))}
                    </select>
                </div>

                <div className="control-group">
                    <label>Metric:</label>
                    <select value={selectedMetric} onChange={(e) => setSelectedMetric(e.target.value)}>
                        {metrics.map(m => (
                            <option key={m.value} value={m.value}>{m.label}</option>
                        ))}
                    </select>
                </div>

                <div className="control-group">
                    <label>View:</label>
                    <select value={comparisonView} onChange={(e) => setComparisonView(e.target.value)}>
                        <option value="techniques">Technique Comparison</option>
                        <option value="patients">Patient Type Analysis</option>
                        <option value="trends">Performance Trends</option>
                    </select>
                </div>
            </div>

            {analyticsData && (
                <>
                    <div className="summary-dashboard">
                        <div className="summary-cards">
                            <div className="summary-card">
                                <div className="card-icon">🧪</div>
                                <div className="card-content">
                                    <div className="card-number">{analyticsData.summary.totalTests}</div>
                                    <div className="card-label">Total Tests</div>
                                </div>
                            </div>

                            {techniques.map(technique => (
                                <div key={technique.id} className="summary-card">
                                    <div className="card-icon" style={{ color: technique.color }}>
                                        {technique.icon}
                                    </div>
                                    <div className="card-content">
                                        <div className="card-number">
                                            {analyticsData.summary.winPercentages[technique.id]}%
                                        </div>
                                        <div className="card-label">{technique.name} Win Rate</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="charts-section">
                        {comparisonView === 'techniques' && analyticsData.techniqueComparison && (
                            <div className="chart-container">
                                <h3>Technique Performance Comparison</h3>
                                <Bar
                                    data={analyticsData.techniqueComparison}
                                    options={{
                                        responsive: true,
                                        plugins: {
                                            legend: { display: false },
                                            title: {
                                                display: true,
                                                text: `${metrics.find(m => m.value === selectedMetric)?.label} by Technique`
                                            }
                                        },
                                        scales: {
                                            y: { beginAtZero: true, max: 100 }
                                        }
                                    }}
                                />
                            </div>
                        )}

                        {comparisonView === 'patients' && analyticsData.patientTypeAnalysis && (
                            <div className="chart-container">
                                <h3>Performance by Patient Type</h3>
                                <Bar
                                    data={analyticsData.patientTypeAnalysis}
                                    options={{
                                        responsive: true,
                                        plugins: {
                                            title: {
                                                display: true,
                                                text: `${metrics.find(m => m.value === selectedMetric)?.label} by Patient Type`
                                            }
                                        },
                                        scales: {
                                            y: { beginAtZero: true, max: 100 }
                                        }
                                    }}
                                />
                            </div>
                        )}

                        {comparisonView === 'trends' && analyticsData.performanceOverTime && (
                            <div className="chart-container">
                                <h3>Performance Trends Over Time</h3>
                                <Line
                                    data={analyticsData.performanceOverTime}
                                    options={{
                                        responsive: true,
                                        plugins: {
                                            title: {
                                                display: true,
                                                text: `${metrics.find(m => m.value === selectedMetric)?.label} Trends`
                                            }
                                        },
                                        scales: {
                                            y: { beginAtZero: true, max: 100 }
                                        }
                                    }}
                                />
                            </div>
                        )}
                    </div>

                    {analyticsData.recommendations && (
                        <div className="recommendations-section">
                            <h3>📋 Optimization Recommendations</h3>
                            <div className="recommendations-grid">
                                {analyticsData.recommendations.map((rec, index) => (
                                    <div key={index} className={`recommendation-card ${rec.type}`}>
                                        <div className="rec-icon">{rec.icon}</div>
                                        <div className="rec-content">
                                            <h4>{rec.title}</h4>
                                            <p>{rec.description}</p>
                                            <div className="rec-action">{rec.action}</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default PromptPerformanceAnalytics;
