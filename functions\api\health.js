/**
 * Health check endpoint for the NLG Research Platform
 * Available at: /api/health
 */
export async function onRequest(context) {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '0.1.0',
    environment: context.env.NODE_ENV || 'production',
    features: {
      neo4j: {
        enabled: !!context.env.NEO4J_PASSWORD,
        configured: !!context.env.NEO4J_URI
      },
      ai: {
        enabled: !!context.env.OPENAI_API_KEY
      }
    }
  };

  return new Response(JSON.stringify(health, null, 2), {
    headers: {
      'content-type': 'application/json',
      'cache-control': 'no-cache'
    },
  });
}
