.prompt-comparison-testing {
  padding: 2rem;
  background: var(--gb-bg1);
  border-radius: 12px;
  box-shadow: var(--gb-shadow-2);
  margin: 1rem 0;
  color: var(--gb-fg);
}

.prompt-comparison-empty {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(235, 219, 178, 0.75);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.testing-header {
  margin-bottom: 2rem;
}

.testing-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--gb-accent2);
  font-size: 1.75rem;
}

.testing-header p {
  margin: 0 0 1.5rem 0;
  color: rgba(235, 219, 178, 0.75);
  font-size: 1rem;
}

.testing-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
  padding: 1rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.analysis-mode-selector,
.category-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.analysis-mode-selector label,
.category-selector label {
  font-weight: 500;
  color: rgba(235, 219, 178, 0.85);
  white-space: nowrap;
}

.analysis-mode-selector select,
.category-selector select {
  padding: 0.5rem;
  border: 1px solid rgba(235, 219, 178, 0.25);
  border-radius: 4px;
  background: rgba(235, 219, 178, 0.05);
  color: var(--gb-fg);
  min-width: 150px;
}

.run-comparison-btn,
.run-category-btn {
  padding: 0.75rem 1.5rem;
  background: transparent;
  color: var(--gb-accent2);
  border: 1px solid var(--gb-accent2);
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}



.run-comparison-btn:hover:not(:disabled),
.run-category-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(250, 189, 47, 0.25);
  background: rgba(250, 189, 47, 0.1);
}

.run-category-btn:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.run-comparison-btn:disabled,
.run-category-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.single-technique-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.single-technique-btn {
  padding: 0.5rem 1rem;
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.single-technique-btn:hover:not(:disabled) {
  background: rgba(235, 219, 178, 0.05);
  transform: translateY(-1px);
}

.single-technique-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.complexity-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(235, 219, 178, 0.2);
  color: var(--gb-fg);
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  font-weight: 600;
}

.technique-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(235, 219, 178, 0.12);
}

.category-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.complexity-indicator {
  font-size: 0.85rem;
  font-weight: 500;
}

.tech-complexity {
  color: rgba(235, 219, 178, 0.75);
  font-size: 0.85rem;
  margin-left: 0.5rem;
}

.technique-performance-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.performance-matrix-item {
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--gb-shadow-1);
}

.matrix-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(235, 219, 178, 0.12);
}

.matrix-technique-icon {
  font-size: 1.5rem;
}

.matrix-technique-name {
  flex: 1;
}

.matrix-technique-name h4 {
  margin: 0;
  color: var(--gb-fg);
  font-size: 1.1rem;
}

.matrix-technique-name p {
  margin: 0.25rem 0 0 0;
  color: rgba(235, 219, 178, 0.75);
  font-size: 0.9rem;
}

.matrix-category {
  background: rgba(235, 219, 178, 0.05);
  color: rgba(235, 219, 178, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
}

.advanced-metrics {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(235, 219, 178, 0.12);
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 4px;
}

.metric-label {
  font-size: 0.85rem;
  color: rgba(235, 219, 178, 0.85);
  font-weight: 500;
}

.metric-value {
  font-size: 0.9rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.reasoning-depth-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.reasoning-icon {
  font-size: 1.2rem;
}

.technique-comparison-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  margin: 2rem 0;
  padding: 1rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 8px;
}

.legend-category {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 20px;
  font-size: 0.9rem;
}

.legend-color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.advanced-analysis-section {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(235, 219, 178, 0.12);
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 1.5rem;
}

.analysis-card {
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--gb-shadow-1);
}

.analysis-card h4 {
  margin: 0 0 1rem 0;
  color: var(--gb-fg);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.complexity-vs-performance-chart {
  margin: 1rem 0;
}

.performance-insight {
  background: rgba(235, 219, 178, 0.05);
  border-left: 4px solid var(--gb-accent2);
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 4px;
}

.performance-insight .insight-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.efficiency-score {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(23, 162, 184, 0.3));
  color: #10b981;
  font-weight: 700;
  font-size: 1.1rem;
  margin: 0 auto;
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .testing-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .analysis-mode-selector,
  .category-selector {
    justify-content: space-between;
  }

  .single-technique-buttons {
    justify-content: center;
  }

  .technique-performance-matrix {
    grid-template-columns: 1fr;
  }

  .analysis-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .complexity-badge {
    position: static;
    margin-left: 0.5rem;
  }

  .technique-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .single-technique-buttons {
    flex-direction: column;
  }
}

.test-progress {
  background: rgba(235, 219, 178, 0.05);
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.85);
}

.error-message {
  color: #ffb4ab;
  background: rgba(220, 53, 69, 0.08);
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 4px;
  padding: 0.75rem;
  margin-top: 0.5rem;
}

.comparison-results {
  margin-top: 2rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(235, 219, 178, 0.12);
}

.results-header h3 {
  margin: 0;
  color: var(--gb-fg);
  font-size: 1.5rem;
}

.best-technique-badge {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
}

.best-technique-badge .score {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  font-size: 0.9rem;
}

.techniques-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.technique-result-card {
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  padding: 1.5rem;
  background: var(--gb-bg1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  transition: all 0.2s ease;
}

.technique-result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.35);
}

.technique-result-card.best {
  border-color: #ffc107;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08), rgba(235, 219, 178, 0.02));
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.2);
}

.technique-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.technique-info {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.technique-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.technique-info h4 {
  margin: 0 0 0.5rem 0;
  color: var(--gb-fg);
  font-size: 1.1rem;
  font-weight: 600;
}

.technique-info p {
  margin: 0;
  color: rgba(235, 219, 178, 0.75);
  font-size: 0.9rem;
  line-height: 1.4;
}

.best-badge {
  background: rgba(245, 158, 11, 0.2);
  color: var(--gb-fg);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.technique-error {
  color: #ffb4ab;
  background: rgba(220, 53, 69, 0.08);
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
}

.technique-scores {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.score-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.score-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.score-bar {
  position: relative;
  height: 24px;
  background: rgba(235, 219, 178, 0.08);
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.score-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 12px;
}

.score-value {
  position: absolute;
  right: 0.75rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--gb-fg);
}

.recommendation-count {
  background: rgba(235, 219, 178, 0.08);
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.85);
  text-align: center;
  font-weight: 500;
}

.sample-recommendations {
  border-top: 1px solid rgba(235, 219, 178, 0.12);
  padding-top: 1rem;
}

.sample-recommendations h5 {
  margin: 0 0 0.75rem 0;
  color: var(--gb-fg);
  font-size: 0.95rem;
}

.sample-recommendation {
  background: rgba(235, 219, 178, 0.05);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.sample-recommendation strong {
  display: block;
  color: var(--gb-fg);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.sample-recommendation p {
  margin: 0;
  color: rgba(235, 219, 178, 0.75);
  font-size: 0.8rem;
  line-height: 1.4;
}

.test-history {
  border-top: 1px solid rgba(235, 219, 178, 0.12);
  padding-top: 2rem;
}

.test-history h3 {
  margin: 0 0 1rem 0;
  color: var(--gb-fg);
  font-size: 1.25rem;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-item {
  background: rgba(235, 219, 178, 0.05);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 8px;
  padding: 1rem;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.history-date {
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.75);
}

.history-best {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.9);
  background: rgba(245, 158, 11, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
}

.history-techniques {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.history-technique-score {
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  cursor: help;
}

/* Responsive design */
@media (max-width: 768px) {
  .prompt-comparison-testing {
    padding: 1rem;
  }

  .testing-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .single-technique-buttons {
    justify-content: center;
  }

  .techniques-grid {
    grid-template-columns: 1fr;
  }

  .results-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .history-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .testing-header h2 {
    font-size: 1.5rem;
  }

  .technique-header {
    flex-direction: column;
    gap: 1rem;
  }

  .technique-info {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .single-technique-buttons {
    flex-direction: column;
  }
}
