/**
 * Prompt Testing Component Styles
 * Two-tone Gruvbox theme for professional prompt testing interface
 */

.prompt-testing {
  padding: 1.5rem;
  min-height: 100vh;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.prompt-testing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, var(--gb-bg1) 0%, rgba(215, 153, 33, 0.1) 100%);
  padding: 1.5rem 2rem;
  border-radius: 12px;
  box-shadow: var(--gb-shadow-2);
  margin-bottom: 2rem;
  border: 1px solid var(--gb-border-strong);
}

.prompt-testing-header h2 {
  margin: 0;
  color: var(--gb-accent2);
  font-size: 1.75rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.header-actions button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--gb-border);
  background: var(--gb-bg1);
  color: var(--gb-fg);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions button:hover {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
  transform: translateY(-1px);
}

.header-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.header-actions select {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  background: var(--gb-bg1);
  color: var(--gb-fg);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.header-actions select:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(250, 189, 47, 0.1);
}

/* Test Setup Panel */
.test-setup-panel {
  background: var(--gb-bg1);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
}

.setup-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.setup-grid>div {
  background: var(--gb-surface-1);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--gb-border);
  transition: all 0.2s ease;
}

.setup-grid>div:hover {
  border-color: var(--gb-accent);
  box-shadow: var(--gb-shadow-1);
}

.setup-grid h3 {
  margin: 0 0 1.25rem 0;
  color: var(--gb-accent2);
  font-size: 1.125rem;
  font-weight: 600;
}

.setup-actions {
  text-align: center;
  padding-top: 1.25rem;
  border-top: 1px solid var(--gb-border);
}

.run-test-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--gb-accent) 0%, var(--gb-accent2) 100%);
  color: var(--gb-bg0);
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--gb-shadow-1);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.run-test-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--gb-shadow-2);
}

.run-test-btn:disabled {
  background: var(--gb-muted);
  cursor: not-allowed;
  opacity: 0.5;
  transform: none;
}

/* Patient Data Editor */
.patient-data-editor {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

.form-row label {
  font-weight: 500;
  color: var(--gb-muted-strong);
  font-size: 0.875rem;
}

.form-row input,
.form-row select,
.form-row textarea {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gb-border);
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-row input:focus,
.form-row select:focus,
.form-row textarea:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(250, 189, 47, 0.1);
}

/* Technique Selector */
.technique-selector {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.technique-group {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--gb-border);
  transition: all 0.2s ease;
}

.technique-group:hover {
  border-color: var(--gb-accent);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.group-header h4 {
  margin: 0;
  color: var(--gb-accent2);
  font-size: 1rem;
  font-weight: 600;
}

.group-actions {
  display: flex;
  gap: 0.5rem;
}

.group-actions button {
  padding: 0.25rem 0.75rem;
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  color: var(--gb-fg);
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.group-actions button:hover {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
}

.technique-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.technique-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.technique-item:hover {
  background: var(--gb-surface-1);
}

.technique-item input[type="checkbox"] {
  accent-color: var(--gb-accent);
}

.technique-name {
  font-size: 0.875rem;
  color: var(--gb-fg);
}

.selected-count {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(250, 189, 47, 0.1);
  border: 1px solid rgba(250, 189, 47, 0.3);
  border-radius: 6px;
  text-align: center;
  font-weight: 500;
  color: var(--gb-accent2);
}

/* Test Running Panel */
.test-running-panel {
  background: var(--gb-bg1);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
}

.running-header {
  text-align: center;
  margin-bottom: 2rem;
}

.running-header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--gb-accent2);
  font-size: 1.5rem;
}

.test-status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(250, 189, 47, 0.1);
  border: 1px solid rgba(250, 189, 47, 0.3);
  border-radius: 6px;
  color: var(--gb-accent2);
  font-weight: 500;
}

.progress-container {
  margin: 2rem 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gb-surface-2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--gb-accent) 0%, var(--gb-accent2) 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: var(--gb-muted-strong);
}

.current-technique {
  padding: 1rem;
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  text-align: center;
}

.current-technique-name {
  font-weight: 600;
  color: var(--gb-accent2);
  margin-bottom: 0.5rem;
}

/* Test Results Panel */
.test-results-panel {
  background: var(--gb-bg1);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gb-border);
}

.results-header h3 {
  margin: 0;
  color: var(--gb-accent2);
  font-size: 1.5rem;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-card {
  background: var(--gb-surface-1);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--gb-border);
  text-align: center;
  transition: all 0.2s ease;
}

.summary-card:hover {
  border-color: var(--gb-accent);
  transform: translateY(-1px);
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gb-accent2);
  margin-bottom: 0.25rem;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--gb-muted);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-item {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.result-item:hover {
  border-color: var(--gb-accent);
  box-shadow: var(--gb-shadow-1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.technique-name-large {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gb-accent2);
}

.scores-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.score-item {
  text-align: center;
  padding: 0.75rem;
  background: var(--gb-bg0);
  border-radius: 6px;
  border: 1px solid var(--gb-border);
}

.score-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gb-accent2);
  margin-bottom: 0.25rem;
}

.score-label {
  font-size: 0.75rem;
  color: var(--gb-muted);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Test Comparison Panel */
.test-comparison-panel {
  background: var(--gb-bg1);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
}

.comparison-controls {
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--gb-surface-1);
  border-radius: 8px;
  border: 1px solid var(--gb-border);
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.comparison-item {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.comparison-item.selected {
  border-color: var(--gb-accent);
  background: rgba(250, 189, 47, 0.05);
}

/* Test Analytics Panel */
.test-analytics-panel {
  background: var(--gb-bg1);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analytics-card {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.analytics-card:hover {
  border-color: var(--gb-accent);
  transform: translateY(-1px);
}

.chart-container {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gb-muted);
}

/* Test History Panel */
.test-history-panel {
  background: var(--gb-bg1);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 600px;
  overflow-y: auto;
}

.history-item {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  border-color: var(--gb-accent);
  background: var(--gb-surface-2);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.history-timestamp {
  font-size: 0.875rem;
  color: var(--gb-muted);
}

.history-techniques {
  font-size: 0.75rem;
  color: var(--gb-muted);
}

/* Notification System */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 400px;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: var(--gb-shadow-2);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.notification.success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #10b981;
}

.notification.warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.notification.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.notification.info {
  background: rgba(250, 189, 47, 0.1);
  border: 1px solid rgba(250, 189, 47, 0.3);
  color: var(--gb-accent2);
}

.notification button {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.notification button:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Modal System */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.modal-content {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-border);
  border-radius: 12px;
  box-shadow: var(--gb-shadow-2);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--gb-border);
}

.modal-title {
  margin: 0;
  color: var(--gb-accent2);
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: var(--gb-muted);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--gb-surface-2);
  color: var(--gb-fg);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid var(--gb-border);
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gb-surface-2);
  border-top: 2px solid var(--gb-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.skeleton {
  background: linear-gradient(90deg,
      rgba(235, 219, 178, 0.1) 25%,
      rgba(235, 219, 178, 0.2) 50%,
      rgba(235, 219, 178, 0.1) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 2s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .prompt-testing {
    padding: 1rem;
  }

  .prompt-testing-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .setup-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .results-summary {
    grid-template-columns: repeat(2, 1fr);
  }

  .scores-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .technique-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .results-summary {
    grid-template-columns: 1fr;
  }

  .scores-grid {
    grid-template-columns: 1fr;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .header-actions button,
  .header-actions select {
    width: 100%;
  }
}

/* Focus and accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--gb-accent2);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .prompt-testing {
    background: white;
    color: black;
  }

  .header-actions {
    display: none;
  }

  .notification {
    display: none;
  }

  .modal-overlay {
    display: none;
  }
}

/* Enhanced Test Results Display Styles */

/* Test Configuration Display */
.test-configuration-display {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.test-configuration-display h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.config-section {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-bg2);
  border-radius: 6px;
  padding: 1rem;
}

.config-section h5 {
  color: var(--gb-accent);
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
}

.config-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.config-item .label {
  color: var(--gb-fg1);
  font-weight: 500;
}

.config-item .value {
  color: var(--gb-fg);
  font-weight: 600;
}

.medication-list,
.challenge-list,
.goal-list,
.technique-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.medication-tag,
.challenge-tag,
.goal-tag,
.technique-tag {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.challenge-tag {
  background: #fb4934;
}

.goal-tag {
  background: #b8bb26;
}

.technique-tag {
  background: #83a598;
}

/* Patient Profile Display */
.patient-profile-display {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.patient-profile-display h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.profile-section {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-bg2);
  border-radius: 6px;
  padding: 1rem;
}

.profile-section h5 {
  color: var(--gb-accent);
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.profile-item .label {
  color: var(--gb-fg1);
  font-weight: 500;
}

.profile-item .value {
  color: var(--gb-fg);
  font-weight: 600;
}

.complexity-indicator {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.complexity-value {
  color: var(--gb-accent);
  font-weight: 600;
  text-transform: capitalize;
}

.complexity-bar {
  width: 100%;
  height: 8px;
  background: var(--gb-bg2);
  border-radius: 4px;
  overflow: hidden;
}

.complexity-fill {
  height: 100%;
  background: linear-gradient(90deg, #b8bb26 0%, #fabd2f 50%, #fb4934 100%);
  transition: width 0.3s ease;
}

/* Enhanced Result Cards */
.detailed-results h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.enhanced-result-card {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.2s ease;
}

.enhanced-result-card:hover {
  border-color: var(--gb-accent);
  box-shadow: var(--gb-shadow-1);
}

.enhanced-result-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  background: var(--gb-bg0);
  border-bottom: 1px solid var(--gb-bg2);
}

.technique-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.technique-info h4 {
  margin: 0;
  color: var(--gb-fg);
  font-size: 1.1rem;
}

.expand-icon {
  color: var(--gb-accent);
  font-weight: bold;
  transition: transform 0.2s ease;
}

.enhanced-result-card .score-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  padding: 1rem 1.5rem;
}

.enhanced-result-card .score-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.enhanced-result-card .score-item .label {
  color: var(--gb-fg1);
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.enhanced-result-card .score-item .value {
  color: var(--gb-accent);
  font-weight: 600;
  font-size: 1.1rem;
}

/* Expanded Content */
.expanded-content {
  border-top: 1px solid var(--gb-bg2);
  padding: 1.5rem;
  background: var(--gb-bg0);
}

/* Iteration Details */
.iteration-details h5 {
  color: var(--gb-accent);
  margin: 0 0 1rem 0;
  font-size: 1rem;
}

.iteration-item {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.iteration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--gb-bg2);
}

.iteration-number {
  color: var(--gb-accent2);
  font-weight: 600;
}

.iteration-timestamp {
  color: var(--gb-fg1);
  font-size: 0.875rem;
}

.iteration-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-item .label {
  color: var(--gb-fg1);
  font-size: 0.875rem;
}

.meta-item .value {
  color: var(--gb-fg);
  font-weight: 500;
  font-size: 0.875rem;
}

/* Recommendation Details */
.recommendation-details h5 {
  color: var(--gb-accent);
  margin: 0 0 1rem 0;
  font-size: 1rem;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 6px;
  padding: 1rem;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.rec-header h6 {
  color: var(--gb-fg);
  margin: 0;
  font-size: 1rem;
  flex: 1;
}

.rec-meta {
  display: flex;
  gap: 0.5rem;
}

.priority,
.difficulty {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.priority.high,
.difficulty.hard {
  background: #fb4934;
  color: white;
}

.priority.medium,
.difficulty.moderate {
  background: #fabd2f;
  color: var(--gb-bg0);
}

.priority.low,
.difficulty.easy {
  background: #b8bb26;
  color: white;
}

.rec-description {
  color: var(--gb-fg1);
  margin: 0.5rem 0;
  line-height: 1.5;
}

.rec-benefit,
.rec-timeline {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: var(--gb-bg0);
  border-radius: 4px;
  font-size: 0.875rem;
}

.rec-actions {
  margin: 0.5rem 0;
}

.rec-actions ul {
  margin: 0.5rem 0 0 1rem;
  padding: 0;
}

.rec-actions li {
  color: var(--gb-fg1);
  margin: 0.25rem 0;
}

/* Raw Response Display */
.raw-response-display {
  margin-top: 1rem;
}

.raw-response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.raw-response-header h5 {
  color: var(--gb-accent);
  margin: 0;
  font-size: 1rem;
}

.toggle-raw-btn {
  background: var(--gb-bg2);
  color: var(--gb-fg);
  border: 1px solid var(--gb-bg2);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.toggle-raw-btn:hover {
  background: var(--gb-accent);
  color: var(--gb-bg0);
}

.raw-response-content {
  background: var(--gb-bg2);
  border: 1px solid var(--gb-bg1);
  border-radius: 6px;
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.raw-text {
  color: var(--gb-fg);
  font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Responsive adjustments for enhanced components */
@media (max-width: 768px) {
  .config-grid {
    grid-template-columns: 1fr;
  }

  .profile-grid {
    grid-template-columns: 1fr;
  }

  .enhanced-result-card .score-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .iteration-meta {
    grid-template-columns: 1fr;
  }

  .rec-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .raw-response-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}
