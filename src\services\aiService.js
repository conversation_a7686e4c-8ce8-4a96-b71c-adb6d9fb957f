// AI Service for Healthcare Recommendations using OpenAI
class AIRecommendationService {
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    this.model = import.meta.env.VITE_OPENAI_MODEL || 'gpt-4o-mini';
    this.maxTokens = parseInt(import.meta.env.VITE_OPENAI_MAX_TOKENS) || 2000;
    this.temperature = parseFloat(import.meta.env.VITE_OPENAI_TEMPERATURE) || 0.3;
    this.enabled = import.meta.env.VITE_AI_ENABLED === 'true';
    this.recommendationLevel = import.meta.env.VITE_AI_RECOMMENDATION_LEVEL || 'detailed';
    this.contextWindow = parseInt(import.meta.env.VITE_AI_CONTEXT_WINDOW) || 8192;
    this.clinicalMode = import.meta.env.VITE_AI_CLINICAL_MODE === 'true';
    this.patientPrivacy = import.meta.env.VITE_AI_PATIENT_PRIVACY || 'strict';

    this.baseURL = 'https://api.openai.com/v1/chat/completions';

    // Global GPT-5 override for all clients (env or localStorage)
    this.forceGPT5 = import.meta.env.VITE_ENABLE_GPT5_FOR_ALL === 'true';
    try {
      if (typeof window !== 'undefined') {
        const lsFlag = localStorage.getItem('ai_force_gpt5');
        if (lsFlag === 'true') this.forceGPT5 = true;
      }
    } catch { }
    if (this.forceGPT5) {
      this.model = 'gpt-5';
    }

    // Cache for AI responses to avoid repeated API calls
    this.responseCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  // Main method to generate AI recommendations for AGP data
  async generateAGPRecommendations(agpData, patientContext = {}) {
    // Use backend API endpoint for AGP recommendations
    try {
      const cacheKey = this.generateCacheKey('agp', agpData, patientContext);
      const cached = this.getCachedResponse(cacheKey);
      if (cached) {
        console.log('🤖 AI Service: Returning cached AGP recommendations');
        return cached;
      }

      const response = await fetch(`${window.location.origin}/api/ai`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'agp-recommendations',
          data: agpData,
          context: patientContext
        })
      });
      if (!response.ok) {
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After') || 300;
          throw new Error(`Rate limit exceeded. Please try again in ${Math.ceil(retryAfter / 60)} minutes.`);
        }
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      const result = await response.json();
      this.cacheResponse(cacheKey, result);
      return result;
    } catch (error) {
      console.error('❌ AI Service: Backend API error:', error);
      return this.generateEnhancedRuleBasedRecommendations(agpData, patientContext);
    }
  }

  // Generate clinical insights from patient query results
  async generateClinicalInsights(queryResults, queryType, patientInfo = {}) {
    // Use backend API endpoint for clinical insights
    try {
      const cacheKey = this.generateCacheKey('clinical', queryResults, queryType, patientInfo);
      const cached = this.getCachedResponse(cacheKey);
      if (cached) return cached;

      const response = await fetch(`${window.location.origin}/api/ai`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'clinical-insights',
          data: queryResults,
          context: { ...patientInfo, queryType }
        })
      });
      if (!response.ok) {
        if (response.status === 429) {
          const retryAfter = response.headers.get('Retry-After') || 300;
          throw new Error(`Rate limit exceeded. Please try again in ${Math.ceil(retryAfter / 60)} minutes.`);
        }
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }
      const result = await response.json();
      this.cacheResponse(cacheKey, result);
      return result;
    } catch (error) {
      console.error('❌ AI Clinical Insights backend API error:', error);
      return this.generateRuleBasedInsights(queryResults, queryType, patientInfo);
    }
  }

  // Generate treatment recommendations based on patient data
  async generateTreatmentRecommendations(patientData, medicalHistory = []) {
    if (!this.enabled || !this.apiKey || this.apiKey === 'your-openai-api-key-here') {
      return this.generateRuleBasedTreatment(patientData, medicalHistory);
    }

    try {
      const prompt = this.buildTreatmentPrompt(patientData, medicalHistory);
      const response = await this.callOpenAI(prompt);
      return this.parseAIResponse(response, 'treatment');

    } catch (error) {
      console.error('❌ AI Treatment Recommendations error:', error);
      return this.generateRuleBasedTreatment(patientData, medicalHistory);
    }
  }

  // Generate personalized health improvement recommendations for patients
  async generatePatientHealthRecommendations(patientData, options = {}) {
    const { promptingTechnique = 'structured', useComparison = false } = options;

    if (!this.enabled || !this.apiKey || this.apiKey === 'your-openai-api-key-here') {
      return this.generateRuleBasedPatientRecommendations(patientData);
    }

    try {
      if (useComparison) {
        return await this.comparePromptingTechniques(patientData);
      } else {
        const prompt = this.buildPatientRecommendationPrompt(patientData, promptingTechnique);
        const response = await this.callOpenAI(prompt);
        return this.parsePatientRecommendations(response, promptingTechnique);
      }
    } catch (error) {
      console.error('❌ AI Patient Recommendations error:', error);
      return this.generateRuleBasedPatientRecommendations(patientData);
    }
  }

  // Compare different prompting techniques for patient recommendations
  async comparePromptingTechniques(patientData) {
    const techniques = [
      'structured', 'conversational', 'motivational', 'clinical',
      'zero_shot', 'one_shot', 'few_shot', 'chain_of_thought',
      'tree_of_thought', 'self_consistency', 'react', 'role_playing'
    ];
    const results = {};

    console.log('🔬 Running comprehensive prompting technique comparison...');

    for (const technique of techniques) {
      try {
        const prompt = this.buildPatientRecommendationPrompt(patientData, technique);
        const response = await this.callOpenAI(prompt);
        const parsed = this.parsePatientRecommendations(response, technique);

        results[technique] = {
          ...parsed,
          promptingTechnique: technique,
          effectiveness_score: this.evaluateRecommendationEffectiveness(parsed, patientData),
          readability_score: this.evaluateReadability(response),
          actionability_score: this.evaluateActionability(parsed.recommendations || []),
          technical_complexity: this.evaluateTechnicalComplexity(technique),
          reasoning_depth: this.evaluateReasoningDepth(response, technique)
        };

        // Add small delay between API calls to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.error(`❌ Error with ${technique} technique:`, error);
        results[technique] = {
          error: error.message,
          promptingTechnique: technique,
          effectiveness_score: 0,
          technical_complexity: 0,
          reasoning_depth: 0
        };
      }
    }

    return {
      comparison_results: results,
      best_technique: this.determineBestTechnique(results),
      technical_analysis: this.analyzeTechnicalPerformance(results),
      analysis_type: 'comprehensive_prompting_comparison',
      generated_at: new Date().toISOString(),
      patient_profile: this.generatePatientProfile(patientData)
    };
  }

  // Build comprehensive AGP analysis prompt
  buildAGPPrompt(agpData, patientContext) {
    const privacyLevel = this.patientPrivacy === 'strict' ? 'anonymized' : 'identified';

    return `You are a clinical diabetes specialist AI assistant analyzing Ambulatory Glucose Profile (AGP) data.
${this.clinicalMode ? 'CLINICAL MODE: Provide medical-grade recommendations following ADA/EASD guidelines.' : ''}

PATIENT CONTEXT (${privacyLevel}):
${this.formatPatientContext(patientContext)}

AGP DATA ANALYSIS:
Time in Range: ${agpData?.timeInRange?.targetRangePercentage || 'N/A'}%
- Very Low (<54 mg/dL): ${agpData?.timeInRange?.ranges?.veryLow?.percentage || 0}%
- Low (54-70 mg/dL): ${agpData?.timeInRange?.ranges?.low?.percentage || 0}%
- Target (70-180 mg/dL): ${agpData?.timeInRange?.ranges?.target?.percentage || 0}%
- High (180-250 mg/dL): ${agpData?.timeInRange?.ranges?.high?.percentage || 0}%
- Very High (>250 mg/dL): ${agpData?.timeInRange?.ranges?.veryHigh?.percentage || 0}%

Glucose Statistics:
- Mean: ${agpData?.summaryStatistics?.mean || 'N/A'} mg/dL
- CV: ${agpData?.summaryStatistics?.coefficientOfVariation || 'N/A'}%
- GMI: ${agpData?.glucoseManagementIndicator?.gmi || 'N/A'}%

Daily Patterns:
${this.formatDailyPatterns(agpData?.dailyPatterns)}

ANALYSIS REQUIREMENTS:
1. Clinical risk assessment (hypoglycemia, variability, A1C)
2. Specific actionable recommendations
3. Priority interventions ranking
4. Patient education focus areas
5. Follow-up timeline suggestions
6. Medication adjustment considerations (if appropriate)

Response format: JSON with structured recommendations, risk levels, and clinical insights.
Detail level: ${this.recommendationLevel}
Max clinical recommendations: ${this.clinicalMode ? '8-10' : '5-6'}`;
  }

  // Build clinical insights prompt for query results
  buildClinicalPrompt(queryResults, queryType, patientInfo) {
    return `Analyze the following healthcare data query results and provide clinical insights.

QUERY TYPE: ${queryType}
PATIENT INFO: ${this.formatPatientContext(patientInfo)}

QUERY RESULTS:
${JSON.stringify(queryResults, null, 2)}

Please provide:
1. Key clinical findings
2. Risk factors identified
3. Recommendations for care
4. Follow-up suggestions
5. Patient education opportunities

Focus on actionable clinical insights that improve patient outcomes.`;
  }

  // Build treatment recommendation prompt
  buildTreatmentPrompt(patientData, medicalHistory) {
    return `As a clinical AI assistant, analyze patient data and medical history to suggest treatment considerations.

PATIENT DATA:
${JSON.stringify(patientData, null, 2)}

MEDICAL HISTORY:
${JSON.stringify(medicalHistory, null, 2)}

Provide treatment considerations focusing on:
1. Current therapy assessment
2. Lifestyle modification recommendations
3. Monitoring frequency suggestions
4. Risk mitigation strategies
5. Coordination of care recommendations

Note: These are considerations for healthcare provider review, not direct medical advice.`;
  }

  // Build patient-focused health improvement prompts using different techniques
  buildPatientRecommendationPrompt(patientData, technique) {
    const baseContext = this.buildPatientContext(patientData);

    switch (technique) {
      // Traditional approaches
      case 'structured':
        return this.buildStructuredPrompt(baseContext, patientData);
      case 'conversational':
        return this.buildConversationalPrompt(baseContext, patientData);
      case 'motivational':
        return this.buildMotivationalPrompt(baseContext, patientData);
      case 'clinical':
        return this.buildClinicalPrompt(baseContext, patientData);

      // Advanced technical prompting techniques
      case 'zero_shot':
        return this.buildZeroShotPrompt(baseContext, patientData);
      case 'one_shot':
        return this.buildOneShotPrompt(baseContext, patientData);
      case 'few_shot':
        return this.buildFewShotPrompt(baseContext, patientData);
      case 'chain_of_thought':
        return this.buildChainOfThoughtPrompt(baseContext, patientData);
      case 'tree_of_thought':
        return this.buildTreeOfThoughtPrompt(baseContext, patientData);
      case 'self_consistency':
        return this.buildSelfConsistencyPrompt(baseContext, patientData);
      case 'react':
        return this.buildReActPrompt(baseContext, patientData);
      case 'role_playing':
        return this.buildRolePlayingPrompt(baseContext, patientData);

      default:
        return this.buildStructuredPrompt(baseContext, patientData);
    }
  }

  buildPatientContext(patientData) {
    const {
      name = 'Patient',
      age,
      gender,
      diabetesType,
      diagnosisDate,
      currentMedications = [],
      recentGlucoseData = {},
      lifestyle = {},
      challenges = [],
      goals = []
    } = patientData;

    return {
      demographics: { name, age, gender },
      medical: { diabetesType, diagnosisDate, currentMedications },
      glucose: recentGlucoseData,
      lifestyle,
      challenges,
      goals
    };
  }

  buildStructuredPrompt(context, patientData) {
    return `You are a healthcare AI assistant providing personalized diabetes management recommendations.

PATIENT PROFILE:
Name: ${context.demographics.name}
Age: ${context.demographics.age}
Gender: ${context.demographics.gender}
Diabetes Type: ${context.medical.diabetesType}
Diagnosis: ${context.medical.diagnosisDate}

CURRENT GLUCOSE MANAGEMENT:
${JSON.stringify(context.glucose, null, 2)}

LIFESTYLE FACTORS:
${JSON.stringify(context.lifestyle, null, 2)}

CURRENT CHALLENGES:
${context.challenges.join(', ')}

PATIENT GOALS:
${context.goals.join(', ')}

Please provide 5-7 specific, actionable health improvement recommendations organized by category:

1. GLUCOSE MANAGEMENT
2. LIFESTYLE MODIFICATIONS
3. MEDICATION OPTIMIZATION
4. MONITORING & TRACKING
5. RISK PREVENTION
6. WELLNESS & SUPPORT

Format each recommendation with:
- Clear action item
- Expected benefit
- Implementation timeline
- Success metrics

Ensure recommendations are personalized to this patient's specific situation and goals.`;
  }

  buildConversationalPrompt(context, patientData) {
    return `Hi! I'm here to help you improve your diabetes management and overall health. Let me share some personalized recommendations based on your current situation.

I can see you're ${context.demographics.age} years old with ${context.medical.diabetesType}, and you've been managing this since ${context.medical.diagnosisDate}. That shows real dedication!

Looking at your recent glucose data and lifestyle, here's what I'd like to suggest to help you feel your best:

${context.challenges.length > 0 ? `I notice you've mentioned challenges with: ${context.challenges.join(', ')}. Let's work on those together.` : ''}

${context.goals.length > 0 ? `Your goals of ${context.goals.join(' and ')} are fantastic - let's create a path to achieve them.` : ''}

Please provide friendly, encouraging recommendations that feel like advice from a supportive health coach. Focus on:

1. Small, manageable changes that build confidence
2. Addressing their specific challenges with empathy
3. Connecting recommendations to their personal goals
4. Using encouraging, non-judgmental language
5. Practical tips they can start today

Make it feel like a conversation with someone who truly cares about their success.`;
  }

  buildMotivationalPrompt(context, patientData) {
    return `You have the power to transform your health! Let's unlock your potential for amazing diabetes management.

🎯 YOUR HEALTH TRANSFORMATION JOURNEY

${context.demographics.name}, you've already taken the most important step - you're here, ready to improve. That's the mindset of a champion!

💪 CURRENT STRENGTHS TO BUILD ON:
- You're actively monitoring your health
- You have clear goals: ${context.goals.join(', ')}
- You're seeking ways to improve

🚀 YOUR PERSONALIZED SUCCESS PLAN:

Please create an inspiring, motivation-focused response that:

1. Celebrates their current efforts and progress
2. Reframes challenges as opportunities for growth
3. Sets exciting, achievable milestones
4. Uses energizing language and success imagery
5. Connects each recommendation to their personal "why"
6. Includes celebration moments and reward systems
7. Emphasizes their control and capability

Make them feel excited about their health journey and confident in their ability to succeed!

Focus on transformation, empowerment, and the amazing life they're building through better health.`;
  }

  // Removed duplicate buildClinicalPrompt (already defined for query results above)

  // Advanced Technical Prompting Techniques

  buildZeroShotPrompt(context, patientData) {
    return `Generate personalized health improvement recommendations for the following diabetes patient without any examples or prior context.

PATIENT DATA:
- Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
- Condition: ${context.medical.diabetesType}
- Current Challenges: ${context.challenges.join(', ')}
- Goals: ${context.goals.join(', ')}
- Recent Glucose Data: ${JSON.stringify(context.glucose)}

Task: Provide 5-7 specific, actionable health recommendations that will help this patient improve their diabetes management and overall health. Each recommendation should include the action, expected benefit, and timeline.

Recommendations:`;
  }

  buildOneShotPrompt(context, patientData) {
    return `Here's an example of a good health recommendation for a diabetes patient:

EXAMPLE:
Patient: 45-year-old with Type 2 diabetes, struggles with post-meal glucose spikes
Recommendation: "Implement a 15-minute walk after each meal"
- Expected Benefit: Reduce post-meal glucose spikes by 20-30mg/dL
- Timeline: Start immediately, see benefits within 1-2 weeks
- Success Metric: Post-meal glucose readings below 180mg/dL

Now, generate similar recommendations for this patient:

PATIENT DATA:
- Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
- Condition: ${context.medical.diabetesType}
- Current Challenges: ${context.challenges.join(', ')}
- Goals: ${context.goals.join(', ')}
- Recent Glucose Data: ${JSON.stringify(context.glucose)}

Your recommendations:`;
  }

  buildFewShotPrompt(context, patientData) {
    return `Here are examples of effective health recommendations for diabetes patients:

EXAMPLE 1:
Patient: 35-year-old Type 1 diabetes, frequent hypoglycemia
Recommendation: "Set up low glucose alerts at 80mg/dL on CGM"
- Benefit: Prevent severe hypoglycemic episodes
- Timeline: Configure today, monitor for 1 week
- Success: Zero episodes below 70mg/dL

EXAMPLE 2:
Patient: 60-year-old Type 2 diabetes, high A1C
Recommendation: "Replace refined carbs with whole grains at dinner"
- Benefit: Improve overnight glucose control
- Timeline: Gradual transition over 2 weeks
- Success: Fasting glucose under 130mg/dL

EXAMPLE 3:
Patient: 28-year-old Type 1 diabetes, dawn phenomenon
Recommendation: "Adjust basal insulin timing with endocrinologist"
- Benefit: Reduce morning glucose elevation
- Timeline: Appointment within 2 weeks
- Success: Morning readings 80-120mg/dL

Now generate similar recommendations for this patient:

PATIENT DATA:
- Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
- Condition: ${context.medical.diabetesType}
- Current Challenges: ${context.challenges.join(', ')}
- Goals: ${context.goals.join(', ')}
- Recent Glucose Data: ${JSON.stringify(context.glucose)}
- Current Medications: ${context.medical.currentMedications.join(', ')}

Your recommendations (following the format above):`;
  }

  buildChainOfThoughtPrompt(context, patientData) {
    return `I need to develop health recommendations for a diabetes patient. Let me think through this step by step.

PATIENT ANALYSIS:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Current Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

Let me analyze this systematically:

Step 1: Assess Current Glucose Control
- What patterns do I see in the glucose data?
- Are there specific times of day with issues?
- What's the current time-in-range status?

Step 2: Identify Priority Areas
- What are the most critical challenges to address first?
- Which goals are most achievable in the short term?
- What safety considerations are paramount?

Step 3: Consider Patient-Specific Factors
- How does age affect recommendation priorities?
- What lifestyle factors should influence my suggestions?
- Are there medication interactions to consider?

Step 4: Develop Targeted Recommendations
- What specific actions address the identified priorities?
- How can I make recommendations actionable and measurable?
- What timeline is realistic for each recommendation?

Step 5: Prioritize and Sequence
- Which recommendations should be implemented first?
- How do recommendations build on each other?
- What support systems are needed?

Based on this analysis, here are my recommendations:`;
  }

  buildTreeOfThoughtPrompt(context, patientData) {
    return `I need to generate health recommendations for this diabetes patient. Let me explore multiple reasoning paths:

PATIENT DATA:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

REASONING TREE:

Branch A: Focus on Glucose Control
├─ A1: Medication optimization approach
│  ├─ A1a: Timing adjustments
│  └─ A1b: Dosage review with provider
├─ A2: Monitoring enhancement approach
│  ├─ A2a: CGM utilization improvement
│  └─ A2b: Testing frequency optimization
└─ A3: Technology integration approach
   ├─ A3a: Apps for tracking
   └─ A3b: Alert systems

Branch B: Focus on Lifestyle Modification
├─ B1: Dietary intervention approach
│  ├─ B1a: Carb counting refinement
│  └─ B1b: Meal timing optimization
├─ B2: Exercise integration approach
│  ├─ B2a: Structured routine development
│  └─ B2b: Activity timing for glucose impact
└─ B3: Stress management approach
   ├─ B3a: Mindfulness techniques
   └─ B3b: Sleep quality improvement

Branch C: Focus on Education and Support
├─ C1: Knowledge enhancement approach
│  ├─ C1a: Diabetes self-management education
│  └─ C1b: Latest research and tools
├─ C2: Support system building approach
│  ├─ C2a: Healthcare team coordination
│  └─ C2b: Family/peer support engagement
└─ C3: Emergency preparedness approach
   ├─ C3a: Hypoglycemia protocols
   └─ C3b: Sick day management

Evaluating paths and selecting optimal combinations:

Most promising path combination: ${this.selectOptimalPaths(context)}

Final integrated recommendations:`;
  }

  buildSelfConsistencyPrompt(context, patientData) {
    return `I will generate health recommendations for this diabetes patient from multiple perspectives to ensure consistency and reliability.

PATIENT DATA:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

PERSPECTIVE 1 - Clinical Safety Focus:
From a safety-first clinical perspective, what are the most important recommendations?

PERSPECTIVE 2 - Patient Experience Focus:
From a patient satisfaction and quality of life perspective, what recommendations would be most valuable?

PERSPECTIVE 3 - Evidence-Based Medicine Focus:
From a research and evidence-based medicine perspective, what recommendations have the strongest scientific support?

PERSPECTIVE 4 - Practical Implementation Focus:
From a real-world implementation perspective, what recommendations are most feasible and sustainable?

PERSPECTIVE 5 - Preventive Care Focus:
From a long-term complication prevention perspective, what recommendations provide the best risk reduction?

Now, let me synthesize these perspectives and identify the recommendations that appear consistently across multiple viewpoints:

CONSISTENT RECOMMENDATIONS (appearing in 3+ perspectives):

SYNTHESIZED FINAL RECOMMENDATIONS:
Combining the most consistent and complementary recommendations from all perspectives:`;
  }

  buildReActPrompt(context, patientData) {
    return `I am a diabetes care specialist AI. I will use Reasoning and Acting (ReAct) to develop personalized health recommendations.

PATIENT DATA:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

THOUGHT 1: I need to analyze the patient's current glucose control status and identify the most critical areas for improvement.

ACTION 1: Analyze glucose data patterns
OBSERVATION 1: ${this.analyzeGlucosePatterns(context.glucose)}

THOUGHT 2: Based on the glucose patterns, I should consider what specific interventions would have the most impact.

ACTION 2: Evaluate intervention priorities
OBSERVATION 2: Priority areas identified: ${this.identifyPriorities(context)}

THOUGHT 3: I need to consider patient-specific factors that might affect recommendation acceptance and implementation.

ACTION 3: Assess patient readiness and barriers
OBSERVATION 3: Key considerations: ${this.assessReadiness(context)}

THOUGHT 4: Now I should generate specific, actionable recommendations that address the priorities while considering patient factors.

ACTION 4: Generate targeted recommendations
OBSERVATION 4: Recommendations formulated with specific actions, timelines, and success metrics.

THOUGHT 5: I should verify these recommendations align with clinical guidelines and patient goals.

ACTION 5: Validate recommendations against standards
OBSERVATION 5: All recommendations reviewed for safety, efficacy, and goal alignment.

FINAL RECOMMENDATIONS:`;
  }

  buildRolePlayingPrompt(context, patientData) {
    return `I am Dr. Sarah Mitchell, an experienced endocrinologist with 15 years of specialized diabetes care. I'm known for my patient-centered approach and ability to create practical, achievable care plans.

*Reviewing patient chart*

PATIENT CONSULTATION NOTES:
Name: ${context.demographics.name}
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Diagnosis: ${context.medical.diabetesType} (since ${context.medical.diagnosisDate})
Current Medications: ${context.medical.currentMedications.join(', ')}

*Looking at recent glucose data and patient concerns*

Patient's expressed challenges: "${context.challenges.join(', ')}"
Patient's goals: "${context.goals.join(', ')}"

Recent glucose trends: ${JSON.stringify(context.glucose)}

*Thinking like an experienced endocrinologist*

As I review this case, I see several opportunities for improvement. Based on my clinical experience with similar patients, here's what I would recommend:

*Speaking directly to the patient*

"I've reviewed your recent glucose data and listened to your concerns. Here's my personalized care plan for you:

First, let me acknowledge what you're doing well...

Now, here are the specific areas where I think we can make meaningful improvements:

1. IMMEDIATE PRIORITIES (Next 1-2 weeks):

2. SHORT-TERM GOALS (Next 1-3 months):

3. LONG-TERM STRATEGY (3+ months):

*Drawing from clinical experience*

I've seen excellent results with patients in similar situations when they focus on these specific interventions. Let me explain why each recommendation is important for your particular case..."

MY CLINICAL RECOMMENDATIONS:`;
  }

  // Call OpenAI API with error handling and retry logic
  async callOpenAI(prompt, retries = 2) {
    let modelToUse = this.model;
    const requestBodyBase = {
      messages: [
        {
          role: "system",
          content: "You are a specialized healthcare AI assistant focused on diabetes care and glucose monitoring. Provide evidence-based, clinical recommendations while emphasizing that all suggestions should be reviewed with healthcare providers."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      top_p: 0.9,
      frequency_penalty: 0.1,
      presence_penalty: 0.1
    };

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await fetch(this.baseURL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({ model: modelToUse, ...requestBodyBase })
        });

        if (!response.ok) {
          const error = await response.json().catch(() => ({}));
          const message = typeof error === 'object' ? JSON.stringify(error) : String(error);

          // If model not found or invalid, try graceful fallback models
          const isModelError =
            response.status === 404 ||
            response.status === 400 ||
            (message && /model|not\s*found|does\s*not\s*exist|unsupported/i.test(message));

          if (isModelError) {
            const fallbacks = ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo'];
            const next = fallbacks.find((m) => m !== modelToUse);
            if (next) {
              console.warn(`⚠️ Model '${modelToUse}' unavailable. Falling back to '${next}'.`);
              modelToUse = next;
              this.model = next; // persist for subsequent calls
              // Retry immediately with fallback without counting towards retries loop
              attempt--;
              await new Promise((r) => setTimeout(r, 200));
              continue;
            }
          }

          throw new Error(`OpenAI API error: ${response.status} - ${message}`);
        }

        const data = await response.json();
        return data.choices[0]?.message?.content || '';

      } catch (error) {
        console.error(`❌ OpenAI API attempt ${attempt + 1} failed:`, error);
        if (attempt === retries) throw error;

        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  // Parse AI response into structured format
  parseAIResponse(response, type) {
    try {
      // Try to extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // If no JSON, parse as structured text
      return this.parseStructuredResponse(response, type);

    } catch (error) {
      console.error('❌ AI Response parsing error:', error);
      return this.parseStructuredResponse(response, type);
    }
  }

  // Parse patient-specific recommendations
  parsePatientRecommendations(response, technique) {
    const recommendations = this.extractRecommendationsFromResponse(response);

    return {
      recommendations: recommendations,
      prompting_technique: technique,
      response_style: this.analyzeResponseStyle(response, technique),
      patient_focus_score: this.evaluatePatientFocus(response),
      actionability_score: this.evaluateActionability(recommendations),
      generated_at: new Date().toISOString(),
      model_used: this.model,
      raw_response: response
    };
  }

  // Extract actionable recommendations from AI response
  extractRecommendationsFromResponse(response) {
    const recommendations = [];
    const lines = response.split('\n').filter(line => line.trim());

    let currentCategory = 'General';
    let currentRecommendation = null;

    for (const line of lines) {
      const trimmed = line.trim();

      // Detect category headers
      if (this.isCategoryHeader(trimmed)) {
        currentCategory = this.extractCategoryName(trimmed);
        continue;
      }

      // Detect recommendation items
      if (this.isRecommendationItem(trimmed)) {
        if (currentRecommendation) {
          recommendations.push(currentRecommendation);
        }

        currentRecommendation = {
          category: currentCategory,
          title: this.extractRecommendationTitle(trimmed),
          description: this.cleanMessage(trimmed),
          action_items: [],
          expected_benefit: '',
          timeline: '',
          success_metrics: [],
          priority: this.extractPriority(trimmed),
          difficulty: this.assessDifficulty(trimmed)
        };
      } else if (currentRecommendation && trimmed) {
        // Continue building current recommendation
        if (trimmed.toLowerCase().includes('benefit:') || trimmed.toLowerCase().includes('expected:')) {
          currentRecommendation.expected_benefit = trimmed.replace(/^.*?benefit:?/i, '').trim();
        } else if (trimmed.toLowerCase().includes('timeline:') || trimmed.toLowerCase().includes('timeframe:')) {
          currentRecommendation.timeline = trimmed.replace(/^.*?timeline:?/i, '').trim();
        } else if (trimmed.toLowerCase().includes('success:') || trimmed.toLowerCase().includes('metric:')) {
          currentRecommendation.success_metrics.push(trimmed.replace(/^.*?success:?/i, '').replace(/^.*?metric:?/i, '').trim());
        } else if (trimmed.startsWith('-') || trimmed.startsWith('•')) {
          currentRecommendation.action_items.push(trimmed.replace(/^[-•]\s*/, ''));
        } else {
          currentRecommendation.description += ' ' + trimmed;
        }
      }
    }

    if (currentRecommendation) {
      recommendations.push(currentRecommendation);
    }

    return recommendations;
  }

  // Evaluation methods for prompting techniques
  evaluateRecommendationEffectiveness(parsed, patientData) {
    let score = 0;
    const recommendations = parsed.recommendations || [];

    // Score based on number of actionable recommendations (max 30 points)
    score += Math.min(recommendations.length * 5, 30);

    // Score based on personalization (max 25 points)
    score += this.evaluatePersonalization(parsed.raw_response, patientData);

    // Score based on goal alignment (max 25 points)
    score += this.evaluateGoalAlignment(recommendations, patientData.goals || []);

    // Score based on challenge addressing (max 20 points)
    score += this.evaluateChallengeAddressing(recommendations, patientData.challenges || []);

    return Math.min(score, 100);
  }

  evaluateReadability(response) {
    // Simple readability assessment based on sentence length, complexity, etc.
    const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.split(' ').length, 0) / sentences.length;

    // Score inversely related to complexity (simpler = higher score)
    const complexityScore = Math.max(0, 100 - (avgSentenceLength - 15) * 2);

    // Bonus for patient-friendly language
    const friendlyWords = ['you', 'your', 'can', 'will', 'help', 'easy', 'simple', 'start'];
    const friendlyScore = friendlyWords.filter(word =>
      response.toLowerCase().includes(word)
    ).length * 5;

    return Math.min(complexityScore + friendlyScore, 100);
  }

  evaluateActionability(recommendations) {
    if (!recommendations || recommendations.length === 0) return 0;

    let score = 0;
    recommendations.forEach(rec => {
      // Has specific action items
      if (rec.action_items && rec.action_items.length > 0) score += 15;

      // Has timeline
      if (rec.timeline && rec.timeline.trim().length > 0) score += 10;

      // Has success metrics
      if (rec.success_metrics && rec.success_metrics.length > 0) score += 10;

      // Contains action verbs
      const actionVerbs = ['start', 'begin', 'track', 'monitor', 'reduce', 'increase', 'schedule', 'contact'];
      if (actionVerbs.some(verb => rec.description.toLowerCase().includes(verb))) score += 5;
    });

    return Math.min(score / recommendations.length, 100);
  }

  evaluatePatientFocus(response) {
    const patientFocusIndicators = [
      'you', 'your', 'yourself', 'personal', 'personalized',
      'specific to you', 'based on your', 'for you', 'your goals'
    ];

    let score = 0;
    patientFocusIndicators.forEach(indicator => {
      const regex = new RegExp(indicator, 'gi');
      const matches = (response.match(regex) || []).length;
      score += matches * 5;
    });

    return Math.min(score, 100);
  }

  // Removed duplicate determineBestTechnique (kept enhanced version below)

  generatePatientProfile(patientData) {
    return {
      demographics: {
        age_range: this.getAgeRange(patientData.age),
        gender: patientData.gender,
        diabetes_type: patientData.diabetesType
      },
      engagement_factors: {
        has_goals: (patientData.goals || []).length > 0,
        has_challenges: (patientData.challenges || []).length > 0,
        active_monitoring: !!patientData.recentGlucoseData
      },
      complexity_level: this.assessPatientComplexity(patientData)
    };
  }

  // Helper methods for parsing and evaluation
  isCategoryHeader(line) {
    return /^\d+\.\s*[A-Z\s&]+$/.test(line) ||
      /^[A-Z\s&]+:?\s*$/.test(line) ||
      line.includes('MANAGEMENT') || line.includes('RECOMMENDATIONS');
  }

  extractCategoryName(line) {
    return line.replace(/^\d+\.\s*/, '').replace(/:$/, '').trim();
  }

  isRecommendationItem(line) {
    return line.match(/^\d+\./) || line.match(/^[-•]\s/) || line.includes('Recommendation:');
  }

  extractRecommendationTitle(line) {
    return line.replace(/^\d+\.\s*/, '').replace(/^[-•]\s*/, '').split('.')[0].trim();
  }

  assessDifficulty(text) {
    const easyIndicators = ['start', 'begin', 'simple', 'easy', 'daily'];
    const hardIndicators = ['major', 'significant', 'comprehensive', 'complex'];

    const easyCount = easyIndicators.filter(w => text.toLowerCase().includes(w)).length;
    const hardCount = hardIndicators.filter(w => text.toLowerCase().includes(w)).length;

    if (easyCount > hardCount) return 'easy';
    if (hardCount > easyCount) return 'challenging';
    return 'moderate';
  }

  evaluatePersonalization(response, patientData) {
    let score = 0;

    // Check if patient's specific data is referenced
    if (patientData.age && response.includes(patientData.age.toString())) score += 5;
    if (patientData.diabetesType && response.includes(patientData.diabetesType)) score += 5;
    if (patientData.name && response.includes(patientData.name)) score += 5;

    // Check for personalized language
    const personalizedPhrases = ['based on your', 'given your', 'for your specific', 'considering your'];
    personalizedPhrases.forEach(phrase => {
      if (response.toLowerCase().includes(phrase)) score += 3;
    });

    return Math.min(score, 25);
  }

  evaluateGoalAlignment(recommendations, goals) {
    if (goals.length === 0) return 20; // Default score if no goals specified

    let alignmentScore = 0;
    goals.forEach(goal => {
      const goalKeywords = goal.toLowerCase().split(' ');
      recommendations.forEach(rec => {
        const recText = (rec.description + ' ' + rec.title).toLowerCase();
        if (goalKeywords.some(keyword => recText.includes(keyword))) {
          alignmentScore += 5;
        }
      });
    });

    return Math.min(alignmentScore, 25);
  }

  evaluateChallengeAddressing(recommendations, challenges) {
    if (challenges.length === 0) return 20; // Default score if no challenges specified

    let addressingScore = 0;
    challenges.forEach(challenge => {
      const challengeKeywords = challenge.toLowerCase().split(' ');
      recommendations.forEach(rec => {
        const recText = (rec.description + ' ' + rec.title).toLowerCase();
        if (challengeKeywords.some(keyword => recText.includes(keyword))) {
          addressingScore += 5;
        }
      });
    });

    return Math.min(addressingScore, 20);
  }

  assessPatientComplexity(patientData) {
    let complexity = 0;

    // Age factor
    if (patientData.age > 65) complexity += 1;
    if (patientData.age < 30) complexity += 1;

    // Multiple medications
    if ((patientData.currentMedications || []).length > 3) complexity += 1;

    // Multiple challenges
    if ((patientData.challenges || []).length > 2) complexity += 1;

    // Poor glucose control
    if (patientData.recentGlucoseData?.averageGlucose > 180) complexity += 1;

    if (complexity <= 1) return 'low';
    if (complexity <= 3) return 'moderate';
    return 'high';
  }

  analyzeResponseStyle(response, technique) {
    const styles = {
      structured: { formal: true, organized: true, clinical: false },
      conversational: { formal: false, organized: false, clinical: false },
      motivational: { formal: false, organized: false, clinical: false, inspirational: true },
      clinical: { formal: true, organized: true, clinical: true },
      zero_shot: { formal: true, direct: true, minimal_context: true },
      one_shot: { formal: true, example_driven: true, pattern_following: true },
      few_shot: { formal: true, example_rich: true, pattern_learning: true },
      chain_of_thought: { formal: true, reasoning_explicit: true, step_by_step: true },
      tree_of_thought: { formal: true, multi_path: true, comprehensive: true },
      self_consistency: { formal: true, multi_perspective: true, validation_focused: true },
      react: { formal: true, action_oriented: true, iterative: true },
      role_playing: { formal: false, immersive: true, persona_driven: true }
    };

    return styles[technique] || styles.structured;
  }

  // Helper methods for advanced prompting techniques
  selectOptimalPaths(context) {
    // Simulate path selection logic for tree-of-thought
    const priorities = [];
    if (context.glucose?.averageGlucose > 180) priorities.push('A2 (Monitoring enhancement)');
    if (context.challenges.length > 2) priorities.push('B3 (Stress management)');
    if (context.goals.some(g => g.includes('exercise'))) priorities.push('B2 (Exercise integration)');
    return priorities.join(', ') || 'A1 (Medication optimization), B1 (Dietary intervention)';
  }

  analyzeGlucosePatterns(glucoseData) {
    if (!glucoseData || Object.keys(glucoseData).length === 0) {
      return 'Limited glucose data available for pattern analysis';
    }

    const patterns = [];
    if (glucoseData.averageGlucose > 180) patterns.push('elevated average glucose');
    if (glucoseData.timeInRange && glucoseData.timeInRange < 70) patterns.push('suboptimal time in range');
    if (glucoseData.variability === 'high') patterns.push('high glucose variability');

    return patterns.length > 0 ? patterns.join(', ') : 'glucose patterns within acceptable range';
  }

  identifyPriorities(context) {
    const priorities = [];
    if (context.challenges.some(c => c.toLowerCase().includes('glucose'))) {
      priorities.push('glucose control optimization');
    }
    if (context.challenges.some(c => c.toLowerCase().includes('exercise'))) {
      priorities.push('activity integration');
    }
    if (context.goals.some(g => g.toLowerCase().includes('weight'))) {
      priorities.push('weight management support');
    }
    return priorities.length > 0 ? priorities.join(', ') : 'general diabetes management improvement';
  }

  assessReadiness(context) {
    const factors = [];
    if (context.goals.length > 0) factors.push('clear goal orientation');
    if (context.challenges.length > 0) factors.push('awareness of barriers');
    factors.push(`${context.demographics.age < 40 ? 'younger' : 'mature'} patient demographic`);
    return factors.join(', ');
  }

  // Enhanced evaluation methods for technical techniques
  evaluateTechnicalComplexity(technique) {
    const complexityScores = {
      structured: 3, conversational: 2, motivational: 2, clinical: 4,
      zero_shot: 1, one_shot: 2, few_shot: 3,
      chain_of_thought: 4, tree_of_thought: 5, self_consistency: 5,
      react: 4, role_playing: 3
    };
    return complexityScores[technique] || 3;
  }

  evaluateReasoningDepth(response, technique) {
    // Evaluate how deeply the response demonstrates reasoning
    const reasoningIndicators = [
      'because', 'therefore', 'as a result', 'this leads to', 'consequently',
      'step 1', 'step 2', 'first', 'second', 'then', 'next',
      'perspective', 'approach', 'strategy', 'rationale', 'reasoning'
    ];

    let score = 0;
    const lowerResponse = response.toLowerCase();

    reasoningIndicators.forEach(indicator => {
      const matches = (lowerResponse.match(new RegExp(indicator, 'g')) || []).length;
      score += matches * 2;
    });

    // Bonus for techniques that should show deeper reasoning
    const deepReasoningTechniques = ['chain_of_thought', 'tree_of_thought', 'self_consistency', 'react'];
    if (deepReasoningTechniques.includes(technique)) {
      score *= 1.5;
    }

    return Math.min(score, 100);
  }

  analyzeTechnicalPerformance(results) {
    const analysis = {
      best_simple_technique: null,
      best_advanced_technique: null,
      complexity_vs_performance: {},
      reasoning_effectiveness: {},
      technique_categories: {
        traditional: ['structured', 'conversational', 'motivational', 'clinical'],
        few_shot_variants: ['zero_shot', 'one_shot', 'few_shot'],
        reasoning_techniques: ['chain_of_thought', 'tree_of_thought', 'self_consistency'],
        interactive_techniques: ['react', 'role_playing']
      }
    };

    // Find best in each category
    let bestSimpleScore = -1, bestAdvancedScore = -1;

    Object.entries(results).forEach(([technique, result]) => {
      if (result.error) return;

      const compositeScore =
        (result.effectiveness_score * 0.3) +
        (result.readability_score * 0.25) +
        (result.actionability_score * 0.25) +
        (result.reasoning_depth * 0.2);

      const isAdvanced = !analysis.technique_categories.traditional.includes(technique);

      if (isAdvanced && compositeScore > bestAdvancedScore) {
        bestAdvancedScore = compositeScore;
        analysis.best_advanced_technique = { technique, score: compositeScore };
      } else if (!isAdvanced && compositeScore > bestSimpleScore) {
        bestSimpleScore = compositeScore;
        analysis.best_simple_technique = { technique, score: compositeScore };
      }

      // Complexity vs Performance analysis
      analysis.complexity_vs_performance[technique] = {
        complexity: result.technical_complexity || 0,
        performance: compositeScore,
        efficiency: compositeScore / (result.technical_complexity || 1)
      };

      // Reasoning effectiveness
      analysis.reasoning_effectiveness[technique] = {
        reasoning_depth: result.reasoning_depth || 0,
        effectiveness: result.effectiveness_score || 0,
        reasoning_to_effectiveness_ratio: (result.reasoning_depth || 0) / Math.max(result.effectiveness_score || 1, 1)
      };
    });

    return analysis;
  }

  // Enhanced best technique determination with technical considerations
  determineBestTechnique(results) {
    let bestTechnique = null;
    let highestScore = -1;

    Object.entries(results).forEach(([technique, result]) => {
      if (result.error) return;

      // Enhanced composite score including technical factors
      const compositeScore =
        (result.effectiveness_score * 0.25) +
        (result.readability_score * 0.2) +
        (result.actionability_score * 0.25) +
        (result.reasoning_depth * 0.15) +
        (Math.max(0, 100 - (result.technical_complexity * 10)) * 0.15); // Efficiency bonus

      if (compositeScore > highestScore) {
        highestScore = compositeScore;
        bestTechnique = {
          technique,
          composite_score: compositeScore,
          individual_scores: {
            effectiveness: result.effectiveness_score,
            readability: result.readability_score,
            actionability: result.actionability_score,
            reasoning_depth: result.reasoning_depth,
            technical_complexity: result.technical_complexity
          },
          recommendation_count: result.recommendations?.length || 0,
          technique_category: this.getTechniqueCategory(technique)
        };
      }
    });

    return bestTechnique;
  }

  getTechniqueCategory(technique) {
    const categories = {
      traditional: ['structured', 'conversational', 'motivational', 'clinical'],
      few_shot: ['zero_shot', 'one_shot', 'few_shot'],
      reasoning: ['chain_of_thought', 'tree_of_thought', 'self_consistency'],
      interactive: ['react', 'role_playing']
    };

    for (const [category, techniques] of Object.entries(categories)) {
      if (techniques.includes(technique)) return category;
    }
    return 'other';
  }

  // Generate rule-based patient recommendations as fallback
  generateRuleBasedPatientRecommendations(patientData) {
    const recommendations = [];

    // Basic glucose management recommendations
    recommendations.push({
      category: 'Glucose Management',
      title: 'Regular Blood Sugar Monitoring',
      description: 'Check your blood sugar levels at recommended times to understand your patterns',
      action_items: ['Check before meals', 'Check 2 hours after meals', 'Keep a log'],
      expected_benefit: 'Better understanding of glucose patterns',
      timeline: '1-2 weeks to establish routine',
      success_metrics: ['Consistent daily readings', 'Identified patterns'],
      priority: 'high',
      difficulty: 'easy'
    });

    // Lifestyle recommendations
    if (patientData.lifestyle?.exercise === 'sedentary' || !patientData.lifestyle?.exercise) {
      recommendations.push({
        category: 'Lifestyle',
        title: 'Start Regular Physical Activity',
        description: 'Begin with light exercise to improve glucose control and overall health',
        action_items: ['Start with 10-minute walks', 'Gradually increase duration', 'Find activities you enjoy'],
        expected_benefit: 'Improved glucose control and energy levels',
        timeline: '2-4 weeks to see benefits',
        success_metrics: ['Regular exercise routine', 'Improved glucose readings'],
        priority: 'medium',
        difficulty: 'moderate'
      });
    }

    return {
      recommendations,
      prompting_technique: 'rule_based',
      generated_at: new Date().toISOString(),
      analysis_type: 'rule_based_patient_recommendations',
      patient_profile: this.generatePatientProfile(patientData)
    };
  }

  // Parse structured text response
  parseStructuredResponse(response, type) {
    const lines = response.split('\n').filter(line => line.trim());
    const recommendations = [];

    let currentRecommendation = null;

    for (const line of lines) {
      const trimmed = line.trim();

      // Detect recommendation headers
      if (trimmed.match(/^\d+\.|^-|^•|^Priority|^Risk|^Recommendation/i)) {
        if (currentRecommendation) {
          recommendations.push(currentRecommendation);
        }

        currentRecommendation = {
          category: this.extractCategory(trimmed),
          level: this.extractLevel(trimmed),
          message: this.cleanMessage(trimmed),
          recommendation: this.cleanMessage(trimmed),
          priority: this.extractPriority(trimmed),
          evidence_level: 'AI-Generated',
          source: 'OpenAI Clinical Analysis'
        };
      } else if (currentRecommendation && trimmed) {
        // Continue building current recommendation
        currentRecommendation.recommendation += ' ' + trimmed;
        currentRecommendation.message += ' ' + trimmed;
      }
    }

    if (currentRecommendation) {
      recommendations.push(currentRecommendation);
    }

    return {
      recommendations: recommendations,
      analysis_type: type,
      ai_confidence: this.assessConfidence(response),
      generated_at: new Date().toISOString(),
      model_used: this.model,
      clinical_notes: this.extractClinicalNotes(response)
    };
  }

  // Enhanced rule-based recommendations as fallback
  generateEnhancedRuleBasedRecommendations(agpData, patientContext) {
    const recommendations = [];
    const tirStats = agpData?.timeInRange;
    const gmiStats = agpData?.glucoseManagementIndicator;
    const summaryStats = agpData?.summaryStatistics;

    // Enhanced Time in Range Analysis
    if (tirStats) {
      const targetPercentage = parseFloat(tirStats.targetRangePercentage || 0);
      const veryLowPercentage = parseFloat(tirStats.ranges?.veryLow?.percentage || 0);
      const lowPercentage = parseFloat(tirStats.ranges?.low?.percentage || 0);
      const highPercentage = parseFloat(tirStats.ranges?.high?.percentage || 0);
      const veryHighPercentage = parseFloat(tirStats.ranges?.veryHigh?.percentage || 0);

      // Comprehensive TIR recommendations
      if (targetPercentage >= 70) {
        recommendations.push({
          category: 'Time in Range',
          level: 'excellent',
          priority: 'low',
          message: `Outstanding glucose control with ${targetPercentage}% TIR exceeding clinical goals.`,
          recommendation: 'Continue current management strategy. Focus on maintaining consistency. Consider sharing successful strategies with healthcare team for other patients.',
          evidence_level: 'ADA/EASD Guidelines',
          source: 'Enhanced Rule-Based Analysis'
        });
      } else if (targetPercentage >= 50) {
        recommendations.push({
          category: 'Time in Range',
          level: 'good',
          priority: 'medium',
          message: `Good progress with ${targetPercentage}% TIR. Target is 70% for optimal outcomes.`,
          recommendation: 'Review meal timing, exercise patterns, and medication adherence. Consider CGM data review with healthcare provider to identify improvement opportunities.',
          evidence_level: 'Clinical Best Practice',
          source: 'Enhanced Rule-Based Analysis'
        });
      } else {
        recommendations.push({
          category: 'Time in Range',
          level: 'needs_improvement',
          priority: 'high',
          message: `TIR of ${targetPercentage}% indicates significant opportunity for glucose management improvement.`,
          recommendation: 'Urgent consultation with endocrinologist recommended. Comprehensive review of therapy regimen, lifestyle factors, and potential barriers to care needed.',
          evidence_level: 'Clinical Urgency Protocol',
          source: 'Enhanced Rule-Based Analysis'
        });
      }

      // Critical hypoglycemia assessment
      if (veryLowPercentage > 1) {
        recommendations.push({
          category: 'Severe Hypoglycemia Risk',
          level: 'critical',
          priority: 'urgent',
          message: `CRITICAL: ${veryLowPercentage}% time below 54 mg/dL exceeds safety threshold.`,
          recommendation: 'IMMEDIATE healthcare provider contact required. Review hypoglycemia prevention plan, medication dosing, and consider continuous glucose monitoring with alerts.',
          evidence_level: 'Safety Critical',
          source: 'Enhanced Rule-Based Analysis'
        });
      }

      // Comprehensive hypoglycemia analysis
      const totalLowTime = lowPercentage + veryLowPercentage;
      if (totalLowTime > 4) {
        recommendations.push({
          category: 'Hypoglycemia Prevention',
          level: 'caution',
          priority: 'high',
          message: `${totalLowTime.toFixed(1)}% time below range exceeds recommended 4% limit.`,
          recommendation: 'Schedule hypoglycemia education session. Review rapid-acting glucose supplies, emergency protocols, and consider medication timing adjustments.',
          evidence_level: 'Clinical Guidelines',
          source: 'Enhanced Rule-Based Analysis'
        });
      }

      // Hyperglycemia analysis
      const totalHighTime = highPercentage + veryHighPercentage;
      if (totalHighTime > 25) {
        recommendations.push({
          category: 'Hyperglycemia Management',
          level: 'needs_attention',
          priority: 'medium',
          message: `${totalHighTime.toFixed(1)}% time above range may increase complications risk.`,
          recommendation: 'Review post-meal glucose patterns. Consider meal planning consultation, exercise timing optimization, and medication adjustment discussion.',
          evidence_level: 'Diabetes Management Standards',
          source: 'Enhanced Rule-Based Analysis'
        });
      }

      if (veryHighPercentage > 5) {
        recommendations.push({
          category: 'Severe Hyperglycemia',
          level: 'alert',
          priority: 'high',
          message: `${veryHighPercentage}% time above 250 mg/dL requires immediate intervention.`,
          recommendation: 'Urgent endocrinology consultation. Screen for illness, medication adherence issues, and consider insulin adjustment or additional therapy.',
          evidence_level: 'Clinical Emergency Protocol',
          source: 'Enhanced Rule-Based Analysis'
        });
      }
    }

    // Advanced glucose variability analysis
    if (summaryStats) {
      const cv = parseFloat(summaryStats.coefficientOfVariation || 0);
      const sd = parseFloat(summaryStats.standardDeviation || 0);
      const meanGlucose = parseFloat(summaryStats.mean || 0);

      if (cv <= 36) {
        recommendations.push({
          category: 'Glucose Stability',
          level: 'excellent',
          priority: 'low',
          message: `Excellent glucose variability control (CV: ${cv}%) indicates stable management.`,
          recommendation: 'Maintain current approach to glucose management. Document successful strategies for future reference and potential adjustments.',
          evidence_level: 'Variability Standards',
          source: 'Enhanced Rule-Based Analysis'
        });
      } else if (cv <= 50) {
        recommendations.push({
          category: 'Glucose Variability',
          level: 'moderate_concern',
          priority: 'medium',
          message: `Moderate glucose variability (CV: ${cv}%) suggests opportunities for pattern optimization.`,
          recommendation: 'Analyze CGM data for daily patterns. Consider structured meal timing, consistent exercise schedule, and stress management techniques.',
          evidence_level: 'Variability Management',
          source: 'Enhanced Rule-Based Analysis'
        });
      } else {
        recommendations.push({
          category: 'High Glucose Variability',
          level: 'needs_improvement',
          priority: 'high',
          message: `High glucose variability (CV: ${cv}%) indicates unstable glucose control patterns.`,
          recommendation: 'Comprehensive lifestyle and therapy review needed. Consider continuous glucose monitoring, structured diabetes education, and possible medication regimen adjustment.',
          evidence_level: 'Variability Clinical Standards',
          source: 'Enhanced Rule-Based Analysis'
        });
      }

      // Dawn phenomenon analysis
      if (agpData?.dailyPatterns?.dawn) {
        const dawnMean = agpData.dailyPatterns.dawn.mean;
        if (dawnMean > 140) {
          recommendations.push({
            category: 'Dawn Phenomenon',
            level: 'pattern_identified',
            priority: 'medium',
            message: `Dawn phenomenon detected with average morning glucose of ${dawnMean} mg/dL.`,
            recommendation: 'Discuss morning insulin timing or evening medication adjustments with healthcare provider. Consider bedtime snack modification.',
            evidence_level: 'Pattern Analysis',
            source: 'Enhanced Rule-Based Analysis'
          });
        }
      }
    }

    // Enhanced GMI analysis with personalized targets
    if (gmiStats) {
      const gmi = parseFloat(gmiStats.gmi || 0);
      const targetA1C = this.getPersonalizedA1CTarget(patientContext);

      if (gmi <= targetA1C) {
        recommendations.push({
          category: 'A1C Management',
          level: 'excellent',
          priority: 'low',
          message: `Estimated A1C of ${gmi}% meets personalized target of ≤${targetA1C}%.`,
          recommendation: 'Continue excellent diabetes management. Schedule routine follow-up and maintain current lifestyle and medication adherence.',
          evidence_level: 'Personalized Targets',
          source: 'Enhanced Rule-Based Analysis'
        });
      } else if (gmi <= targetA1C + 0.5) {
        recommendations.push({
          category: 'A1C Optimization',
          level: 'near_target',
          priority: 'medium',
          message: `Estimated A1C of ${gmi}% is close to target (${targetA1C}%) with room for improvement.`,
          recommendation: 'Fine-tune current management approach. Review medication timing, meal consistency, and physical activity patterns with healthcare team.',
          evidence_level: 'Target Optimization',
          source: 'Enhanced Rule-Based Analysis'
        });
      } else {
        recommendations.push({
          category: 'A1C Management',
          level: 'needs_improvement',
          priority: 'high',
          message: `Estimated A1C of ${gmi}% exceeds target of ${targetA1C}% by ${(gmi - targetA1C).toFixed(1)}%.`,
          recommendation: 'Comprehensive diabetes management review required. Consider therapy intensification, lifestyle counseling, and structured diabetes self-management education.',
          evidence_level: 'Treatment Guidelines',
          source: 'Enhanced Rule-Based Analysis'
        });
      }
    }

    return {
      recommendations: recommendations,
      analysis_type: 'enhanced_rule_based',
      ai_confidence: 'rule_based_high',
      generated_at: new Date().toISOString(),
      model_used: 'Enhanced Clinical Rules Engine',
      clinical_notes: this.generateClinicalNotes(agpData, patientContext),
      total_recommendations: recommendations.length,
      priority_distribution: this.analyzePriorityDistribution(recommendations)
    };
  }

  // Generate rule-based clinical insights for query results
  generateRuleBasedInsights(queryResults, queryType, patientInfo) {
    const insights = [];

    if (!queryResults || queryResults.length === 0) {
      return {
        insights: [{
          category: 'Data Availability',
          level: 'info',
          message: 'No data available for analysis.',
          recommendation: 'Ensure proper data collection and connectivity.'
        }],
        analysis_type: 'rule_based_fallback',
        generated_at: new Date().toISOString()
      };
    }

    // Analyze based on query type
    switch (queryType.toLowerCase()) {
      case 'glucose_data':
        insights.push(...this.analyzeGlucoseData(queryResults));
        break;
      case 'medication_data':
        insights.push(...this.analyzeMedicationData(queryResults));
        break;
      case 'appointment_data':
        insights.push(...this.analyzeAppointmentData(queryResults));
        break;
      default:
        insights.push(...this.analyzeGeneralHealthData(queryResults));
    }

    return {
      insights: insights,
      analysis_type: 'rule_based_clinical',
      generated_at: new Date().toISOString(),
      total_insights: insights.length
    };
  }

  // Utility methods
  formatPatientContext(context) {
    if (this.patientPrivacy === 'strict') {
      return `Age Range: ${this.getAgeRange(context.age)}, Diabetes Type: ${context.diabetes_type || 'Unknown'}, Duration: ${context.diabetes_duration || 'Unknown'}`;
    }
    return JSON.stringify(context, null, 2);
  }

  formatDailyPatterns(patterns) {
    if (!patterns) return 'No daily pattern data available';

    return Object.entries(patterns).map(([period, data]) => {
      if (!data) return `${period}: No data`;
      return `${period}: Mean ${data.mean} mg/dL, Count ${data.count}`;
    }).join('\n');
  }

  getPersonalizedA1CTarget(patientContext) {
    // Personalized A1C targets based on patient characteristics
    const age = patientContext?.age || 50;
    const complications = patientContext?.complications || false;
    const hypoglycemiaHistory = patientContext?.hypoglycemia_history || false;

    if (age >= 65 || complications || hypoglycemiaHistory) {
      return 8.0; // Less stringent target
    } else if (age < 30 && !complications) {
      return 6.5; // More stringent target
    }
    return 7.0; // Standard target
  }

  generateCacheKey(...args) {
    return btoa(JSON.stringify(args)).slice(0, 32);
  }

  getCachedResponse(key) {
    const cached = this.responseCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.responseCache.delete(key);
    return null;
  }

  cacheResponse(key, data) {
    this.responseCache.set(key, {
      data: data,
      timestamp: Date.now()
    });

    // Cleanup old cache entries
    if (this.responseCache.size > 50) {
      const oldestKey = this.responseCache.keys().next().value;
      this.responseCache.delete(oldestKey);
    }
  }

  // Extract semantic information from AI responses
  extractCategory(text) {
    if (text.toLowerCase().includes('hypoglycemia')) return 'Hypoglycemia Management';
    if (text.toLowerCase().includes('hyperglycemia')) return 'Hyperglycemia Management';
    if (text.toLowerCase().includes('time in range') || text.toLowerCase().includes('tir')) return 'Time in Range';
    if (text.toLowerCase().includes('variability')) return 'Glucose Variability';
    if (text.toLowerCase().includes('a1c') || text.toLowerCase().includes('gmi')) return 'A1C Management';
    return 'General Recommendation';
  }

  extractLevel(text) {
    if (text.toLowerCase().includes('critical') || text.toLowerCase().includes('urgent')) return 'critical';
    if (text.toLowerCase().includes('high') || text.toLowerCase().includes('important')) return 'high';
    if (text.toLowerCase().includes('moderate') || text.toLowerCase().includes('medium')) return 'medium';
    if (text.toLowerCase().includes('excellent') || text.toLowerCase().includes('good')) return 'excellent';
    return 'info';
  }

  extractPriority(text) {
    if (text.toLowerCase().includes('urgent') || text.toLowerCase().includes('immediate')) return 'urgent';
    if (text.toLowerCase().includes('high priority') || text.toLowerCase().includes('important')) return 'high';
    if (text.toLowerCase().includes('medium') || text.toLowerCase().includes('moderate')) return 'medium';
    return 'low';
  }

  cleanMessage(text) {
    return text.replace(/^\d+\.|^-|^•|^Priority:|^Risk:|^Recommendation:/i, '').trim();
  }

  assessConfidence(response) {
    const length = response.length;
    const hasNumbers = /\d/.test(response);
    const hasSpecificTerms = /mg\/dL|percent|target|goal|clinical/i.test(response);

    if (length > 500 && hasNumbers && hasSpecificTerms) return 'high';
    if (length > 200 && (hasNumbers || hasSpecificTerms)) return 'medium';
    return 'low';
  }

  extractClinicalNotes(response) {
    const notes = [];
    if (response.includes('provider')) notes.push('Healthcare provider consultation recommended');
    if (response.includes('medication')) notes.push('Medication review may be needed');
    if (response.includes('lifestyle')) notes.push('Lifestyle modifications suggested');
    return notes;
  }

  generateClinicalNotes(agpData, patientContext) {
    const notes = [];

    if (agpData?.timeInRange?.targetRangePercentage) {
      const tir = parseFloat(agpData.timeInRange.targetRangePercentage);
      if (tir < 50) notes.push('Consider comprehensive diabetes management review');
      if (tir >= 70) notes.push('Patient demonstrates excellent glucose control');
    }

    if (agpData?.summaryStatistics?.coefficientOfVariation) {
      const cv = parseFloat(agpData.summaryStatistics.coefficientOfVariation);
      if (cv > 36) notes.push('High glucose variability warrants pattern analysis');
    }

    return notes;
  }

  analyzePriorityDistribution(recommendations) {
    const distribution = { urgent: 0, high: 0, medium: 0, low: 0 };
    recommendations.forEach(rec => {
      distribution[rec.priority] = (distribution[rec.priority] || 0) + 1;
    });
    return distribution;
  }

  getAgeRange(age) {
    if (!age) return 'Unknown';
    if (age < 18) return 'Pediatric';
    if (age < 30) return 'Young Adult';
    if (age < 50) return 'Adult';
    if (age < 65) return 'Middle Age';
    return 'Older Adult';
  }

  // Analysis methods for different data types
  analyzeGlucoseData(data) {
    const insights = [];
    // Add glucose-specific analysis logic
    insights.push({
      category: 'Glucose Analysis',
      level: 'info',
      message: `Analyzed ${data.length} glucose readings.`,
      recommendation: 'Review glucose patterns with healthcare provider.'
    });
    return insights;
  }

  analyzeMedicationData(data) {
    const insights = [];
    // Add medication-specific analysis logic
    insights.push({
      category: 'Medication Review',
      level: 'info',
      message: `Found ${data.length} medication records.`,
      recommendation: 'Ensure medication adherence and proper timing.'
    });
    return insights;
  }

  analyzeAppointmentData(data) {
    const insights = [];
    // Add appointment-specific analysis logic
    insights.push({
      category: 'Care Coordination',
      level: 'info',
      message: `Review of ${data.length} appointment records.`,
      recommendation: 'Maintain regular healthcare provider visits.'
    });
    return insights;
  }

  analyzeGeneralHealthData(data) {
    const insights = [];
    insights.push({
      category: 'Health Data Review',
      level: 'info',
      message: `General analysis of ${data.length} health records.`,
      recommendation: 'Continue monitoring and data collection.'
    });
    return insights;
  }

  generateRuleBasedTreatment(patientData, medicalHistory) {
    return {
      recommendations: [{
        category: 'Treatment Planning',
        level: 'info',
        message: 'Rule-based treatment analysis completed.',
        recommendation: 'Consult with healthcare provider for personalized treatment plan.'
      }],
      analysis_type: 'rule_based_treatment',
      generated_at: new Date().toISOString()
    };
  }
}

export default AIRecommendationService;
