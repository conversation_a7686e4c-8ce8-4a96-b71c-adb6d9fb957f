import dotenv from 'dotenv';
import fs from 'fs';
import neo4j from 'neo4j-driver';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Neo4j connection - Remote AuraDB
const driver = neo4j.driver(
  process.env.VITE_NEO4J_URI,
  neo4j.auth.basic(
    process.env.VITE_NEO4J_USERNAME,
    process.env.VITE_NEO4J_PASSWORD
  )
);

const DATASET_PATH = path.join(__dirname, '..', 'data', 'd1namo', 'diabetes_subset_ecg_data');

async function testSingleFileImport() {
  console.log('🚀 Testing single D1NAMO ECG file import...');

  const session = driver.session();
  try {
    // Find first ECG file
    const patientDirs = fs.readdirSync(DATASET_PATH).filter(dir => /^\d+$/.test(dir)).sort();
    const firstPatient = patientDirs[0];
    const sensorDataDir = path.join(DATASET_PATH, firstPatient, 'sensor_data');
    const sessionDirs = fs.readdirSync(sensorDataDir);
    const firstSession = sessionDirs[0];
    const sessionPath = path.join(sensorDataDir, firstSession);
    const ecgFiles = fs.readdirSync(sessionPath).filter(file => file.includes('ECG'));
    const firstECGFile = path.join(sessionPath, ecgFiles[0]);

    console.log(`📄 Processing file: ${ecgFiles[0]} from patient ${firstPatient}`);

    // Create patient if not exists
    await session.run(`
      MERGE (p:Patient:D1NAMOSubject {patientId: $patientId})
      SET p.name = $name, p.age = 45, p.gender = 'Unknown', p.condition = 'Type 2 Diabetes'
    `, {
      patientId: `D1NAMO_${firstPatient}`,
      name: `D1NAMO Subject ${firstPatient}`
    });

    console.log(`✅ Created/updated patient D1NAMO_${firstPatient}`);

    // Read first 1000 lines of ECG data for testing
    const content = fs.readFileSync(firstECGFile, 'utf8');
    const lines = content.split('\n');
    const headerLine = lines[0];
    const dataLines = lines.slice(1, 1001); // First 1000 data points

    console.log(`📊 Header: ${headerLine}`);
    console.log(`📊 Processing ${dataLines.length} ECG samples...`);

    // Parse and create ECG reading
    let samplesProcessed = 0;
    const timestamp = new Date();

    // Create one ECG reading node for this batch
    const ecgResult = await session.run(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
      CREATE (e:ECGReading:D1NAMOReading {
        readingId: $readingId,
        timestamp: datetime($timestamp),
        samplingRate: 250,
        duration: $duration,
        signalQuality: 'Good',
        sampleCount: $sampleCount
      })
      CREATE (p)-[:HAD_ECG]->(e)
      RETURN e.readingId as readingId
    `, {
      patientId: `D1NAMO_${firstPatient}`,
      readingId: `ECG_${firstPatient}_${timestamp.getTime()}`,
      timestamp: timestamp.toISOString(),
      duration: dataLines.length / 250, // seconds
      sampleCount: dataLines.length
    });

    const readingId = ecgResult.records[0].get('readingId');
    console.log(`✅ Created ECG reading: ${readingId}`);

    // Create ECG features (analyze the samples)
    let heartRates = [];
    let amplitudes = [];

    for (let i = 0; i < Math.min(dataLines.length, 1000); i++) {
      const line = dataLines[i];
      if (line.trim()) {
        const parts = line.split(',');
        if (parts.length >= 2) {
          const amplitude = parseFloat(parts[1]);
          if (!isNaN(amplitude)) {
            amplitudes.push(amplitude);
            samplesProcessed++;
          }
        }
      }
    }

    // Calculate basic features
    const avgAmplitude = amplitudes.reduce((a, b) => a + b, 0) / amplitudes.length;
    const simulatedHeartRate = 70 + Math.random() * 30; // Simulated for testing

    // Create ECG features
    await session.run(`
      MATCH (e:ECGReading {readingId: $readingId})
      CREATE (f:ECGFeatures {
        heartRate: $heartRate,
        meanAmplitude: $meanAmplitude,
        samplesAnalyzed: $samplesAnalyzed,
        hrv_rmssd: $hrv,
        qtc_interval: $qtc
      })
      CREATE (e)-[:HAS_FEATURES]->(f)
    `, {
      readingId: readingId,
      heartRate: simulatedHeartRate,
      meanAmplitude: avgAmplitude,
      samplesAnalyzed: samplesProcessed,
      hrv: 25 + Math.random() * 20,
      qtc: 400 + Math.random() * 40
    });

    console.log(`✅ Created ECG features - HR: ${simulatedHeartRate.toFixed(1)} bpm`);
    console.log(`📊 Processed ${samplesProcessed} ECG samples`);

    // Verify data was created
    const verifyResult = await session.run(`
      MATCH (p:Patient:D1NAMOSubject)-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
      WHERE p.patientId = $patientId
      RETURN count(e) as ecgCount, count(f) as featuresCount
    `, {
      patientId: `D1NAMO_${firstPatient}`
    });

    const stats = verifyResult.records[0].toObject();
    console.log(`✅ Verification: ${stats.ecgCount} ECG readings, ${stats.featuresCount} feature sets`);

    return true;

  } catch (error) {
    console.error('❌ Import failed:', error);
    return false;
  } finally {
    await session.close();
  }
}

// Run test import
testSingleFileImport()
  .then((success) => {
    console.log(success ? '🎉 Test import completed successfully!' : '💥 Test import failed');
    process.exit(success ? 0 : 1);
  })
  .finally(() => {
    driver.close();
  });
