// Neo4j Database Population Script (CommonJS version)
// Creates 7 patients with comprehensive healthcare data over 1 month

const neo4j = require('neo4j-driver');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }
  }
}

// Load environment variables
loadEnvFile();

// Database connection configuration (environment only)
const uri = process.env.VITE_NEO4J_URI;
const username = process.env.VITE_NEO4J_USERNAME;
const password = process.env.VITE_NEO4J_PASSWORD;

if (!uri || !username || !password) {
  console.error('Missing Neo4j env vars. Please set VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD.');
  process.exit(1);
}

console.log('Using Neo4j URI:', uri);

// Create driver instance
const driver = neo4j.driver(uri, neo4j.auth.basic(username, password));

// Patient data templates
const patients = [
  {
    patientId: 'Patient_1',
    name: 'John Anderson',
    age: 45,
    gender: 'Male',
    condition: 'Type 2 Diabetes',
    diagnosis_date: '2024-01-15',
    insurance: 'Blue Cross',
    phone: '555-0101',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_2',
    name: 'Maria Garcia',
    age: 38,
    gender: 'Female',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2023-08-20',
    insurance: 'Aetna',
    phone: '555-0102',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_3',
    name: 'Robert Chen',
    age: 52,
    gender: 'Male',
    condition: 'Prediabetes',
    diagnosis_date: '2024-03-10',
    insurance: 'Kaiser',
    phone: '555-0103',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_4',
    name: 'Sarah Johnson',
    age: 29,
    gender: 'Female',
    condition: 'Gestational Diabetes',
    diagnosis_date: '2024-06-01',
    insurance: 'Cigna',
    phone: '555-0104',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_5',
    name: 'David Williams',
    age: 67,
    gender: 'Male',
    condition: 'Type 2 Diabetes',
    diagnosis_date: '2022-11-08',
    insurance: 'Medicare',
    phone: '555-0105',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_6',
    name: 'Emily Davis',
    age: 34,
    gender: 'Female',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2023-12-15',
    insurance: 'UnitedHealth',
    phone: '555-0106',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_7',
    name: 'Michael Brown',
    age: 41,
    gender: 'Male',
    condition: 'Type 2 Diabetes',
    diagnosis_date: '2024-02-28',
    insurance: 'Humana',
    phone: '555-0107',
    email: '<EMAIL>'
  }
];

// Generate realistic glucose readings for a patient over 30 days
function generateGlucoseReadings(patientId, patientType = 'type2') {
  const readings = [];
  const now = new Date();
  const startDate = new Date(now);
  startDate.setDate(startDate.getDate() - 30);

  // Patient-specific glucose patterns
  const patterns = {
    type1: { base: 140, variance: 60, dawn: 30, meal: 40, night: -20 },
    type2: { base: 130, variance: 40, dawn: 25, meal: 35, night: -15 },
    prediabetes: { base: 115, variance: 25, dawn: 15, meal: 25, night: -10 },
    gestational: { base: 125, variance: 30, dawn: 20, meal: 30, night: -12 }
  };

  const pattern = patterns[patientType] || patterns.type2;

  for (let day = 0; day < 30; day++) {
    // 4-6 readings per day (fasting, pre-meal, post-meal)
    const readingsPerDay = Math.floor(Math.random() * 3) + 4;

    for (let reading = 0; reading < readingsPerDay; reading++) {
      const timestamp = new Date(startDate);
      timestamp.setDate(timestamp.getDate() + day);

      // Set realistic times
      const times = [7, 9, 12, 15, 18, 21]; // 7am, 9am, 12pm, 3pm, 6pm, 9pm
      timestamp.setHours(times[reading % times.length]);
      timestamp.setMinutes(Math.random() * 60);
      timestamp.setSeconds(0);

      let glucose = pattern.base;
      const hour = timestamp.getHours();

      // Add time-based variations
      if (hour >= 6 && hour <= 8) glucose += pattern.dawn; // Dawn phenomenon
      if ((hour >= 8 && hour <= 10) || (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20)) {
        glucose += pattern.meal; // Post-meal spikes
      }
      if (hour >= 22 || hour <= 5) glucose += pattern.night; // Night time

      // Add random variation
      glucose += (Math.random() - 0.5) * pattern.variance;

      // Ensure realistic bounds
      glucose = Math.max(60, Math.min(300, Math.round(glucose)));

      readings.push({
        timestamp: timestamp.toISOString(),
        glucose: glucose,
        readingType: getReadingType(hour),
        patientId: patientId
      });
    }
  }

  return readings.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
}

function getReadingType(hour) {
  if (hour >= 6 && hour <= 8) return 'Fasting';
  if ((hour >= 8 && hour <= 10) || (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20)) return 'Post-meal';
  if ((hour >= 11 && hour <= 12) || (hour >= 17 && hour <= 18)) return 'Pre-meal';
  return 'Random';
}

// Test connection function
async function testConnection() {
  const session = driver.session();
  try {
    console.log('Testing connection to Neo4j...');
    const result = await session.run('RETURN "Hello Neo4j!" as message, datetime() as now');
    const record = result.records[0];
    console.log('✅ Connection successful!');
    console.log('Message:', record.get('message'));
    console.log('Server time:', record.get('now').toString());
    return true;
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    return false;
  } finally {
    await session.close();
    await driver.close();
  }
}

// Clear database
async function clearDatabase(session) {
  console.log('🗑️  Clearing existing data...');
  const result = await session.run('MATCH (n) RETURN count(n) as nodeCount');
  const nodeCount = result.records[0].get('nodeCount').toNumber();

  if (nodeCount > 0) {
    console.log(`Found ${nodeCount} existing nodes. Deleting...`);
    await session.run('MATCH (n) DETACH DELETE n');
    console.log('✅ Database cleared.');
  } else {
    console.log('Database is already empty.');
  }
}

// Create patients
async function createPatients(session) {
  console.log('👥 Creating patients...');

  for (const patient of patients) {
    await session.run(`
      CREATE (p:Patient {
        patientId: $patientId,
        name: $name,
        age: $age,
        gender: $gender,
        condition: $condition,
        diagnosis_date: $diagnosis_date,
        insurance: $insurance,
        phone: $phone,
        email: $email,
        created_at: datetime()
      })
    `, patient);
  }

  console.log(`✅ Created ${patients.length} patients.`);
}

// Create glucose readings with batch processing for better performance
async function createGlucoseReadings(session) {
  console.log('📊 Creating glucose readings...');

  const patientTypes = ['type2', 'type1', 'prediabetes', 'gestational', 'type2', 'type1', 'type2'];
  let totalReadings = 0;

  for (let i = 0; i < patients.length; i++) {
    const patient = patients[i];
    const readings = generateGlucoseReadings(patient.patientId, patientTypes[i]);

    console.log(`  Creating ${readings.length} glucose readings for ${patient.name}...`);

    // Batch insert for better performance
    const batchSize = 100;
    for (let j = 0; j < readings.length; j += batchSize) {
      const batch = readings.slice(j, j + batchSize);

      await session.run(`
        UNWIND $readings as reading
        MATCH (p:Patient {patientId: reading.patientId})
        CREATE (g:GlucoseReading {
          glucose: reading.glucose,
          timestamp: reading.timestamp,
          readingType: reading.readingType,
          created_at: datetime()
        })
        CREATE (p)-[:HAD_READING]->(g)
      `, { readings: batch });
    }

    totalReadings += readings.length;
  }

  console.log(`✅ Created ${totalReadings} total glucose readings.`);
}

// Create lab results (HbA1c, cholesterol, etc.)
async function createLabResults(session) {
  console.log('🧪 Creating lab results...');

  for (const patient of patients) {
    const results = [];

    // Generate HbA1c results (monthly for 3 months)
    for (let month = 0; month < 3; month++) {
      const date = new Date();
      date.setMonth(date.getMonth() - month);
      date.setDate(15);

      results.push({
        patientId: patient.patientId,
        type: 'HbA1c',
        value: (Math.random() * 3 + 6.5).toFixed(1),
        unit: '%',
        timestamp: date.toISOString(),
        normal_range: '< 7.0%'
      });
    }

    // Add cholesterol test
    const cholesterolDate = new Date();
    cholesterolDate.setMonth(cholesterolDate.getMonth() - 1);
    results.push({
      patientId: patient.patientId,
      type: 'Total Cholesterol',
      value: Math.floor(Math.random() * 100 + 180).toString(),
      unit: 'mg/dL',
      timestamp: cholesterolDate.toISOString(),
      normal_range: '< 200 mg/dL'
    });

    // Batch insert lab results
    await session.run(`
      UNWIND $results as result
      MATCH (p:Patient {patientId: result.patientId})
      CREATE (l:LabResult {
        type: result.type,
        value: result.value,
        unit: result.unit,
        timestamp: result.timestamp,
        normal_range: result.normal_range,
        created_at: datetime()
      })
      CREATE (p)-[:HAD_LAB_RESULT]->(l)
    `, { results });
  }

  console.log('✅ Created lab results for all patients.');
}

// Create hospital structure and staff
async function createHospitalData(session) {
  console.log('🏥 Creating hospital structure...');

  // Create hospital
  await session.run(`
    CREATE (h:Hospital {
      name: "Metro General Hospital",
      location: "New York, NY",
      beds: 500,
      established: "1985",
      phone: "555-HOSPITAL",
      created_at: datetime()
    })
  `);

  // Create departments
  const departments = [
    { name: 'Endocrinology', head: 'Dr. Sarah Wilson', capacity: 50 },
    { name: 'Internal Medicine', head: 'Dr. James Miller', capacity: 75 },
    { name: 'Laboratory', head: 'Dr. Robert Lee', capacity: 30 }
  ];

  for (const dept of departments) {
    await session.run(`
      MATCH (h:Hospital {name: "Metro General Hospital"})
      CREATE (d:Department {
        name: $name,
        head: $head,
        capacity: $capacity,
        created_at: datetime()
      })
      CREATE (h)-[:HAS_DEPARTMENT]->(d)
    `, dept);
  }

  // Create doctors
  const doctors = [
    { name: 'Dr. Sarah Wilson', specialty: 'Endocrinology', license: 'MD12345' },
    { name: 'Dr. James Miller', specialty: 'Internal Medicine', license: 'MD12346' },
    { name: 'Dr. Lisa Thompson', specialty: 'Diabetes Care', license: 'MD12347' }
  ];

  for (const doctor of doctors) {
    await session.run(`
      CREATE (d:Doctor {
        name: $name,
        specialty: $specialty,
        license: $license,
        phone: "555-DOC" + toString(toInteger(rand() * 9000) + 1000),
        email: toLower(replace($name, " ", ".")) + "@hospital.com",
        created_at: datetime()
      })
    `, doctor);
  }

  console.log('✅ Created hospital structure and staff.');
}

// Create patient-doctor relationships and treatments
async function createTreatmentPaths(session) {
  console.log('💊 Creating treatment relationships...');

  // Create medications
  const medications = [
    { name: 'Metformin', type: 'Oral', category: 'Biguanide' },
    { name: 'Insulin Glargine', type: 'Injection', category: 'Long-acting insulin' },
    { name: 'Lisinopril', type: 'Oral', category: 'ACE Inhibitor' }
  ];

  for (const med of medications) {
    await session.run(`
      CREATE (m:Medication {
        name: $name,
        type: $type,
        category: $category,
        created_at: datetime()
      })
    `, med);
  }

  // Create treatment relationships
  for (const patient of patients) {
    // Assign doctor
    await session.run(`
      MATCH (p:Patient {patientId: $patientId})
      MATCH (d:Doctor)
      WITH p, d ORDER BY rand() LIMIT 1
      CREATE (p)-[:TREATED_BY {
        since: $diagnosisDate,
        primary_care: true
      }]->(d)
    `, { patientId: patient.patientId, diagnosisDate: patient.diagnosis_date });

    // Assign medications
    const numMeds = Math.floor(Math.random() * 2) + 1;
    await session.run(`
      MATCH (p:Patient {patientId: $patientId})
      MATCH (m:Medication)
      WITH p, m ORDER BY rand() LIMIT $numMeds
      CREATE (p)-[:TAKES_MEDICATION {
        prescribed_date: $diagnosisDate,
        dosage: "As prescribed",
        frequency: "Daily"
      }]->(m)
    `, { patientId: patient.patientId, diagnosisDate: patient.diagnosis_date, numMeds });
  }

  console.log('✅ Created treatment relationships.');
}

// Main population function
async function populateDatabase() {
  console.log('🚀 Starting Neo4j Healthcare Database Population');
  console.log('================================================');
  console.log(`📡 Connecting to: ${uri}`);
  console.log(`👤 Username: ${username}`);

  const session = driver.session();

  try {
    // Test connection first
    if (!(await testConnection())) {
      throw new Error('Cannot connect to Neo4j database');
    }

    console.log('\n📝 Starting data population...');

    // Clear existing data
    await clearDatabase(session);

    // Create all data
    await createPatients(session);
    await createGlucoseReadings(session);
    await createLabResults(session);
    await createHospitalData(session);
    await createTreatmentPaths(session);

    // Create some research data
    console.log('🔬 Creating research entities...');
    await session.run(`
      MATCH (d:Doctor)
      CREATE (r:Researcher {
        name: d.name + " Research Lab",
        department: d.specialty,
        years_experience: toInteger(rand() * 15) + 5,
        created_at: datetime()
      })
      CREATE (d)-[:CONDUCTS_RESEARCH]->(r)

      CREATE (s:Study {
        title: "Diabetes Management Study - " + d.specialty,
        phase: "Phase " + toString(toInteger(rand() * 3) + 1),
        participants: toInteger(rand() * 500) + 100,
        start_date: toString(date().year - toInteger(rand() * 2)) + "-01-01",
        created_at: datetime()
      })
      CREATE (r)-[:CONDUCTS]->(s)

      CREATE (pub:Publication {
        title: "Clinical Outcomes in " + d.specialty,
        journal: "Medical Research Journal",
        year: date().year - toInteger(rand() * 3),
        impact_factor: toString((rand() * 5 + 2))[0..3],
        created_at: datetime()
      })
      CREATE (s)-[:RESULTED_IN]->(pub)
    `);

    // Get final statistics
    const stats = await session.run(`
      MATCH (p:Patient) WITH count(p) as patients
      MATCH (g:GlucoseReading) WITH patients, count(g) as glucoseReadings
      MATCH (l:LabResult) WITH patients, glucoseReadings, count(l) as labResults
      MATCH (d:Doctor) WITH patients, glucoseReadings, labResults, count(d) as doctors
      MATCH (m:Medication) WITH patients, glucoseReadings, labResults, doctors, count(m) as medications
      RETURN patients, glucoseReadings, labResults, doctors, medications
    `);

    const record = stats.records[0];

    console.log('\n🎉 Database Population Completed Successfully!');
    console.log('==============================================');
    console.log(`👥 Patients: ${record.get('patients').toNumber()}`);
    console.log(`📊 Glucose Readings: ${record.get('glucoseReadings').toNumber()}`);
    console.log(`🧪 Lab Results: ${record.get('labResults').toNumber()}`);
    console.log(`👨‍⚕️ Doctors: ${record.get('doctors').toNumber()}`);
    console.log(`💊 Medications: ${record.get('medications').toNumber()}`);

    console.log('\n📋 Sample Cypher Queries to Try:');
    console.log('================================');
    console.log('1. Get all patients:');
    console.log('   MATCH (p:Patient) RETURN p.name, p.condition, p.age');
    console.log('\n2. Get glucose readings for Patient_1:');
    console.log('   MATCH (p:Patient {patientId: "Patient_1"})-[:HAD_READING]->(g:GlucoseReading)');
    console.log('   RETURN g.timestamp, g.glucose ORDER BY g.timestamp DESC LIMIT 10');
    console.log('\n3. Get treatment paths:');
    console.log('   MATCH p = (patient:Patient)-[*1..3]-(treatment) RETURN p LIMIT 5');

  } catch (error) {
    console.error('❌ Error during population:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// Export functions for external use
module.exports = {
  populateDatabase,
  testConnection,
  patients
};

// Run if called directly
if (require.main === module) {
  populateDatabase()
    .then(() => {
      console.log('\n✅ Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Script failed:', error);
      process.exit(1);
    })
    .finally(() => {
      driver.close();
    });
}
