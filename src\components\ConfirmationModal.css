/* Confirmation Modal Styles */
.confirmation-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.confirmation-modal {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: scale(1);
    transition: transform 0.2s ease-out;
    outline: none;
}

.confirmation-modal-overlay[data-opening="true"] .confirmation-modal {
    transform: scale(0.95);
    animation: modalEnter 0.2s ease-out forwards;
}

@keyframes modalEnter {
    to {
        transform: scale(1);
    }
}

.confirmation-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.confirmation-modal-title {
    margin: 0;
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.confirmation-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.confirmation-modal-close:hover {
    color: var(--text-color);
    background-color: var(--hover-bg);
}

.confirmation-modal-body {
    padding: 1.5rem;
}

.confirmation-modal-message {
    margin: 0;
    color: var(--text-color);
    font-size: 1rem;
    line-height: 1.5;
}

.confirmation-modal-actions {
    display: flex;
    gap: 0.75rem;
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    justify-content: flex-end;
    border-top: 1px solid var(--border-color);
}

.confirmation-modal-button {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.cancel-button {
    background: var(--bg-color);
    color: var(--text-color);
}

.cancel-button:hover {
    background: var(--hover-bg);
    border-color: var(--text-muted);
}

.confirm-button {
    color: white;
    border-color: transparent;
}

.confirm-button.danger {
    background: #dc3545;
}

.confirm-button.danger:hover {
    background: #c82333;
}

.confirm-button.primary {
    background: var(--accent-color);
}

.confirm-button.primary:hover {
    background: var(--accent-hover);
}

.confirm-button.success {
    background: #28a745;
}

.confirm-button.success:hover {
    background: #218838;
}

/* Focus styles for accessibility */
.confirmation-modal-button:focus,
.confirmation-modal-close:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Mobile responsive */
@media (max-width: 640px) {
    .confirmation-modal {
        min-width: 90vw;
        max-width: 90vw;
        margin: 0 1rem;
    }
    
    .confirmation-modal-actions {
        flex-direction: column-reverse;
    }
    
    .confirmation-modal-button {
        width: 100%;
        padding: 0.75rem 1rem;
    }
}