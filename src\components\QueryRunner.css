/* Compact layout to fit one screen (no scroll) */
.query-runner {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  height: 100%;
  /* fill workbench panel */
  overflow: hidden;
  /* prevent internal scroll */
}

.query-runner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.query-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.query-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  /* editor side larger */
  gap: 1rem;
  flex: 1;
  min-height: 0;
  /* allow grid children to size without overflow */
}

/* Left side (tabs content) will scroll internally if needed */
.predefined-queries,
.agp-settings-panel,
.ai-control-panel {
  overflow: auto;
  max-height: 100%;
}

.query-editor {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow: auto;
}

.query-textarea {
  resize: vertical;
  min-height: 120px;
  max-height: 200px;
  /* keep it compact */
}

.query-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.results-section {
  border-top: 1px solid rgba(235, 219, 178, 0.2);
  padding-top: 0.75rem;
  /* fit remaining space for results */
}

.raw-results {
  overflow: auto;
  max-height: 240px;
  /* cap height to keep within viewport */
}

.results-table {
  width: 100%;
  border-collapse: collapse;
}

.results-table th,
.results-table td {
  text-align: left;
  padding: 0.4rem 0.5rem;
}

/* Hide big AGP visualizations in this compact mode */
.agp-section,
.agp-chart,
.agp-statistics,
.ai-recommendations-section {
  display: none !important;
}

/* Enhanced Results Styles */
.enhanced-results {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 1rem;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--gb-bg1);
  border-bottom: 1px solid var(--gb-border);
  flex-wrap: wrap;
  gap: 1rem;
}

.results-header h3 {
  margin: 0;
  color: var(--gb-accent2);
}

.results-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.search-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-input {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(250, 189, 47, 0.1);
}

.page-size-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.page-size-control label {
  color: var(--gb-muted-strong);
}

.page-size-select {
  padding: 0.375rem 0.5rem;
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  background: var(--gb-bg0);
  color: var(--gb-fg);
}

.export-controls {
  display: flex;
  gap: 0.5rem;
}

.export-btn {
  padding: 0.375rem 0.75rem;
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  color: var(--gb-fg);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.export-btn:hover {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
}

.enhanced-results-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.enhanced-results-table th {
  background: var(--gb-bg1);
  padding: 0.75rem 0.5rem;
  border-bottom: 2px solid var(--gb-border);
  color: var(--gb-accent2);
  font-weight: 600;
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 1;
}

.enhanced-results-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.enhanced-results-table th.sortable:hover {
  background: var(--gb-surface-2);
}

.enhanced-results-table th.sorted-asc,
.enhanced-results-table th.sorted-desc {
  background: var(--gb-surface-2);
}

.sort-indicator {
  opacity: 0.5;
  font-size: 0.75rem;
}

.enhanced-results-table th.sortable .sort-indicator {
  opacity: 0.7;
}

.enhanced-results-table th.sorted-asc .sort-indicator,
.enhanced-results-table th.sorted-desc .sort-indicator {
  opacity: 1;
  color: var(--gb-accent);
}

.enhanced-results-table td {
  padding: 0.5rem;
  border-bottom: 1px solid var(--gb-border);
  vertical-align: top;
  max-width: 300px;
  word-wrap: break-word;
}

.enhanced-results-table tr:hover {
  background: var(--gb-surface-1);
}

.enhanced-results-table tr:nth-child(even) {
  background: rgba(235, 219, 178, 0.02);
}

.enhanced-results-table tr:nth-child(even):hover {
  background: var(--gb-surface-1);
}

/* Cell type styling */
.cell-number .number-value {
  color: #83a598;
  font-family: monospace;
}

.cell-boolean .boolean-value {
  font-weight: 600;
}

.cell-boolean .boolean-value.true {
  color: #b8bb26;
}

.cell-boolean .boolean-value.false {
  color: #fb4934;
}

.null-value {
  color: var(--gb-muted);
  font-style: italic;
  opacity: 0.7;
}

.datetime-value {
  font-family: monospace;
  color: #d3869b;
}

.json-object {
  color: var(--gb-fg);
}

.json-object summary {
  cursor: pointer;
  color: var(--gb-accent2);
  font-size: 0.8125rem;
  padding: 0.25rem 0;
}

.json-object pre {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  padding: 0.5rem;
  margin: 0.25rem 0 0 0;
  font-size: 0.75rem;
  max-height: 200px;
  overflow: auto;
}

.long-text {
  color: var(--gb-fg);
}

.long-text summary {
  cursor: pointer;
  color: var(--gb-accent2);
  font-size: 0.8125rem;
}

.long-text .full-text {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: var(--gb-surface-1);
  border-radius: 4px;
  border: 1px solid var(--gb-border);
  max-height: 200px;
  overflow: auto;
  white-space: pre-wrap;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--gb-bg1);
  border-top: 1px solid var(--gb-border);
}

.pagination-info {
  font-size: 0.875rem;
  color: var(--gb-muted-strong);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-btn {
  padding: 0.375rem 0.75rem;
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  color: var(--gb-fg);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  min-width: 2.5rem;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 0.875rem;
  color: var(--gb-muted-strong);
  margin: 0 0.5rem;
}

.results-summary {
  display: flex;
  justify-content: space-around;
  padding: 0.75rem;
  background: var(--gb-surface-1);
  border-top: 1px solid var(--gb-border);
}

.results-summary .summary-item {
  font-size: 0.8125rem;
  color: var(--gb-muted-strong);
}

.results-summary .summary-item strong {
  color: var(--gb-accent2);
}

/* Responsive design for results */
@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    align-items: stretch;
  }

  .results-controls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .search-input {
    min-width: unset;
    width: 100%;
  }

  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }

  .pagination-controls {
    order: -1;
  }

  .enhanced-results-table {
    font-size: 0.75rem;
  }

  .enhanced-results-table th,
  .enhanced-results-table td {
    padding: 0.375rem 0.25rem;
  }
}

/* Scrollable table container */
.results-table-container {
  max-height: 500px;
  overflow: auto;
  border: 1px solid var(--gb-border);
}

/* Enhanced Error Message Styles */
.enhanced-error-message {
  background: var(--gb-surface-1);
  border: 2px solid #fb4934;
  border-radius: 8px;
  margin: 1rem 0;
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhanced-error-message.connection {
  border-color: #fe8019;
}

.enhanced-error-message.syntax {
  border-color: #fabd2f;
}

.enhanced-error-message.security {
  border-color: #fb4934;
}

.enhanced-error-message.performance {
  border-color: #d3869b;
}

.enhanced-error-message.data {
  border-color: #83a598;
}

.enhanced-error-message.server {
  border-color: #8ec07c;
}

.error-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(251, 73, 52, 0.1);
  border-bottom: 1px solid rgba(251, 73, 52, 0.2);
}

.enhanced-error-message.connection .error-header {
  background: rgba(254, 128, 25, 0.1);
  border-bottom-color: rgba(254, 128, 25, 0.2);
}

.enhanced-error-message.syntax .error-header {
  background: rgba(250, 189, 47, 0.1);
  border-bottom-color: rgba(250, 189, 47, 0.2);
}

.enhanced-error-message.performance .error-header {
  background: rgba(211, 134, 155, 0.1);
  border-bottom-color: rgba(211, 134, 155, 0.2);
}

.enhanced-error-message.data .error-header {
  background: rgba(131, 165, 152, 0.1);
  border-bottom-color: rgba(131, 165, 152, 0.2);
}

.enhanced-error-message.server .error-header {
  background: rgba(142, 192, 124, 0.1);
  border-bottom-color: rgba(142, 192, 124, 0.2);
}

.error-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.error-title {
  flex: 1;
  margin: 0;
  color: var(--gb-accent2);
  font-size: 1.125rem;
  font-weight: 600;
}

.retry-button,
.dismiss-button {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  background: var(--gb-surface-2);
  color: var(--gb-fg);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.retry-button:hover:not(:disabled),
.dismiss-button:hover {
  background: var(--gb-bg1);
  border-color: var(--gb-accent);
}

.retry-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-content {
  padding: 1rem;
}

.error-message {
  margin: 0 0 1rem 0;
  color: var(--gb-fg);
  font-size: 0.9375rem;
  line-height: 1.5;
}

.error-suggestion {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 0.75rem;
  margin: 1rem 0;
  border-left: 4px solid #fabd2f;
}

.error-suggestion strong {
  color: var(--gb-accent2);
  display: block;
  margin-bottom: 0.5rem;
}

.error-details {
  margin-top: 1rem;
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  overflow: hidden;
}

.error-details summary {
  padding: 0.75rem;
  background: var(--gb-bg1);
  cursor: pointer;
  font-weight: 600;
  color: var(--gb-accent2);
  border-bottom: 1px solid var(--gb-border);
}

.error-details summary:hover {
  background: var(--gb-surface-1);
}

.error-trace {
  margin: 0;
  padding: 1rem;
  background: var(--gb-bg0);
  color: var(--gb-muted-strong);
  font-family: monospace;
  font-size: 0.8125rem;
  line-height: 1.4;
  white-space: pre-wrap;
  overflow-x: auto;
  border: none;
}

/* Query Management Styles */
.query-history-panel,
.saved-queries-panel,
.bookmarks-panel {
  padding: 1rem;
  max-height: 500px;
  overflow-y: auto;
}

.query-history-panel h3,
.saved-queries-panel h3,
.bookmarks-panel h3 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--gb-muted-strong);
  background: var(--gb-surface-1);
  border: 2px dashed var(--gb-border);
  border-radius: 8px;
}

.history-list,
.saved-list,
.bookmarks-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item,
.saved-item,
.bookmark-item {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.history-item:hover,
.saved-item:hover,
.bookmark-item:hover {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-header,
.saved-header,
.bookmark-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--gb-border);
}

.history-timestamp,
.saved-date,
.bookmark-date {
  font-size: 0.8125rem;
  color: var(--gb-muted-strong);
  font-family: monospace;
}

.history-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.8125rem;
}

.execution-time {
  color: #d3869b;
}

.result-count {
  color: #83a598;
}

.saved-name,
.bookmark-name {
  margin: 0;
  color: var(--gb-accent2);
  font-size: 1rem;
  font-weight: 600;
}

.history-query,
.saved-query,
.bookmark-query {
  margin: 0.75rem 0;
}

.history-query pre,
.saved-query pre,
.bookmark-query pre {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0;
  font-size: 0.8125rem;
  line-height: 1.4;
  overflow-x: auto;
  color: var(--gb-fg);
  white-space: pre-wrap;
  max-height: 150px;
  overflow-y: auto;
}

.history-actions,
.saved-actions,
.bookmark-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(235, 219, 178, 0.1);
}

.history-action-btn,
.saved-action-btn,
.bookmark-action-btn {
  padding: 0.375rem 0.75rem;
  background: var(--gb-surface-2);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  color: var(--gb-fg);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8125rem;
  white-space: nowrap;
}

.history-action-btn:hover:not(:disabled),
.saved-action-btn:hover:not(:disabled),
.bookmark-action-btn:hover:not(:disabled) {
  background: var(--gb-bg1);
  border-color: var(--gb-accent);
  transform: translateY(-1px);
}

.history-action-btn:disabled,
.saved-action-btn:disabled,
.bookmark-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.delete-btn {
  background: rgba(251, 73, 52, 0.1);
  border-color: rgba(251, 73, 52, 0.3);
  color: #fb4934;
}

.delete-btn:hover {
  background: rgba(251, 73, 52, 0.2);
  border-color: #fb4934;
}

/* Tab styling updates for new tabs */
.tab-button {
  position: relative;
  font-size: 0.875rem;
}

.tab-button:nth-child(n+3) {
  font-size: 0.8125rem;
}

/* Responsive design for query management */
@media (max-width: 768px) {

  .history-header,
  .saved-header,
  .bookmark-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .history-stats {
    justify-content: space-between;
  }

  .history-actions,
  .saved-actions,
  .bookmark-actions {
    justify-content: center;
  }

  .history-action-btn,
  .saved-action-btn,
  .bookmark-action-btn {
    flex: 1;
    min-width: 0;
    text-align: center;
  }
}

/* Schema Explorer Styles */
.schema-explorer {
  padding: 1rem;
  max-height: 500px;
  overflow-y: auto;
}

.schema-explorer h3 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.schema-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.schema-section {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 8px;
  padding: 1rem;
}

.schema-section h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
}

.schema-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.schema-item {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.schema-item:hover {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.schema-item h5 {
  color: var(--gb-accent2);
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.schema-item p {
  color: var(--gb-muted-strong);
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  line-height: 1.4;
}

.schema-properties {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.schema-properties span {
  background: var(--gb-surface-2);
  border: 1px solid var(--gb-border);
  border-radius: 12px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  color: var(--gb-accent);
  font-family: monospace;
  font-weight: 500;
}

.schema-query-btn {
  padding: 0.5rem 1rem;
  background: var(--gb-accent);
  border: 1px solid var(--gb-accent);
  border-radius: 4px;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

/* Template styles */
.template-description {
  color: var(--gb-muted-strong);
  font-size: 0.875rem;
  margin: 0.5rem 0 1rem 0;
  line-height: 1.4;
}

.template-params {
  margin-bottom: 1rem;
}

.template-params small {
  color: var(--gb-accent);
  font-family: monospace;
  background: var(--gb-surface-2);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--gb-border);
}

.template-btn {
  padding: 0.5rem 1rem;
  background: var(--gb-accent);
  border: 1px solid var(--gb-accent);
  border-radius: 4px;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.template-btn:hover,
.schema-query-btn:hover {
  background: var(--gb-accent-hover);
  border-color: var(--gb-accent-hover);
}

.schema-query-btn:hover {
  background: #fabd2f;
  border-color: #fabd2f;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(250, 189, 47, 0.3);
}

.schema-relationships {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.relationship-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--gb-bg1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
}

.relationship-item:hover {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
}

.rel-source,
.rel-target {
  background: var(--gb-surface-2);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-family: monospace;
  font-size: 0.8125rem;
  color: var(--gb-accent2);
  font-weight: 600;
}

.rel-type {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-family: monospace;
  font-size: 0.8125rem;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.rel-query-btn {
  padding: 0.375rem 0.75rem;
  background: var(--gb-surface-2);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  color: var(--gb-fg);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.rel-query-btn:hover {
  background: var(--gb-bg1);
  border-color: var(--gb-accent);
}

.query-templates {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--gb-bg1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.2s ease;
}

.template-item:hover {
  background: var(--gb-surface-2);
  border-color: var(--gb-accent);
}

.template-item h5 {
  color: var(--gb-accent2);
  margin: 0;
  font-size: 0.9375rem;
  font-weight: 600;
}

.template-btn {
  padding: 0.5rem 1rem;
  background: var(--gb-surface-2);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  color: var(--gb-fg);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 600;
  white-space: nowrap;
}

.template-btn:hover {
  background: var(--gb-accent);
  border-color: var(--gb-accent);
  color: var(--gb-bg0);
  transform: translateY(-1px);
}

/* Responsive design for schema explorer */
@media (max-width: 768px) {
  .schema-items {
    grid-template-columns: 1fr;
  }

  .relationship-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .rel-type {
    text-align: center;
  }

  .template-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .template-btn {
    width: 100%;
  }
}
