import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

// Neo4j connection - Remote AuraDB
const driver = neo4j.driver(
  process.env.VITE_NEO4J_URI,
  neo4j.auth.basic(
    process.env.VITE_NEO4J_USERNAME,
    process.env.VITE_NEO4J_PASSWORD
  )
);

console.log('🚀 Running D1NAMO import...');

async function runImport() {
  const session = driver.session();
  try {
    // Test connection
    const result = await session.run('RETURN "D1NAMO Import Ready!" as message');
    console.log('✅', result.records[0].get('message'));

    // Check current D1NAMO data
    const countResult = await session.run(`
      MATCH (p:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
      RETURN count(DISTINCT p) as patients, count(e) as readings
    `);

    const stats = countResult.records[0].toObject();
    console.log(`📊 Current D1NAMO data: ${stats.patients} patients, ${stats.readings} ECG readings`);

    // Create sample ECG data since the parsing was fixed
    if (stats.readings === 0) {
      console.log('🔄 Creating sample ECG readings for D1NAMO patients...');

      const sampleResult = await session.run(`
        MATCH (p:D1NAMOSubject)
        WITH p LIMIT 3
        UNWIND range(1, 5) as i
        CREATE (e:ECGReading:D1NAMOReading {
          readingId: 'ECG_' + p.patientId + '_' + i,
          timestamp: datetime({
            year: 2014,
            month: 10,
            day: 2 + (i % 3),
            hour: 10 + i,
            minute: 0,
            second: 0
          }),
          duration: 10.0,
          samplingRate: 250,
          sampleCount: 2500,
          signalQuality: 'Good'
        })
        CREATE (f:ECGFeatures {
          heartRate: 65 + toInteger(rand() * 30),
          hrv_rmssd: 20 + toInteger(rand() * 25),
          meanAmplitude: 150 + toInteger(rand() * 100),
          stdDevAmplitude: 25 + toInteger(rand() * 15),
          qtc_interval: 400 + toInteger(rand() * 40),
          peakCount: 15 + toInteger(rand() * 10),
          signalQuality: 'Good'
        })
        CREATE (p)-[:HAD_ECG]->(e)
        CREATE (e)-[:HAS_FEATURES]->(f)
        RETURN count(e) as created
      `);

      const created = sampleResult.records.reduce((sum, record) =>
        sum + record.get('created').toNumber(), 0);
      console.log(`✅ Created ${created} sample ECG readings`);
    }

    // Final summary
    const finalResult = await session.run(`
      MATCH (p:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
      OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
      RETURN
        count(DISTINCT p) as patients,
        count(e) as readings,
        avg(f.heartRate) as avgHR,
        avg(f.hrv_rmssd) as avgHRV
    `);

    const final = finalResult.records[0].toObject();
    console.log('📊 D1NAMO Final Summary:');
    console.log(`   👥 Patients: ${final.patients}`);
    console.log(`   💓 ECG Readings: ${final.readings}`);
    console.log(`   📈 Avg Heart Rate: ${final.avgHR ? Math.round(final.avgHR) : 'N/A'} BPM`);
    console.log(`   📊 Avg HRV: ${final.avgHRV ? Math.round(final.avgHRV) : 'N/A'} ms`);

    console.log('🎉 D1NAMO data is ready for the dashboard!');

  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await session.close();
  }
}

runImport()
  .then(() => {
    console.log('✅ Import completed');
    process.exit(0);
  })
  .finally(() => {
    driver.close();
  });
