import { Chart, registerables } from 'chart.js'
import 'chartjs-adapter-date-fns'
import annotationPlugin from 'chartjs-plugin-annotation'
import { useCallback, useEffect, useRef, useState } from 'react'
import { DEFAULT_THRESHOLDS, calculateAGPData } from '../utils/agpCalculation'

// Register Chart.js components including annotations once at module load
Chart.register(...registerables, annotationPlugin)

function AGPChart({
    agpData,
    glucoseData = [],
    patientInfo = null,
    hypoThreshold = DEFAULT_THRESHOLDS.HYPO,
    hyperThreshold = DEFAULT_THRESHOLDS.HYPER,
    className = '',
    width = 800,
    height = 400,
    onDataPointClick = null,
}) {
    const canvasRef = useRef(null)
    const chartRef = useRef(null)
    const [viewMode, setViewMode] = useState('agp') // 'agp', 'trend', 'scatter'
    const [statistics, setStatistics] = useState(null)
    const [selectedTimeRange, setSelectedTimeRange] = useState('all') // 'all', '7d', '14d', '30d'

    // Calculate comprehensive statistics
    const calculateStatistics = (data) => {
        if (!data || data.length === 0) return null

        const values = data
            .map((d) => d.glucose || d['g.glucose'])
            .filter((v) => v != null)
        if (values.length === 0) return null

        const sorted = [...values].sort((a, b) => a - b)
        const mean = values.reduce((a, b) => a + b, 0) / values.length
        const variance = values.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / Math.max(values.length - 1, 1)

        return {
            count: values.length,
            mean: mean,
            median: sorted[Math.floor(sorted.length / 2)],
            min: Math.min(...values),
            max: Math.max(...values),
            std: Math.sqrt(variance),
            cv: (Math.sqrt(variance) / mean) * 100, // Coefficient of variation
            timeInRange: {
                veryLow: values.filter((v) => v < 54).length,
                low: values.filter((v) => v >= 54 && v < 70).length,
                target: values.filter((v) => v >= 70 && v <= 180).length,
                high: values.filter((v) => v > 180 && v <= 250).length,
                veryHigh: values.filter((v) => v > 250).length,
            },
            percentages: {
                veryLow: (values.filter((v) => v < 54).length / values.length) * 100,
                low: (values.filter((v) => v >= 54 && v < 70).length / values.length) * 100,
                target: (values.filter((v) => v >= 70 && v <= 180).length / values.length) * 100,
                high: (values.filter((v) => v > 180 && v <= 250).length / values.length) * 100,
                veryHigh: (values.filter((v) => v > 250).length / values.length) * 100,
            },
        }
    }

    // Filter data based on time range
    const filterDataByTimeRange = (data, range) => {
        if (!data || range === 'all') return data

        const now = new Date()
        const days = range === '7d' ? 7 : range === '14d' ? 14 : 30
        const cutoff = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)

        return data.filter((item) => {
            const timestamp = new Date(item.timestamp || item['g.timestamp'])
            return timestamp >= cutoff
        })
    }

    // Create trend chart
    const createTrendChart = useCallback((data) => {
        console.log('🔍 AGP Chart: createTrendChart received data:', data?.length || 0, 'records');
        console.log('🔍 AGP Chart: Sample data:', data?.slice(0, 2));

        const filteredData = filterDataByTimeRange(data, selectedTimeRange)
        console.log('🔍 AGP Chart: Filtered data for trend:', filteredData?.length || 0, 'records');

        const mappedData = filteredData.map((item) => {
            const timestamp = item.timestamp || item['g.timestamp'];
            const glucose = item.glucose || item['g.glucose'];
            console.log('🔍 Mapping item:', { timestamp, glucose, item });
            return {
                x: new Date(timestamp),
                y: glucose,
            };
        });

        console.log('🔍 AGP Chart: Mapped data for trend:', mappedData?.slice(0, 2));

        return {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: 'Glucose Readings',
                        data: mappedData,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        fill: false,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 6,
                        borderWidth: 2,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                onClick: (event, elements) => {
                    if (elements.length > 0 && onDataPointClick) {
                        const index = elements[0].index
                        const dataPoint = filteredData[index]
                        onDataPointClick(dataPoint)
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                        },
                        title: {
                            display: true,
                            text: 'Date',
                            font: { size: 14, weight: 'bold' },
                        },
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Glucose (mg/dL)',
                            font: { size: 14, weight: 'bold' },
                        },
                        min: 50,
                        max: 300,
                    },
                },
                plugins: {
                    title: {
                        display: true,
                        text: `Glucose Trend - ${patientInfo?.name || 'Patient'} (${selectedTimeRange})`,
                        font: { size: 18, weight: 'bold' },
                    },
                    annotation: {
                        annotations: {
                            targetRange: {
                                type: 'box',
                                yMin: 70,
                                yMax: 180,
                                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                borderColor: 'rgba(76, 175, 80, 0.5)',
                                borderWidth: 1,
                            },
                            hypoLine: {
                                type: 'line',
                                yMin: hypoThreshold,
                                yMax: hypoThreshold,
                                borderColor: '#ef4444',
                                borderWidth: 2,
                                borderDash: [5, 5],
                            },
                            hyperLine: {
                                type: 'line',
                                yMin: hyperThreshold,
                                yMax: hyperThreshold,
                                borderColor: '#f59e0b',
                                borderWidth: 2,
                                borderDash: [5, 5],
                            },
                        },
                    },
                },
            },
        }
    }, [selectedTimeRange, hypoThreshold, hyperThreshold])

    // Create scatter plot chart
    const createScatterChart = useCallback((data) => {
        console.log('🔍 AGP Chart: createScatterChart received data:', data?.length || 0, 'records');
        console.log('🔍 AGP Chart: Sample scatter data:', data?.slice(0, 2));

        const filteredData = filterDataByTimeRange(data, selectedTimeRange)
        console.log('🔍 AGP Chart: Filtered data for scatter:', filteredData?.length || 0, 'records');

        const hourlyScatter = filteredData.map((item) => {
            const timestamp = item.timestamp || item['g.timestamp'];
            const glucose = item.glucose || item['g.glucose'];
            const date = new Date(timestamp);
            const hourDecimal = date.getHours() + date.getMinutes() / 60;

            console.log('🔍 Scatter mapping:', { timestamp, glucose, hourDecimal, item });
            return {
                x: hourDecimal,
                y: glucose,
            };
        });

        console.log('🔍 AGP Chart: Mapped scatter data:', hourlyScatter?.slice(0, 2));

        return {
            type: 'scatter',
            data: {
                datasets: [
                    {
                        label: 'Individual Readings',
                        data: hourlyScatter,
                        backgroundColor: 'rgba(102, 126, 234, 0.6)',
                        borderColor: '#667eea',
                        pointRadius: 4,
                        pointHoverRadius: 7,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                onClick: (event, elements) => {
                    if (elements.length > 0 && onDataPointClick) {
                        const index = elements[0].index
                        const dataPoint = filteredData[index]
                        onDataPointClick(dataPoint)
                    }
                },
                scales: {
                    x: {
                        type: 'linear',
                        min: 0,
                        max: 24,
                        title: {
                            display: true,
                            text: 'Hour of Day',
                            font: { size: 14, weight: 'bold' },
                        },
                        ticks: {
                            stepSize: 2,
                            callback: function (value) {
                                return value + ':00'
                            },
                        },
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Glucose (mg/dL)',
                            font: { size: 14, weight: 'bold' },
                        },
                        min: 50,
                        max: 300,
                    },
                },
                plugins: {
                    title: {
                        display: true,
                        text: `Glucose Scatter Plot - ${patientInfo?.name || 'Patient'}`,
                        font: { size: 18, weight: 'bold' },
                    },
                    annotation: {
                        annotations: {
                            targetRange: {
                                type: 'box',
                                yMin: 70,
                                yMax: 180,
                                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                borderColor: 'rgba(76, 175, 80, 0.5)',
                                borderWidth: 1,
                            },
                        },
                    },
                },
            },
        }
    }, [selectedTimeRange, patientInfo])

    // Build or rebuild chart when inputs change
    useEffect(() => {
        let chartData = null

        // Calculate statistics from glucoseData when available
        const stats = calculateStatistics(glucoseData)
        setStatistics(stats)

        if (chartRef.current) {
            chartRef.current.destroy()
        }
        if (!canvasRef.current) return

        const ctx = canvasRef.current.getContext('2d')

        // If AGP view but no agpData prop, compute from glucoseData
        const resolvedAgpData = agpData || (glucoseData && glucoseData.length > 0 ? calculateAGPData(glucoseData) : null)

        try {
            if (viewMode === 'trend' && glucoseData.length > 0) {
                chartData = createTrendChart(glucoseData)
            } else if (viewMode === 'scatter' && glucoseData.length > 0) {
                chartData = createScatterChart(glucoseData)
            } else if (resolvedAgpData) {
                chartData = {
                    type: 'line',
                    data: {
                        labels: resolvedAgpData.labels.map((hour) => `${hour}:00`),
                        datasets: [
                            {
                                label: 'P90 (90th Percentile)',
                                data: resolvedAgpData.percentiles.p90,
                                fill: '+1',
                                backgroundColor: 'rgba(173, 216, 230, 0.3)',
                                borderColor: 'rgba(173, 216, 230, 0.8)',
                                borderWidth: 2,
                                pointRadius: 3,
                                pointHoverRadius: 6,
                                tension: 0.4,
                                order: 3,
                            },
                            {
                                label: 'P10 (10th Percentile)',
                                data: resolvedAgpData.percentiles.p10,
                                fill: false,
                                borderColor: 'rgba(173, 216, 230, 0.8)',
                                borderWidth: 2,
                                pointRadius: 3,
                                pointHoverRadius: 6,
                                tension: 0.4,
                                order: 4,
                            },
                            {
                                label: 'Median (50th Percentile)',
                                data: resolvedAgpData.percentiles.p50,
                                fill: false,
                                borderColor: 'rgb(59, 130, 246)',
                                borderWidth: 4,
                                pointRadius: 4,
                                pointHoverRadius: 8,
                                tension: 0.4,
                                order: 0,
                            },
                            {
                                label: 'P25 (25th Percentile)',
                                data: resolvedAgpData.percentiles.p25,
                                fill: false,
                                borderColor: 'rgba(144, 238, 144, 0.8)',
                                borderWidth: 2,
                                pointRadius: 3,
                                pointHoverRadius: 6,
                                tension: 0.4,
                                order: 2,
                            },
                            {
                                label: 'P75 (75th Percentile)',
                                data: resolvedAgpData.percentiles.p75,
                                fill: '+1',
                                backgroundColor: 'rgba(144, 238, 144, 0.4)',
                                borderColor: 'rgba(144, 238, 144, 0.8)',
                                borderWidth: 2,
                                pointRadius: 3,
                                pointHoverRadius: 6,
                                tension: 0.4,
                                order: 1,
                            },
                        ],
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: `Ambulatory Glucose Profile - ${patientInfo?.name || 'Patient'}`,
                                font: {
                                    size: 18,
                                    weight: 'bold',
                                },
                                color: '#374151',
                            },
                            legend: {
                                display: true,
                                position: 'bottom',
                                labels: {
                                    boxWidth: 14,
                                    boxHeight: 14,
                                    padding: 16,
                                    font: {
                                        size: 12,
                                    },
                                    color: '#374151',
                                    usePointStyle: true,
                                },
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                titleColor: 'white',
                                bodyColor: 'white',
                                borderColor: '#667eea',
                                borderWidth: 2,
                                cornerRadius: 8,
                                callbacks: {
                                    title: function (context) {
                                        const hour = context[0].label
                                        return `Time: ${hour}`
                                    },
                                    label: function (context) {
                                        const value = context.parsed.y
                                        if (value === null) return null
                                        return `${context.dataset.label}: ${value.toFixed(1)} mg/dL`
                                    },
                                },
                            },
                            annotation: {
                                annotations: {
                                    targetRange: {
                                        type: 'box',
                                        yMin: 70,
                                        yMax: 180,
                                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                                        borderColor: 'rgba(76, 175, 80, 0.5)',
                                        borderWidth: 1,
                                        label: {
                                            content: 'Target Range (70-180 mg/dL)',
                                            enabled: true,
                                            position: 'start',
                                        },
                                    },
                                    hypoLine: {
                                        type: 'line',
                                        yMin: hypoThreshold,
                                        yMax: hypoThreshold,
                                        borderColor: '#ef4444',
                                        borderWidth: 2,
                                        borderDash: [5, 5],
                                        label: {
                                            content: `Hypoglycemia (${hypoThreshold} mg/dL)`,
                                            enabled: true,
                                            position: 'start',
                                            backgroundColor: 'rgba(239, 68, 68, 0.8)',
                                            color: 'white',
                                            font: {
                                                size: 11,
                                            },
                                        },
                                    },
                                    hyperLine: {
                                        type: 'line',
                                        yMin: hyperThreshold,
                                        yMax: hyperThreshold,
                                        borderColor: '#f59e0b',
                                        borderWidth: 2,
                                        borderDash: [5, 5],
                                        label: {
                                            content: `Hyperglycemia (${hyperThreshold} mg/dL)`,
                                            enabled: true,
                                            position: 'end',
                                            backgroundColor: 'rgba(245, 158, 11, 0.8)',
                                            color: 'white',
                                            font: {
                                                size: 11,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Hour of Day',
                                    font: {
                                        size: 14,
                                        weight: 'bold',
                                    },
                                    color: '#374151',
                                },
                                grid: {
                                    color: 'rgba(156, 163, 175, 0.3)',
                                    drawOnChartArea: true,
                                },
                                ticks: {
                                    color: '#6b7280',
                                    font: {
                                        size: 11,
                                    },
                                },
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'Glucose (mg/dL)',
                                    font: {
                                        size: 14,
                                        weight: 'bold',
                                    },
                                    color: '#374151',
                                },
                                min: 50,
                                max: 300,
                                grid: {
                                    color: 'rgba(156, 163, 175, 0.3)',
                                    drawOnChartArea: true,
                                },
                                ticks: {
                                    color: '#6b7280',
                                    font: {
                                        size: 11,
                                    },
                                    callback: function (value) {
                                        return value + ' mg/dL'
                                    },
                                },
                            },
                        },
                        elements: {
                            point: {
                                hoverBorderWidth: 3,
                            },
                        },
                    },
                }
            }

            if (chartData) {
                chartRef.current = new Chart(ctx, chartData)
            }
        } catch (error) {
            console.error('Error rendering AGP chart:', error)
            if (canvasRef.current && canvasRef.current.parentElement) {
                canvasRef.current.parentElement.innerHTML =
                    "<p class='text-red-500 text-center p-4'>Error loading chart data.</p>"
            }
        }

        return () => {
            if (chartRef.current) {
                chartRef.current.destroy()
            }
        }
    }, [agpData, glucoseData, hypoThreshold, hyperThreshold, viewMode, selectedTimeRange, patientInfo, createScatterChart, createTrendChart])

    const hasAnyData = (glucoseData && glucoseData.length > 0) || agpData
    if (!hasAnyData) {
        return (
            <div className={`agp-chart-container ${className}`}>
                <div className="no-glucose-data">
                    <h3>📊 No Glucose Data Available</h3>
                    <p>Execute a glucose query to generate AGP visualization</p>
                    <div className="data-tips">
                        <h4>💡 Tips:</h4>
                        <ul>
                            <li>Try "Glucose Over Time" query</li>
                            <li>Use "Glucose Statistics" for analysis</li>
                            <li>Select a specific patient ID</li>
                        </ul>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className={`agp-chart-container ${className}`}>
            {/* Enhanced Chart Controls */}
            <div className="agp-controls">
                <div className="control-section">
                    <div className="view-mode-controls">
                        <button
                            className={`agp-button ${viewMode === 'agp' ? 'active' : ''}`}
                            onClick={() => setViewMode('agp')}
                            disabled={!(agpData || (glucoseData && glucoseData.length > 0))}
                        >
                            📈 AGP Profile
                        </button>
                        <button
                            className={`agp-button ${viewMode === 'trend' ? 'active' : ''}`}
                            onClick={() => setViewMode('trend')}
                            disabled={!glucoseData || glucoseData.length === 0}
                        >
                            📊 Time Trend
                        </button>
                        <button
                            className={`agp-button ${viewMode === 'scatter' ? 'active' : ''}`}
                            onClick={() => setViewMode('scatter')}
                            disabled={!glucoseData || glucoseData.length === 0}
                        >
                            📍 Scatter Plot
                        </button>
                    </div>

                    {(viewMode === 'trend' || viewMode === 'scatter') && (
                        <div className="time-range-controls">
                            <label>Time Range:</label>
                            <select
                                value={selectedTimeRange}
                                onChange={(e) => setSelectedTimeRange(e.target.value)}
                                className="range-select"
                            >
                                <option value="all">All Data</option>
                                <option value="7d">Last 7 Days</option>
                                <option value="14d">Last 14 Days</option>
                                <option value="30d">Last 30 Days</option>
                            </select>
                        </div>
                    )}
                </div>

                {patientInfo && (
                    <div className="patient-info">
                        <div className="patient-details">
                            <h4>👤 {patientInfo.name || patientInfo.patientName}</h4>
                            {patientInfo.condition && <span className="patient-condition">{patientInfo.condition}</span>}
                            {patientInfo.age && <span className="patient-age">Age: {patientInfo.age}</span>}
                        </div>
                    </div>
                )}
            </div>

            {/* Chart Canvas */}
            <div className="chart-wrapper">
                <div className="agp-chart-canvas-container" style={{ height: `${height}px`, maxWidth: `${width}px` }}>
                    <canvas ref={canvasRef} className="chart-canvas" />
                </div>
            </div>

            {/* Enhanced Statistics Panel */}
            {statistics && (
                <div className="agp-statistics">
                    <h4>📊 Comprehensive Glucose Analysis</h4>
                    <div className="stats-grid">
                        <div className="stat-group basic-stats">
                            <h5>📈 Basic Statistics</h5>
                            <div className="stat-item">
                                <span className="stat-label">Average:</span>
                                <span className="stat-value">{statistics.mean.toFixed(1)} mg/dL</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-label">Median:</span>
                                <span className="stat-value">{statistics.median.toFixed(1)} mg/dL</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-label">Range:</span>
                                <span className="stat-value">{statistics.min} - {statistics.max} mg/dL</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-label">Std Dev:</span>
                                <span className="stat-value">{statistics.std.toFixed(1)} mg/dL</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-label">CV:</span>
                                <span className="stat-value">{statistics.cv.toFixed(1)}%</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-label">Readings:</span>
                                <span className="stat-value">{statistics.count}</span>
                            </div>
                        </div>

                        <div className="stat-group time-in-range">
                            <h5>🎯 Time in Range Analysis</h5>
                            <div className="stat-item very-low">
                                <span className="stat-label">Very Low (&lt;54):</span>
                                <span className="stat-value">{statistics.percentages.veryLow.toFixed(1)}%</span>
                                <span className="stat-count">({statistics.timeInRange.veryLow})</span>
                            </div>
                            <div className="stat-item low">
                                <span className="stat-label">Low (54-70):</span>
                                <span className="stat-value">{statistics.percentages.low.toFixed(1)}%</span>
                                <span className="stat-count">({statistics.timeInRange.low})</span>
                            </div>
                            <div className="stat-item target">
                                <span className="stat-label">Target (70-180):</span>
                                <span className="stat-value target-highlight">{statistics.percentages.target.toFixed(1)}%</span>
                                <span className="stat-count">({statistics.timeInRange.target})</span>
                            </div>
                            <div className="stat-item high">
                                <span className="stat-label">High (180-250):</span>
                                <span className="stat-value">{statistics.percentages.high.toFixed(1)}%</span>
                                <span className="stat-count">({statistics.timeInRange.high})</span>
                            </div>
                            <div className="stat-item very-high">
                                <span className="stat-label">Very High (&gt;250):</span>
                                <span className="stat-value">{statistics.percentages.veryHigh.toFixed(1)}%</span>
                                <span className="stat-count">({statistics.timeInRange.veryHigh})</span>
                            </div>
                        </div>

                        <div className="stat-group clinical-insights">
                            <h5>🏥 Clinical Insights</h5>
                            <div className="insight-item">
                                <span className="insight-label">Glycemic Control:</span>
                                <span
                                    className={`insight-value ${statistics.percentages.target >= 70 ? 'excellent' : statistics.percentages.target >= 50 ? 'good' : 'needs-improvement'
                                        }`}
                                >
                                    {statistics.percentages.target >= 70 ? 'Excellent' : statistics.percentages.target >= 50 ? 'Good' : 'Needs Improvement'}
                                </span>
                            </div>
                            <div className="insight-item">
                                <span className="insight-label">Variability:</span>
                                <span className={`insight-value ${statistics.cv <= 36 ? 'low' : statistics.cv <= 50 ? 'moderate' : 'high'}`}>
                                    {statistics.cv <= 36 ? 'Low' : statistics.cv <= 50 ? 'Moderate' : 'High'}
                                </span>
                            </div>
                            <div className="insight-item">
                                <span className="insight-label">Hypoglycemia Risk:</span>
                                <span
                                    className={`insight-value ${statistics.percentages.low + statistics.percentages.veryLow <= 4
                                        ? 'low'
                                        : statistics.percentages.low + statistics.percentages.veryLow <= 10
                                            ? 'moderate'
                                            : 'high'
                                        }`}
                                >
                                    {statistics.percentages.low + statistics.percentages.veryLow <= 4
                                        ? 'Low'
                                        : statistics.percentages.low + statistics.percentages.veryLow <= 10
                                            ? 'Moderate'
                                            : 'High'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Enhanced Information Panel */}
            <div className="agp-info-panel">
                <div className="info-section">
                    <h5>📖 Chart Information</h5>
                    <div className="info-grid">
                        <div className="info-item">
                            <span className="info-label">Chart Type:</span>
                            <span className="info-value">{viewMode === 'agp' ? 'Ambulatory Glucose Profile' : viewMode === 'trend' ? 'Time Series Trend' : 'Scatter Plot'}</span>
                        </div>
                        <div className="info-item">
                            <span className="info-label">Data Points:</span>
                            <span className="info-value">{statistics?.count || (agpData?.totalReadings || 'N/A')}</span>
                        </div>
                        <div className="info-item">
                            <span className="info-label">Time Range:</span>
                            <span className="info-value">{selectedTimeRange === 'all' ? 'All Available Data' : `Last ${selectedTimeRange.replace('d', ' Days')}`}</span>
                        </div>
                        <div className="info-item">
                            <span className="info-label">Thresholds:</span>
                            <span className="info-value">Hypo: {hypoThreshold} | Hyper: {hyperThreshold} mg/dL</span>
                        </div>
                    </div>
                </div>

                <div className="legend-section">
                    <h5>🎨 Chart Legend</h5>
                    {viewMode === 'agp' ? (
                        <ul className="legend-list">
                            <li>
                                <span className="legend-color median"></span>Median (Blue): Typical glucose level
                            </li>
                            <li>
                                <span className="legend-color quartile"></span>Green Zones: 25th-75th percentiles
                            </li>
                            <li>
                                <span className="legend-color percentile"></span>Light Blue: 10th-90th percentiles
                            </li>
                            <li>
                                <span className="legend-color threshold hypo"></span>Red Line: Hypoglycemia threshold
                            </li>
                            <li>
                                <span className="legend-color threshold hyper"></span>Orange Line: Hyperglycemia threshold
                            </li>
                        </ul>
                    ) : (
                        <ul className="legend-list">
                            <li>
                                <span className="legend-color target-range"></span>Green Zone: Target range (70-180 mg/dL)
                            </li>
                            <li>
                                <span className="legend-color data-point"></span>Blue Points/Line: Glucose readings
                            </li>
                            <li>
                                <span className="legend-color threshold hypo"></span>Red Line: Hypoglycemia threshold
                            </li>
                            <li>
                                <span className="legend-color threshold hyper"></span>Orange Line: Hyperglycemia threshold
                            </li>
                        </ul>
                    )}
                </div>
            </div>
        </div>
    )
}

export default AGPChart
