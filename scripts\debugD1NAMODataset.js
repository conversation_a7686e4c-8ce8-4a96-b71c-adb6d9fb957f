import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Debugging D1NAMO dataset import...');

// Test dataset path
const DATASET_PATH = path.join(__dirname, '..', 'data', 'd1namo', 'diabetes_subset_ecg_data');
console.log(`📂 Dataset Path: ${DATASET_PATH}`);
console.log(`✅ Path exists: ${fs.existsSync(DATASET_PATH)}`);

if (fs.existsSync(DATASET_PATH)) {
  try {
    const contents = fs.readdirSync(DATASET_PATH);
    console.log(`📁 Contents (${contents.length} items):`, contents);

    // Check first patient directory
    const patientDirs = contents.filter(dir => /^\d+$/.test(dir)).sort();
    console.log(`👥 Patient directories found: ${patientDirs.length}`);

    if (patientDirs.length > 0) {
      const firstPatient = patientDirs[0];
      const patientPath = path.join(DATASET_PATH, firstPatient);
      console.log(`🔍 Checking patient ${firstPatient} at: ${patientPath}`);

      const patientContents = fs.readdirSync(patientPath);
      console.log(`📁 Patient contents:`, patientContents);

      // Check sensor_data directory
      const sensorDataDir = path.join(patientPath, 'sensor_data');
      if (fs.existsSync(sensorDataDir)) {
        console.log(`📊 Sensor data directory exists`);
        const sensorContents = fs.readdirSync(sensorDataDir);
        console.log(`📁 Sensor data sessions (${sensorContents.length}):`, sensorContents);

        // Check first session
        if (sensorContents.length > 0) {
          const firstSession = sensorContents[0];
          const sessionPath = path.join(sensorDataDir, firstSession);
          console.log(`🔍 Checking session ${firstSession}`);

          const sessionContents = fs.readdirSync(sessionPath);
          console.log(`📁 Session contents:`, sessionContents);

          // Look for ECG files
          const ecgFiles = sessionContents.filter(file => file.includes('ECG') || file.endsWith('.csv'));
          console.log(`💓 ECG files found (${ecgFiles.length}):`, ecgFiles);

          if (ecgFiles.length > 0) {
            const firstECGFile = path.join(sessionPath, ecgFiles[0]);
            const stats = fs.statSync(firstECGFile);
            console.log(`📄 First ECG file: ${ecgFiles[0]} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);

            // Read first few lines
            const content = fs.readFileSync(firstECGFile, 'utf8');
            const lines = content.split('\n').slice(0, 5);
            console.log(`📖 First 5 lines of ECG file:`);
            lines.forEach((line, i) => console.log(`   ${i + 1}: ${line}`));
          }
        }
      } else {
        console.log(`❌ No sensor_data directory found for patient ${firstPatient}`);
      }
    }
  } catch (error) {
    console.error('❌ Error reading dataset:', error);
  }
} else {
  console.log('❌ Dataset path does not exist');
}

console.log('✅ Debug complete');
