# Environment variables for HealthHub Research Platform

# Frontend (Vite) variables - read via import.meta.env
VITE_AI_ENABLED=true
VITE_AI_RECOMMENDATION_LEVEL=detailed
VITE_AI_CONTEXT_WINDOW=8192
VITE_AI_TEMPERATURE=0.3
VITE_OPENAI_MODEL=gpt-4o-mini
VITE_OPENAI_API_KEY=

# Neo4j (client) – prefer API proxy in browser
VITE_NEO4J_USE_API=true
VITE_NEO4J_URI=
VITE_NEO4J_USERNAME=
VITE_NEO4J_PASSWORD=
VITE_NEO4J_DATABASE=neo4j

# Server-side (Cloudflare Functions / Node scripts) – set via wrangler secrets
NEO4J_URI=
NEO4J_USERNAME=
NEO4J_PASSWORD=
NEO4J_DATABASE=neo4j
OPENAI_API_KEY=

# Local development notes:
# - Copy this file to .env for Vite-only variables (non-secret, must start with VITE_)
# - For server-side secrets in local dev, copy .dev.vars.example to .dev.vars (used by wrangler)
# - Recommended: keep VITE_NEO4J_USE_API=true so the browser calls /api/neo4j and credentials stay server-side
# - In production, set secrets with Wrangler:
#     wrangler pages secret put NEO4J_PASSWORD --project-name=healthhub-research-platform
# - Never commit .env or .dev.vars
