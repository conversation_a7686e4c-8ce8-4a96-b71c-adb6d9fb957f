.prompt-performance-analytics {
  padding: 2rem;
  background: var(--gb-bg1);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  margin: 1rem 0;
  color: var(--gb-fg);
}

.analytics-loading {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(235, 219, 178, 0.75);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(235, 219, 178, 0.15);
  border-top: 4px solid var(--gb-accent2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.analytics-empty {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(235, 219, 178, 0.75);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.analytics-header {
  margin-bottom: 2rem;
  text-align: center;
}

.analytics-header h2 {
  margin: 0 0 0.5rem 0;
  color: var(--gb-accent2);
  font-size: 1.75rem;
  font-weight: 700;
}

.analytics-header p {
  margin: 0;
  color: rgba(235, 219, 178, 0.75);
  font-size: 1rem;
  line-height: 1.5;
}

.analytics-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.12);
  flex-wrap: wrap;
  justify-content: center;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.control-group label {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.85);
}

.control-group select {
  padding: 0.5rem;
  border: 1px solid rgba(235, 219, 178, 0.25);
  border-radius: 6px;
  background: rgba(235, 219, 178, 0.05);
  color: var(--gb-fg);
  font-size: 0.9rem;
  cursor: pointer;
}

.summary-dashboard {
  margin-bottom: 3rem;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
}

.summary-card {
  background: linear-gradient(135deg, var(--gb-bg1), rgba(215, 153, 33, 0.06));
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.35);
  border-color: var(--gb-accent2);
}

.card-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
  display: block;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.card-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gb-fg);
  font-family: 'Courier New', monospace;
}

.card-label {
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.75);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.charts-section {
  margin-bottom: 3rem;
}

.chart-container {
  background: rgba(235, 219, 178, 0.05);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.chart-container h3 {
  margin: 0 0 1.5rem 0;
  color: var(--gb-fg);
  font-size: 1.25rem;
  font-weight: 600;
  text-align: center;
}

.recommendations-section {
  border-top: 1px solid rgba(235, 219, 178, 0.12);
  padding-top: 2rem;
}

.recommendations-section h3 {
  margin: 0 0 1.5rem 0;
  color: var(--gb-accent2);
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.recommendation-card {
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  transition: all 0.2s ease;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.recommendation-card.primary {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08), rgba(235, 219, 178, 0.02));
  border: 1px solid #ffc107;
}

.recommendation-card.improvement {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08), rgba(235, 219, 178, 0.02));
  border: 1px solid #28a745;
}

.recommendation-card.insight {
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.08), rgba(235, 219, 178, 0.02));
  border: 1px solid #17a2b8;
}

.rec-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  margin: 0 0 0.75rem 0;
  color: var(--gb-fg);
  font-size: 1.1rem;
  font-weight: 600;
}

.rec-content p {
  margin: 0 0 1rem 0;
  color: rgba(235, 219, 178, 0.85);
  font-size: 0.95rem;
  line-height: 1.5;
}

.rec-action {
  background: rgba(235, 219, 178, 0.05);
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.85);
  font-style: italic;
  border-left: 4px solid var(--gb-accent2);
}

.correlation-matrix {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin: 2rem 0;
}

.correlation-item {
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.correlation-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gb-accent2);
  font-family: 'Courier New', monospace;
  margin-bottom: 0.5rem;
}

.correlation-label {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.trend-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  font-weight: 500;
  margin: 1rem 0;
}

.trend-indicator.improving {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.trend-indicator.declining {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.trend-indicator.stable {
  background: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
  border: 1px solid rgba(23, 162, 184, 0.3);
}

.metric-legend {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 20px;
  font-size: 0.9rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.technique-performance-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.technique-matrix-item {
  background: var(--gb-bg1);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.matrix-technique-name {
  font-weight: 600;
  color: var(--gb-fg);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.matrix-scores {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.matrix-score-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 4px;
}

.matrix-score-label {
  font-size: 0.85rem;
  color: rgba(235, 219, 178, 0.75);
  font-weight: 500;
}

.matrix-score-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--gb-fg);
  font-family: 'Courier New', monospace;
}

/* Responsive design */
@media (max-width: 768px) {
  .prompt-performance-analytics {
    padding: 1rem;
  }

  .analytics-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .control-group {
    min-width: unset;
    width: 100%;
  }

  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
  }

  .correlation-matrix {
    grid-template-columns: 1fr;
  }

  .technique-performance-matrix {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .analytics-header h2 {
    font-size: 1.5rem;
  }

  .summary-cards {
    grid-template-columns: 1fr;
  }

  .card-number {
    font-size: 1.5rem;
  }

  .chart-container {
    padding: 1rem;
  }

  .recommendation-card {
    flex-direction: column;
    text-align: center;
  }

  .rec-icon {
    align-self: center;
  }
}

/* Print styles */
@media print {
  .prompt-performance-analytics {
    box-shadow: none;
    border: 1px solid #000;
  }

  .analytics-controls {
    display: none;
  }

  .chart-container {
    break-inside: avoid;
    border: 1px solid #000;
    margin-bottom: 2rem;
  }

  .recommendation-card {
    break-inside: avoid;
    border: 1px solid #000 !important;
    background: var(--gb-bg1) !important;
  }
}
