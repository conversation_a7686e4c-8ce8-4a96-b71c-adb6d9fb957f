/**
 * AI Service API Endpoint
 * Available at: /api/ai
 *
 * <PERSON>les secure OpenAI API calls for the HealthHub Research Platform
 * Provides AGP recommendations and clinical insights
 */

// Configuration constants
const ITERATION_DELAY_MS = 1000;
const TECHNIQUE_DELAY_MS = 2000;
const ESTIMATED_TECHNIQUE_DURATION_SECONDS = 30;

// CORS headers for browser requests
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json'
};

// Rate limiting (simple in-memory store)
const rateLimiter = new Map();

export async function onRequest(context) {
  const { request, env } = context;

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: CORS_HEADERS });
  }

  try {
    // Rate limiting check
    const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
    const rateLimitKey = `ai_${clientIP}`;
    const now = Date.now();
    const windowMs = 300000; // 5 minutes
    const maxRequests = 10; // 10 AI requests per 5 minutes

    if (rateLimiter.has(rateLimitKey)) {
      const { count, resetTime } = rateLimiter.get(rateLimitKey);
      if (now < resetTime && count >= maxRequests) {
        return new Response(JSON.stringify({
          error: 'Rate limit exceeded',
          message: 'AI service rate limit exceeded. Please try again later.',
          retryAfter: Math.ceil((resetTime - now) / 1000)
        }), {
          status: 429,
          headers: { ...CORS_HEADERS, 'Retry-After': Math.ceil((resetTime - now) / 1000) }
        });
      }
    }

    // Update rate limit counter
    const currentWindow = rateLimiter.get(rateLimitKey);
    if (!currentWindow || now >= currentWindow.resetTime) {
      rateLimiter.set(rateLimitKey, { count: 1, resetTime: now + windowMs });
    } else {
      rateLimiter.set(rateLimitKey, { count: currentWindow.count + 1, resetTime: currentWindow.resetTime });
    }

    if (request.method === 'GET') {
      return handleHealthCheck(env);
    }

    if (request.method === 'POST') {
      const body = await request.json();
      const { action, data, context: requestContext = {} } = body;

      switch (action) {
        case 'agp-recommendations':
          return handleAGPRecommendations(env, data, requestContext);
        case 'clinical-insights':
          return handleClinicalInsights(env, data, requestContext);
        case 'patient-health-recommendations':
          return handlePatientHealthRecommendations(env, data, requestContext);
        case 'prompt-comparison':
          return handlePromptComparison(env, data, requestContext);
        case 'custom-prompt-test':
          return handleCustomPromptTest(env, data, requestContext);
        case 'batch-prompt-test':
          return handleBatchPromptTest(env, data, requestContext);
        case 'prompt-performance-analysis':
          return handlePromptPerformanceAnalysis(env, data, requestContext);
        case 'health-check':
          return handleHealthCheck(env);
        default:
          return errorResponse('Invalid action', 400);
      }
    }

    return errorResponse('Method not allowed', 405);

  } catch (error) {
    console.error('AI API Error:', error);
    return errorResponse('Internal server error', 500);
  }
}

async function handleHealthCheck(env) {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    ai: {
      enabled: !!env.OPENAI_API_KEY,
      mockMode: !env.OPENAI_API_KEY,
      model: 'gpt-3.5-turbo'
    }
  };

  return new Response(JSON.stringify(health, null, 2), {
    headers: CORS_HEADERS
  });
}

async function handleAGPRecommendations(env, agpData, context) {
  if (!agpData || !agpData.percentiles) {
    return errorResponse('Invalid AGP data provided', 400);
  }

  // Require OpenAI API key
  if (!env.OPENAI_API_KEY) {
    return errorResponse('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.', 503);
  }

  try {
    const prompt = buildAGPPrompt(agpData, context);
    const openAIResponse = await callOpenAI(env.OPENAI_API_KEY, prompt);

    return successResponse({
      recommendations: parseAIRecommendations(openAIResponse),
      model_used: 'gpt-3.5-turbo',
      analysis_type: 'AGP Analysis',
      generated_at: new Date().toISOString(),
      ai_confidence: 'high',
      mockMode: false
    });

  } catch (error) {
    console.error('OpenAI API error:', error);
    return errorResponse(`AI service error: ${error.message}`, 500);
  }
}

async function handleClinicalInsights(env, queryResults, context) {
  if (!queryResults || !Array.isArray(queryResults)) {
    return errorResponse('Invalid query results provided', 400);
  }

  // Require OpenAI API key
  if (!env.OPENAI_API_KEY) {
    return errorResponse('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.', 503);
  }

  try {
    const prompt = buildClinicalInsightsPrompt(queryResults, context);
    const openAIResponse = await callOpenAI(env.OPENAI_API_KEY, prompt);

    return successResponse({
      insights: parseAIInsights(openAIResponse),
      model_used: 'gpt-3.5-turbo',
      analysis_type: 'Clinical Insights',
      generated_at: new Date().toISOString(),
      mockMode: false
    });


  } catch (error) {
    console.error('OpenAI API error:', error);
    return errorResponse(`AI service error: ${error.message}`, 500);
  }
}

async function handlePatientHealthRecommendations(env, patientData, context) {
  if (!patientData) {
    return errorResponse('Invalid patient data provided', 400);
  }

  // Require OpenAI API key
  if (!env.OPENAI_API_KEY) {
    return errorResponse('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.', 503);
  }

  try {
    const technique = context.promptingTechnique || 'structured';
    const prompt = buildPatientRecommendationPrompt(patientData, technique, context);
    const openAIResponse = await callOpenAI(env.OPENAI_API_KEY, prompt);

    return successResponse({
      recommendations: parsePatientRecommendations(openAIResponse, technique),
      prompting_technique: technique,
      model_used: 'gpt-3.5-turbo',
      analysis_type: 'Patient Health Recommendations',
      generated_at: new Date().toISOString(),
      patient_profile: generatePatientProfile(patientData),
      mockMode: false
    });

  } catch (error) {
    console.error('OpenAI API error:', error);
    return errorResponse(`AI service error: ${error.message}`, 500);
  }
}

async function handlePromptComparison(env, patientData, context) {
  if (!patientData) {
    return errorResponse('Invalid patient data provided', 400);
  }

  // Require OpenAI API key
  if (!env.OPENAI_API_KEY) {
    return errorResponse('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.', 503);
  }

  const techniques = [
    'structured', 'conversational', 'motivational', 'clinical',
    'zero_shot', 'one_shot', 'few_shot', 'chain_of_thought',
    'tree_of_thought', 'self_consistency', 'react', 'role_playing'
  ];
  const results = {};

  try {
    // Start comprehensive prompting comparison

    for (const technique of techniques) {
      try {
        const prompt = buildPatientRecommendationPrompt(patientData, technique, context);
        const response = await callOpenAI(env.OPENAI_API_KEY, prompt);
        const parsed = parsePatientRecommendations(response, technique);

        results[technique] = {
          ...parsed,
          promptingTechnique: technique,
          effectiveness_score: evaluateRecommendationEffectiveness(parsed, patientData),
          readability_score: evaluateReadability(response),
          actionability_score: evaluateActionability(parsed.recommendations || []),
          technical_complexity: evaluateTechnicalComplexity(technique),
          reasoning_depth: evaluateReasoningDepth(response, technique)
        };

        // Add small delay between API calls to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.error(`Error with ${technique} technique:`, error);
        results[technique] = {
          error: error.message,
          promptingTechnique: technique,
          effectiveness_score: 0,
          technical_complexity: 0,
          reasoning_depth: 0
        };
      }
    }

    return successResponse({
      comparison_results: results,
      best_technique: determineBestTechnique(results),
      technical_analysis: analyzeTechnicalPerformance(results),
      analysis_type: 'comprehensive_prompting_comparison',
      generated_at: new Date().toISOString(),
      patient_profile: generatePatientProfile(patientData),
      mockMode: false
    });

  } catch (error) {
    console.error('Prompt comparison error:', error);
    return errorResponse(`AI service error: ${error.message}`, 500);
  }
}

async function handleCustomPromptTest(env, patientData, context) {
  if (!patientData) {
    return errorResponse('Missing or invalid patient data provided', 400);
  }
  if (!context.customPrompt) {
    return errorResponse('Missing or invalid custom prompt provided', 400);
  }

  // Require OpenAI API key
  if (!env.OPENAI_API_KEY) {
    return errorResponse('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.', 503);
  }

  try {
    const customPrompt = buildCustomPrompt(patientData, context.customPrompt, context);
    const response = await callOpenAI(env.OPENAI_API_KEY, customPrompt);
    const parsed = parsePatientRecommendations(response, 'custom');

    return successResponse({
      ...parsed,
      prompting_technique: 'custom',
      effectiveness_score: evaluateRecommendationEffectiveness(parsed, patientData),
      readability_score: evaluateReadability(response),
      actionability_score: evaluateActionability(parsed.recommendations || []),
      custom_prompt_used: context.customPrompt,
      model_used: 'gpt-3.5-turbo',
      analysis_type: 'Custom Prompt Test',
      generated_at: new Date().toISOString(),
      patient_profile: generatePatientProfile(patientData),
      mockMode: false
    });

  } catch (error) {
    console.error('Custom prompt test error:', error);
    return errorResponse(`AI service error: ${error.message}`, 500);
  }
}

async function handleBatchPromptTest(env, testData, context) {
  if (!testData || !testData.techniques || !testData.patientData) {
    return errorResponse('Invalid batch test data provided', 400);
  }

  // Require OpenAI API key
  if (!env.OPENAI_API_KEY) {
    return errorResponse('OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.', 503);
  }

  const { techniques, patientData, iterations = 1 } = testData;
  const results = {};

  try {
    // Running batch test with multiple techniques and iterations

    for (const technique of techniques) {
      const techniqueResults = [];

      for (let i = 0; i < iterations; i++) {
        try {
          const prompt = buildPatientRecommendationPrompt(patientData, technique, context);
          const startTime = Date.now();
          const response = await callOpenAI(env.OPENAI_API_KEY, prompt);
          const endTime = Date.now();

          const parsed = parsePatientRecommendations(response, technique);

          techniqueResults.push({
            ...parsed,
            iteration: i + 1,
            response_time: endTime - startTime,
            effectiveness_score: evaluateRecommendationEffectiveness(parsed, patientData),
            readability_score: evaluateReadability(response),
            actionability_score: evaluateActionability(parsed.recommendations || []),
            reasoning_depth: evaluateReasoningDepth(response, technique),
            timestamp: new Date().toISOString()
          });

          // Delay between iterations
          if (i < iterations - 1) {
            await new Promise(resolve => setTimeout(resolve, ITERATION_DELAY_MS));
          }

        } catch (error) {
          console.error(`Error in iteration ${i + 1} for ${technique}:`, error);
          techniqueResults.push({
            iteration: i + 1,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        }
      }

      // Calculate averages for this technique
      const validResults = techniqueResults.filter(r => !r.error);
      const averages = calculateIterationAverages(validResults);

      results[technique] = {
        technique,
        iterations: techniqueResults,
        averages,
        consistency: calculateConsistencyScore(validResults),
        reliability: (validResults.length / techniqueResults.length) * 100
      };

      // Delay between techniques
      if (techniques.indexOf(technique) < techniques.length - 1) {
        await new Promise(resolve => setTimeout(resolve, TECHNIQUE_DELAY_MS));
      }
    }

    return successResponse({
      batch_results: results,
      test_summary: generateBatchTestSummary(results),
      best_technique: findBestBatchTechnique(results),
      analysis_type: 'Batch Prompt Test',
      generated_at: new Date().toISOString(),
      patient_profile: generatePatientProfile(patientData),
      mockMode: false
    });

  } catch (error) {
    console.error('Batch prompt test error:', error);
    return errorResponse(`AI service error: ${error.message}`, 500);
  }
}

async function handlePromptPerformanceAnalysis(env, analysisData, context) {
  if (!analysisData || !analysisData.testResults) {
    return errorResponse('Invalid analysis data provided', 400);
  }

  try {
    const { testResults, analysisType = 'comprehensive' } = analysisData;

    const analysis = {
      performance_metrics: calculatePerformanceMetrics(testResults),
      technique_rankings: rankTechniquesByPerformance(testResults),
      consistency_analysis: analyzeConsistency(testResults),
      efficiency_analysis: analyzeEfficiency(testResults),
      recommendation_quality: analyzeRecommendationQuality(testResults),
      statistical_significance: calculateStatisticalSignificance(testResults),
      trends_and_patterns: identifyTrendsAndPatterns(testResults),
      optimization_suggestions: generateOptimizationSuggestions(testResults)
    };

    return successResponse({
      performance_analysis: analysis,
      analysis_type: `Prompt Performance Analysis - ${analysisType}`,
      generated_at: new Date().toISOString(),
      analyzed_techniques: Object.keys(testResults),
      mockMode: false
    });

  } catch (error) {
    console.error('Performance analysis error:', error);
    return errorResponse(`Analysis error: ${error.message}`, 500);
  }
}

async function callOpenAI(apiKey, prompt) {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a clinical diabetes specialist AI assistant. Provide evidence-based recommendations for glucose management based on AGP data and clinical guidelines. Always include appropriate medical disclaimers.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.3,
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  return data.choices[0].message.content;
}

function buildAGPPrompt(agpData, context) {
  const { patientId = 'Unknown' } = context;

  return `
Analyze the following Ambulatory Glucose Profile (AGP) data for patient ${patientId}:

AGP Data Summary:
- Total readings: ${agpData.totalReadings}
- Date range: ${agpData.dateRange?.start} to ${agpData.dateRange?.end}
- Hourly percentile data available: ${agpData.percentiles ? 'Yes' : 'No'}

Glucose Pattern Analysis:
${JSON.stringify(agpData.percentiles, null, 2)}

Please provide:
1. Clinical assessment of glucose patterns
2. Time-in-range analysis recommendations
3. Potential areas for therapy optimization
4. Lifestyle modification suggestions
5. Follow-up recommendations

Format your response as structured clinical recommendations with priority levels (critical, high, medium, low) and evidence levels (strong, moderate, limited).
`;
}

function buildClinicalInsightsPrompt(queryResults, context) {
  const { patientId = 'Unknown' } = context;

  return `
Analyze the following clinical data for patient ${patientId}

Clinical Data:
${JSON.stringify(queryResults.slice(0, 10), null, 2)}

Please provide clinical insights including:
1. Key findings from the data
2. Potential clinical implications
3. Recommended follow-up actions
4. Risk assessment if applicable

Format your response as structured clinical insights with categories and priority levels.
`;
}

function buildPatientRecommendationPrompt(patientData, technique, context) {
  const baseContext = buildPatientContext(patientData);

  switch (technique) {
    // Traditional approaches
    case 'structured':
      return buildStructuredPrompt(baseContext, patientData);
    case 'conversational':
      return buildConversationalPrompt(baseContext, patientData);
    case 'motivational':
      return buildMotivationalPrompt(baseContext, patientData);
    case 'clinical':
      return buildClinicalPrompt(baseContext, patientData);

    // Advanced technical prompting techniques
    case 'zero_shot':
      return buildZeroShotPrompt(baseContext, patientData);
    case 'one_shot':
      return buildOneShotPrompt(baseContext, patientData);
    case 'few_shot':
      return buildFewShotPrompt(baseContext, patientData);
    case 'chain_of_thought':
      return buildChainOfThoughtPrompt(baseContext, patientData);
    case 'tree_of_thought':
      return buildTreeOfThoughtPrompt(baseContext, patientData);
    case 'self_consistency':
      return buildSelfConsistencyPrompt(baseContext, patientData);
    case 'react':
      return buildReActPrompt(baseContext, patientData);
    case 'role_playing':
      return buildRolePlayingPrompt(baseContext, patientData);

    default:
      return buildStructuredPrompt(baseContext, patientData);
  }
}

function buildCustomPrompt(patientData, customPromptTemplate, context) {
  // Replace placeholders in custom prompt with patient data
  let processedPrompt = customPromptTemplate;

  const replacements = {
    '{{patient_name}}': patientData.name || 'Patient',
    '{{age}}': patientData.age || 'Unknown',
    '{{gender}}': patientData.gender || 'Unknown',
    '{{diabetes_type}}': patientData.diabetesType || 'Unknown',
    '{{avg_glucose}}': patientData.recentGlucoseData?.averageGlucose || 'Unknown',
    '{{time_in_range}}': patientData.recentGlucoseData?.timeInRange || 'Unknown',
    '{{challenges}}': (patientData.challenges || []).join(', ') || 'None specified',
    '{{goals}}': (patientData.goals || []).join(', ') || 'None specified',
    '{{medications}}': (patientData.currentMedications || []).join(', ') || 'None specified'
  };

  Object.entries(replacements).forEach(([placeholder, value]) => {
    processedPrompt = processedPrompt.replace(new RegExp(placeholder, 'g'), value);
  });

  return processedPrompt;
}

function calculateIterationAverages(validResults) {
  if (validResults.length === 0) return null;

  const sums = {
    effectiveness_score: 0,
    readability_score: 0,
    actionability_score: 0,
    reasoning_depth: 0,
    response_time: 0
  };

  validResults.forEach(result => {
    sums.effectiveness_score += result.effectiveness_score || 0;
    sums.readability_score += result.readability_score || 0;
    sums.actionability_score += result.actionability_score || 0;
    sums.reasoning_depth += result.reasoning_depth || 0;
    sums.response_time += result.response_time || 0;
  });

  const averages = {};
  Object.keys(sums).forEach(key => {
    averages[key] = sums[key] / validResults.length;
  });

  return averages;
}

function calculateConsistencyScore(validResults) {
  if (validResults.length <= 1) return 100;

  const effectivenessScores = validResults.map(r => r.effectiveness_score || 0);
  const mean = effectivenessScores.reduce((sum, score) => sum + score, 0) / effectivenessScores.length;
  const variance = effectivenessScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / effectivenessScores.length;
  const standardDeviation = Math.sqrt(variance);

  // Convert to consistency score (lower deviation = higher consistency)
  return Math.max(0, 100 - (standardDeviation * 2));
}

function generateBatchTestSummary(results) {
  const techniques = Object.keys(results);
  const totalTests = techniques.length;
  const successfulTechniques = techniques.filter(t =>
    results[t].averages && Object.keys(results[t].averages).length > 0
  ).length;

  const avgReliability = techniques.reduce((sum, t) => sum + (results[t].reliability || 0), 0) / totalTests;
  const avgConsistency = techniques.reduce((sum, t) => sum + (results[t].consistency || 0), 0) / totalTests;

  return {
    total_techniques: totalTests,
    successful_techniques: successfulTechniques,
    success_rate: (successfulTechniques / totalTests) * 100,
    average_reliability: avgReliability,
    average_consistency: avgConsistency,
    test_duration_estimate: techniques.length * ESTIMATED_TECHNIQUE_DURATION_SECONDS
  };
}

function findBestBatchTechnique(results) {
  let bestTechnique = null;
  let highestCompositeScore = -1;

  Object.entries(results).forEach(([technique, result]) => {
    if (!result.averages) return;

    const compositeScore = (
      (result.averages.effectiveness_score * 0.3) +
      (result.averages.readability_score * 0.2) +
      (result.averages.actionability_score * 0.2) +
      (result.averages.reasoning_depth * 0.1) +
      (result.consistency * 0.1) +
      (result.reliability * 0.1)
    );

    if (compositeScore > highestCompositeScore) {
      highestCompositeScore = compositeScore;
      bestTechnique = {
        technique,
        composite_score: compositeScore,
        averages: result.averages,
        consistency: result.consistency,
        reliability: result.reliability,
        total_iterations: result.iterations.length,
        successful_iterations: result.iterations.filter(i => !i.error).length
      };
    }
  });

  return bestTechnique;
}

// Performance Analysis Functions
function calculatePerformanceMetrics(testResults) {
  const metrics = {
    response_times: {},
    effectiveness_scores: {},
    consistency_scores: {},
    error_rates: {},
    token_efficiency: {}
  };

  Object.entries(testResults).forEach(([technique, results]) => {
    const validResults = (results.iterations || []).filter(r => !r.error);
    const totalResults = results.iterations || [];

    if (validResults.length > 0) {
      metrics.response_times[technique] = {
        average: validResults.reduce((sum, r) => sum + (r.response_time || 0), 0) / validResults.length,
        min: Math.min(...validResults.map(r => r.response_time || 0)),
        max: Math.max(...validResults.map(r => r.response_time || 0))
      };

      metrics.effectiveness_scores[technique] = {
        average: validResults.reduce((sum, r) => sum + (r.effectiveness_score || 0), 0) / validResults.length,
        std_dev: calculateStandardDeviation(validResults.map(r => r.effectiveness_score || 0))
      };

      metrics.error_rates[technique] = ((totalResults.length - validResults.length) / totalResults.length) * 100;
    }
  });

  return metrics;
}

function rankTechniquesByPerformance(testResults) {
  const rankings = [];

  Object.entries(testResults).forEach(([technique, results]) => {
    const validResults = (results.iterations || []).filter(r => !r.error);

    if (validResults.length > 0) {
      const avgEffectiveness = validResults.reduce((sum, r) => sum + (r.effectiveness_score || 0), 0) / validResults.length;
      const avgResponseTime = validResults.reduce((sum, r) => sum + (r.response_time || 0), 0) / validResults.length;
      const errorRate = ((results.iterations.length - validResults.length) / results.iterations.length) * 100;

      const performanceScore = (avgEffectiveness * 0.6) + (Math.max(0, 100 - avgResponseTime / 100) * 0.2) + (Math.max(0, 100 - errorRate) * 0.2);

      rankings.push({
        technique,
        performance_score: performanceScore,
        avg_effectiveness: avgEffectiveness,
        avg_response_time: avgResponseTime,
        error_rate: errorRate,
        total_tests: results.iterations.length
      });
    }
  });

  return rankings.sort((a, b) => b.performance_score - a.performance_score);
}

function analyzeConsistency(testResults) {
  const consistencyAnalysis = {};

  Object.entries(testResults).forEach(([technique, results]) => {
    const validResults = (results.iterations || []).filter(r => !r.error);

    if (validResults.length > 1) {
      const effectivenessScores = validResults.map(r => r.effectiveness_score || 0);
      const readabilityScores = validResults.map(r => r.readability_score || 0);

      consistencyAnalysis[technique] = {
        effectiveness_consistency: 100 - (calculateStandardDeviation(effectivenessScores) * 2),
        readability_consistency: 100 - (calculateStandardDeviation(readabilityScores) * 2),
        overall_consistency: results.consistency || 0,
        sample_size: validResults.length
      };
    }
  });

  return consistencyAnalysis;
}

function analyzeEfficiency(testResults) {
  const efficiencyAnalysis = {};

  Object.entries(testResults).forEach(([technique, results]) => {
    const validResults = (results.iterations || []).filter(r => !r.error);

    if (validResults.length > 0) {
      const avgResponseTime = validResults.reduce((sum, r) => sum + (r.response_time || 0), 0) / validResults.length;
      const avgEffectiveness = validResults.reduce((sum, r) => sum + (r.effectiveness_score || 0), 0) / validResults.length;

      efficiencyAnalysis[technique] = {
        time_per_point: avgResponseTime / Math.max(avgEffectiveness, 1),
        efficiency_score: (avgEffectiveness / Math.max(avgResponseTime / 1000, 1)) * 100,
        throughput: validResults.length / (avgResponseTime * validResults.length / 1000 / 60), // tests per minute
        resource_utilization: calculateResourceUtilization(validResults)
      };
    }
  });

  return efficiencyAnalysis;
}

function analyzeRecommendationQuality(testResults) {
  const qualityAnalysis = {};

  Object.entries(testResults).forEach(([technique, results]) => {
    const validResults = (results.iterations || []).filter(r => !r.error && r.recommendations);

    if (validResults.length > 0) {
      const avgRecommendationCount = validResults.reduce((sum, r) => sum + (r.recommendations?.length || 0), 0) / validResults.length;
      const avgActionability = validResults.reduce((sum, r) => sum + (r.actionability_score || 0), 0) / validResults.length;
      const avgReadability = validResults.reduce((sum, r) => sum + (r.readability_score || 0), 0) / validResults.length;

      qualityAnalysis[technique] = {
        avg_recommendation_count: avgRecommendationCount,
        avg_actionability: avgActionability,
        avg_readability: avgReadability,
        quality_score: (avgActionability * 0.4) + (avgReadability * 0.3) + (Math.min(avgRecommendationCount * 10, 30) * 0.3),
        comprehensiveness: avgRecommendationCount >= 5 ? 'high' : avgRecommendationCount >= 3 ? 'medium' : 'low'
      };
    }
  });

  return qualityAnalysis;
}

function calculateStatisticalSignificance(testResults) {
  const significance = {};
  const techniques = Object.keys(testResults);

  // Compare each technique pair
  for (let i = 0; i < techniques.length; i++) {
    for (let j = i + 1; j < techniques.length; j++) {
      const tech1 = techniques[i];
      const tech2 = techniques[j];

      const results1 = (testResults[tech1].iterations || []).filter(r => !r.error);
      const results2 = (testResults[tech2].iterations || []).filter(r => !r.error);

      if (results1.length >= 3 && results2.length >= 3) {
        const scores1 = results1.map(r => r.effectiveness_score || 0);
        const scores2 = results2.map(r => r.effectiveness_score || 0);

        const comparison = `${tech1}_vs_${tech2}`;
        significance[comparison] = {
          sample_sizes: [results1.length, results2.length],
          mean_difference: calculateMean(scores1) - calculateMean(scores2),
          t_statistic: calculateTStatistic(scores1, scores2),
          practical_significance: Math.abs(calculateMean(scores1) - calculateMean(scores2)) > 10 ? 'significant' : 'minimal'
        };
      }
    }
  }

  return significance;
}

function identifyTrendsAndPatterns(testResults) {
  const patterns = {
    performance_clusters: {},
    technique_categories: {},
    optimization_opportunities: []
  };

  const techniques = Object.keys(testResults);

  // Group techniques by performance characteristics
  const performanceGroups = {
    high_performance: [],
    medium_performance: [],
    low_performance: []
  };

  techniques.forEach(technique => {
    const results = testResults[technique];
    const validResults = (results.iterations || []).filter(r => !r.error);

    if (validResults.length > 0) {
      const avgEffectiveness = validResults.reduce((sum, r) => sum + (r.effectiveness_score || 0), 0) / validResults.length;

      if (avgEffectiveness >= 80) {
        performanceGroups.high_performance.push(technique);
      } else if (avgEffectiveness >= 60) {
        performanceGroups.medium_performance.push(technique);
      } else {
        performanceGroups.low_performance.push(technique);
      }
    }
  });

  patterns.performance_clusters = performanceGroups;

  // Identify optimization opportunities
  techniques.forEach(technique => {
    const results = testResults[technique];
    const validResults = (results.iterations || []).filter(r => !r.error);

    if (validResults.length > 0) {
      const avgResponseTime = validResults.reduce((sum, r) => sum + (r.response_time || 0), 0) / validResults.length;
      const avgEffectiveness = validResults.reduce((sum, r) => sum + (r.effectiveness_score || 0), 0) / validResults.length;
      const errorRate = ((results.iterations.length - validResults.length) / results.iterations.length) * 100;

      if (avgResponseTime > 10000 && avgEffectiveness < 70) {
        patterns.optimization_opportunities.push({
          technique,
          issue: 'slow_and_ineffective',
          suggestion: 'Consider simpler prompting approach or technique optimization'
        });
      } else if (errorRate > 20) {
        patterns.optimization_opportunities.push({
          technique,
          issue: 'high_error_rate',
          suggestion: 'Review prompt structure and error handling'
        });
      } else if (avgEffectiveness > 85 && avgResponseTime < 3000) {
        patterns.optimization_opportunities.push({
          technique,
          issue: 'optimal_performance',
          suggestion: 'Consider this technique for production use'
        });
      }
    }
  });

  return patterns;
}

function generateOptimizationSuggestions(testResults) {
  const suggestions = [];
  const techniques = Object.keys(testResults);

  // Analyze overall performance
  const techniquePerformance = techniques.map(technique => {
    const results = testResults[technique];
    const validResults = (results.iterations || []).filter(r => !r.error);

    if (validResults.length === 0) return null;

    const avgEffectiveness = validResults.reduce((sum, r) => sum + (r.effectiveness_score || 0), 0) / validResults.length;
    const avgResponseTime = validResults.reduce((sum, r) => sum + (r.response_time || 0), 0) / validResults.length;
    const errorRate = ((results.iterations.length - validResults.length) / results.iterations.length) * 100;

    return { technique, avgEffectiveness, avgResponseTime, errorRate };
  }).filter(Boolean);

  // Find best performing technique
  const bestTechnique = techniquePerformance.reduce((best, current) =>
    current.avgEffectiveness > best.avgEffectiveness ? current : best
  );

  suggestions.push({
    type: 'best_practice',
    priority: 'high',
    title: `Recommended Primary Technique: ${bestTechnique.technique}`,
    description: `Based on testing, ${bestTechnique.technique} shows the best overall performance with ${bestTechnique.avgEffectiveness.toFixed(1)}% effectiveness.`
  });

  // Identify speed vs quality tradeoffs
  const fastTechniques = techniquePerformance.filter(t => t.avgResponseTime < 3000);
  const qualityTechniques = techniquePerformance.filter(t => t.avgEffectiveness > 80);

  if (fastTechniques.length > 0 && qualityTechniques.length > 0) {
    suggestions.push({
      type: 'strategy',
      priority: 'medium',
      title: 'Speed vs Quality Strategy',
      description: `Use ${fastTechniques[0].technique} for real-time applications and ${qualityTechniques[0].technique} for detailed analysis.`
    });
  }

  // Error rate warnings
  const highErrorTechniques = techniquePerformance.filter(t => t.errorRate > 15);
  if (highErrorTechniques.length > 0) {
    suggestions.push({
      type: 'warning',
      priority: 'high',
      title: 'High Error Rate Alert',
      description: `Techniques ${highErrorTechniques.map(t => t.technique).join(', ')} show high error rates. Review prompt structure.`
    });
  }

  return suggestions;
}

// Helper mathematical functions
function calculateStandardDeviation(values) {
  if (values.length === 0) return 0;
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  return Math.sqrt(variance);
}

function calculateMean(values) {
  return values.reduce((sum, val) => sum + val, 0) / values.length;
}

function calculateTStatistic(sample1, sample2) {
  const mean1 = calculateMean(sample1);
  const mean2 = calculateMean(sample2);
  const n1 = sample1.length;
  const n2 = sample2.length;

  const variance1 = sample1.reduce((sum, val) => sum + Math.pow(val - mean1, 2), 0) / (n1 - 1);
  const variance2 = sample2.reduce((sum, val) => sum + Math.pow(val - mean2, 2), 0) / (n2 - 1);

  const pooledVariance = ((n1 - 1) * variance1 + (n2 - 1) * variance2) / (n1 + n2 - 2);
  const standardError = Math.sqrt(pooledVariance * (1 / n1 + 1 / n2));

  return (mean1 - mean2) / standardError;
}

function calculateResourceUtilization(validResults) {
  // Simplified resource utilization based on response time and token usage
  const avgResponseTime = validResults.reduce((sum, r) => sum + (r.response_time || 0), 0) / validResults.length;
  const avgTokens = validResults.reduce((sum, r) => sum + (r.token_count || 0), 0) / validResults.length;

  // Efficiency score: lower is better
  return {
    time_efficiency: Math.max(0, 100 - (avgResponseTime / 100)),
    token_efficiency: Math.max(0, 100 - (avgTokens / 10)),
    overall_efficiency: Math.max(0, 100 - (avgResponseTime / 100) - (avgTokens / 20))
  };
}

function buildPatientContext(patientData) {
  const {
    name = 'Patient',
    age,
    gender,
    diabetesType,
    diagnosisDate,
    currentMedications = [],
    recentGlucoseData = {},
    lifestyle = {},
    challenges = [],
    goals = []
  } = patientData;

  return {
    demographics: { name, age, gender },
    medical: { diabetesType, diagnosisDate, currentMedications },
    glucose: recentGlucoseData,
    lifestyle,
    challenges,
    goals
  };
}

function buildStructuredPrompt(context, patientData) {
  return `You are a healthcare AI assistant providing personalized diabetes management recommendations.

PATIENT PROFILE:
Name: ${context.demographics.name}
Age: ${context.demographics.age}
Gender: ${context.demographics.gender}
Diabetes Type: ${context.medical.diabetesType}
Diagnosis: ${context.medical.diagnosisDate}

CURRENT GLUCOSE MANAGEMENT:
${JSON.stringify(context.glucose, null, 2)}

LIFESTYLE FACTORS:
${JSON.stringify(context.lifestyle, null, 2)}

CURRENT CHALLENGES:
${context.challenges.join(', ')}

PATIENT GOALS:
${context.goals.join(', ')}

Please provide 5-7 specific, actionable health improvement recommendations organized by category:

1. GLUCOSE MANAGEMENT
2. LIFESTYLE MODIFICATIONS
3. MEDICATION OPTIMIZATION
4. MONITORING & TRACKING
5. RISK PREVENTION
6. WELLNESS & SUPPORT

Format each recommendation with:
- Clear action item
- Expected benefit
- Implementation timeline
- Success metrics

Ensure recommendations are personalized to this patient's specific situation and goals.`;
}

function buildConversationalPrompt(context, patientData) {
  return `Hi! I'm here to help you improve your diabetes management and overall health. Let me share some personalized recommendations based on your current situation.

I can see you're ${context.demographics.age} years old with ${context.medical.diabetesType}, and you've been managing this since ${context.medical.diagnosisDate}. That shows real dedication!

Looking at your recent glucose data and lifestyle, here's what I'd like to suggest to help you feel your best:

${context.challenges.length > 0 ? `I notice you've mentioned challenges with: ${context.challenges.join(', ')}. Let's work on those together.` : ''}

${context.goals.length > 0 ? `Your goals of ${context.goals.join(' and ')} are fantastic - let's create a path to achieve them.` : ''}

Please provide friendly, encouraging recommendations that feel like advice from a supportive health coach. Focus on:

1. Small, manageable changes that build confidence
2. Addressing their specific challenges with empathy
3. Connecting recommendations to their personal goals
4. Using encouraging, non-judgmental language
5. Practical tips they can start today

Make it feel like a conversation with someone who truly cares about their success.`;
}

function buildMotivationalPrompt(context, patientData) {
  return `You have the power to transform your health! Let's unlock your potential for amazing diabetes management.

🎯 YOUR HEALTH TRANSFORMATION JOURNEY

${context.demographics.name}, you've already taken the most important step - you're here, ready to improve. That's the mindset of a champion!

💪 CURRENT STRENGTHS TO BUILD ON:
- You're actively monitoring your health
- You have clear goals: ${context.goals.join(', ')}
- You're seeking ways to improve

🚀 YOUR PERSONALIZED SUCCESS PLAN:

Please create an inspiring, motivation-focused response that:

1. Celebrates their current efforts and progress
2. Reframes challenges as opportunities for growth
3. Sets exciting, achievable milestones
4. Uses energizing language and success imagery
5. Connects each recommendation to their personal "why"
6. Includes celebration moments and reward systems
7. Emphasizes their control and capability

Make them feel excited about their health journey and confident in their ability to succeed!

Focus on transformation, empowerment, and the amazing life they're building through better health.`;
}

function buildClinicalPrompt(context, patientData) {
  return `Clinical Assessment and Evidence-Based Recommendations

PATIENT: ${context.demographics.name}
CONDITION: ${context.medical.diabetesType}
CURRENT MANAGEMENT STATUS: Under Review

CLINICAL DATA ANALYSIS:
Current Glucose Profile: ${JSON.stringify(context.glucose, null, 2)}
Medication Regimen: ${context.medical.currentMedications.join(', ')}
Lifestyle Risk Factors: ${JSON.stringify(context.lifestyle, null, 2)}

CLINICAL CHALLENGES IDENTIFIED:
${context.challenges.map(c => `- ${c}`).join('\n')}

TREATMENT OPTIMIZATION GOALS:
${context.goals.map(g => `- ${g}`).join('\n')}

Provide evidence-based clinical recommendations following current diabetes management guidelines (ADA/EASD):

1. GLYCEMIC CONTROL OPTIMIZATION
2. CARDIOVASCULAR RISK REDUCTION
3. MEDICATION THERAPY MANAGEMENT
4. LIFESTYLE INTERVENTION PROTOCOLS
5. MONITORING & SURVEILLANCE REQUIREMENTS
6. COMPLICATION PREVENTION STRATEGIES

For each recommendation, include:
- Clinical rationale with evidence level (A, B, C)
- Specific intervention parameters
- Monitoring protocols
- Expected clinical outcomes
- Risk mitigation measures

Use precise medical terminology and reference current clinical guidelines. Focus on measurable outcomes and evidence-based interventions.`;
}

// Advanced Technical Prompting Techniques

function buildZeroShotPrompt(context, patientData) {
  return `Generate personalized health improvement recommendations for the following diabetes patient without any examples or prior context.

PATIENT DATA:
- Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
- Condition: ${context.medical.diabetesType}
- Current Challenges: ${context.challenges.join(', ')}
- Goals: ${context.goals.join(', ')}
- Recent Glucose Data: ${JSON.stringify(context.glucose)}

Task: Provide 5-7 specific, actionable health recommendations that will help this patient improve their diabetes management and overall health. Each recommendation should include the action, expected benefit, and timeline.

Recommendations:`;
}

function buildOneShotPrompt(context, patientData) {
  return `Here's an example of a good health recommendation for a diabetes patient:

EXAMPLE:
Patient: 45-year-old with Type 2 diabetes, struggles with post-meal glucose spikes
Recommendation: "Implement a 15-minute walk after each meal"
- Expected Benefit: Reduce post-meal glucose spikes by 20-30mg/dL
- Timeline: Start immediately, see benefits within 1-2 weeks
- Success Metric: Post-meal glucose readings below 180mg/dL

Now, generate similar recommendations for this patient:

PATIENT DATA:
- Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
- Condition: ${context.medical.diabetesType}
- Current Challenges: ${context.challenges.join(', ')}
- Goals: ${context.goals.join(', ')}
- Recent Glucose Data: ${JSON.stringify(context.glucose)}

Your recommendations:`;
}

function buildFewShotPrompt(context, patientData) {
  return `Here are examples of effective health recommendations for diabetes patients:

EXAMPLE 1:
Patient: 35-year-old Type 1 diabetes, frequent hypoglycemia
Recommendation: "Set up low glucose alerts at 80mg/dL on CGM"
- Benefit: Prevent severe hypoglycemic episodes
- Timeline: Configure today, monitor for 1 week
- Success: Zero episodes below 70mg/dL

EXAMPLE 2:
Patient: 60-year-old Type 2 diabetes, high A1C
Recommendation: "Replace refined carbs with whole grains at dinner"
- Benefit: Improve overnight glucose control
- Timeline: Gradual transition over 2 weeks
- Success: Fasting glucose under 130mg/dL

EXAMPLE 3:
Patient: 28-year-old Type 1 diabetes, dawn phenomenon
Recommendation: "Adjust basal insulin timing with endocrinologist"
- Benefit: Reduce morning glucose elevation
- Timeline: Appointment within 2 weeks
- Success: Morning readings 80-120mg/dL

Now generate similar recommendations for this patient:

PATIENT DATA:
- Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
- Condition: ${context.medical.diabetesType}
- Current Challenges: ${context.challenges.join(', ')}
- Goals: ${context.goals.join(', ')}
- Recent Glucose Data: ${JSON.stringify(context.glucose)}

Your recommendations (following the format above):`;
}

function buildChainOfThoughtPrompt(context, patientData) {
  return `I need to develop health recommendations for a diabetes patient. Let me think through this step by step.

PATIENT ANALYSIS:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Current Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

Let me analyze this systematically:

Step 1: Assess Current Glucose Control
- What patterns do I see in the glucose data?
- Are there specific times of day with issues?
- What's the current time-in-range status?

Step 2: Identify Priority Areas
- What are the most critical challenges to address first?
- Which goals are most achievable in the short term?
- What safety considerations are paramount?

Step 3: Consider Patient-Specific Factors
- How does age affect recommendation priorities?
- What lifestyle factors should influence my suggestions?
- Are there medication interactions to consider?

Step 4: Develop Targeted Recommendations
- What specific actions address the identified priorities?
- How can I make recommendations actionable and measurable?
- What timeline is realistic for each recommendation?

Step 5: Prioritize and Sequence
- Which recommendations should be implemented first?
- How do recommendations build on each other?
- What support systems are needed?

Based on this analysis, here are my recommendations:`;
}

function buildTreeOfThoughtPrompt(context, patientData) {
  return `I need to generate health recommendations for this diabetes patient. Let me explore multiple reasoning paths:

PATIENT DATA:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

REASONING TREE:

Branch A: Focus on Glucose Control
├─ A1: Medication optimization approach
│  ├─ A1a: Timing adjustments
│  └─ A1b: Dosage review with provider
├─ A2: Monitoring enhancement approach
│  ├─ A2a: CGM utilization improvement
│  └─ A2b: Testing frequency optimization
└─ A3: Technology integration approach
   ├─ A3a: Apps for tracking
   └─ A3b: Alert systems

Branch B: Focus on Lifestyle Modification
├─ B1: Dietary intervention approach
│  ├─ B1a: Carb counting refinement
│  └─ B1b: Meal timing optimization
├─ B2: Exercise integration approach
│  ├─ B2a: Structured routine development
│  └─ B2b: Activity timing for glucose impact
└─ B3: Stress management approach
   ├─ B3a: Mindfulness techniques
   └─ B3b: Sleep quality improvement

Branch C: Focus on Education and Support
├─ C1: Knowledge enhancement approach
│  ├─ C1a: Diabetes self-management education
│  └─ C1b: Latest research and tools
├─ C2: Support system building approach
│  ├─ C2a: Healthcare team coordination
│  └─ C2b: Family/peer support engagement
└─ C3: Emergency preparedness approach
   ├─ C3a: Hypoglycemia protocols
   └─ C3b: Sick day management

Evaluating paths and selecting optimal combinations:

Most promising path combination: A2 (Monitoring enhancement), B1 (Dietary intervention), C2 (Support system building)

Final integrated recommendations:`;
}

function buildSelfConsistencyPrompt(context, patientData) {
  return `I will generate health recommendations for this diabetes patient from multiple perspectives to ensure consistency and reliability.

PATIENT DATA:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

PERSPECTIVE 1 - Clinical Safety Focus:
From a safety-first clinical perspective, what are the most important recommendations?

PERSPECTIVE 2 - Patient Experience Focus:
From a patient satisfaction and quality of life perspective, what recommendations would be most valuable?

PERSPECTIVE 3 - Evidence-Based Medicine Focus:
From a research and evidence-based medicine perspective, what recommendations have the strongest scientific support?

PERSPECTIVE 4 - Practical Implementation Focus:
From a real-world implementation perspective, what recommendations are most feasible and sustainable?

PERSPECTIVE 5 - Preventive Care Focus:
From a long-term complication prevention perspective, what recommendations provide the best risk reduction?

Now, let me synthesize these perspectives and identify the recommendations that appear consistently across multiple viewpoints:

CONSISTENT RECOMMENDATIONS (appearing in 3+ perspectives):

SYNTHESIZED FINAL RECOMMENDATIONS:
Combining the most consistent and complementary recommendations from all perspectives:`;
}

function buildReActPrompt(context, patientData) {
  return `I am a diabetes care specialist AI. I will use Reasoning and Acting (ReAct) to develop personalized health recommendations.

PATIENT DATA:
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Condition: ${context.medical.diabetesType}
Challenges: ${context.challenges.join(', ')}
Goals: ${context.goals.join(', ')}
Glucose Data: ${JSON.stringify(context.glucose)}

THOUGHT 1: I need to analyze the patient's current glucose control status and identify the most critical areas for improvement.

ACTION 1: Analyze glucose data patterns
OBSERVATION 1: ${analyzeGlucosePatterns(context.glucose)}

THOUGHT 2: Based on the glucose patterns, I should consider what specific interventions would have the most impact.

ACTION 2: Evaluate intervention priorities
OBSERVATION 2: Priority areas identified: ${identifyPriorities(context)}

THOUGHT 3: I need to consider patient-specific factors that might affect recommendation acceptance and implementation.

ACTION 3: Assess patient readiness and barriers
OBSERVATION 3: Key considerations: ${assessReadiness(context)}

THOUGHT 4: Now I should generate specific, actionable recommendations that address the priorities while considering patient factors.

ACTION 4: Generate targeted recommendations
OBSERVATION 4: Recommendations formulated with specific actions, timelines, and success metrics.

THOUGHT 5: I should verify these recommendations align with clinical guidelines and patient goals.

ACTION 5: Validate recommendations against standards
OBSERVATION 5: All recommendations reviewed for safety, efficacy, and goal alignment.

FINAL RECOMMENDATIONS:`;
}

function buildRolePlayingPrompt(context, patientData) {
  return `I am Dr. Sarah Mitchell, an experienced endocrinologist with 15 years of specialized diabetes care. I'm known for my patient-centered approach and ability to create practical, achievable care plans.

*Reviewing patient chart*

PATIENT CONSULTATION NOTES:
Name: Patient ${context.demographics.age}-year-old
Age: ${context.demographics.age}, Gender: ${context.demographics.gender}
Diagnosis: ${context.medical.diabetesType}
Current Medications: ${context.medical.currentMedications?.join(', ') || 'Not specified'}

*Looking at recent glucose data and patient concerns*

Patient's expressed challenges: "${context.challenges.join(', ')}"
Patient's goals: "${context.goals.join(', ')}"

Recent glucose trends: ${JSON.stringify(context.glucose)}

*Thinking like an experienced endocrinologist*

As I review this case, I see several opportunities for improvement. Based on my clinical experience with similar patients, here's what I would recommend:

*Speaking directly to the patient*

"I've reviewed your recent glucose data and listened to your concerns. Here's my personalized care plan for you:

First, let me acknowledge what you're doing well...

Now, here are the specific areas where I think we can make meaningful improvements:

1. IMMEDIATE PRIORITIES (Next 1-2 weeks):

2. SHORT-TERM GOALS (Next 1-3 months):

3. LONG-TERM STRATEGY (3+ months):

*Drawing from clinical experience*

I've seen excellent results with patients in similar situations when they focus on these specific interventions. Let me explain why each recommendation is important for your particular case..."

MY CLINICAL RECOMMENDATIONS:`;
}

// Helper functions for advanced prompting techniques
function analyzeGlucosePatterns(glucoseData) {
  if (!glucoseData || Object.keys(glucoseData).length === 0) {
    return 'Limited glucose data available for pattern analysis';
  }

  const patterns = [];
  if (glucoseData.averageGlucose > 180) patterns.push('elevated average glucose');
  if (glucoseData.timeInRange && glucoseData.timeInRange < 70) patterns.push('suboptimal time in range');
  if (glucoseData.variability === 'high') patterns.push('high glucose variability');

  return patterns.length > 0 ? patterns.join(', ') : 'glucose patterns within acceptable range';
}

function identifyPriorities(context) {
  const priorities = [];
  if (context.challenges.some(c => c.toLowerCase().includes('glucose'))) {
    priorities.push('glucose control optimization');
  }
  if (context.challenges.some(c => c.toLowerCase().includes('exercise'))) {
    priorities.push('activity integration');
  }
  if (context.goals.some(g => g.toLowerCase().includes('weight'))) {
    priorities.push('weight management support');
  }
  return priorities.length > 0 ? priorities.join(', ') : 'general diabetes management improvement';
}

function assessReadiness(context) {
  const factors = [];
  if (context.goals.length > 0) factors.push('clear goal orientation');
  if (context.challenges.length > 0) factors.push('awareness of barriers');
  factors.push(`${context.demographics.age < 40 ? 'younger' : 'mature'} patient demographic`);
  return factors.join(', ');
}

// Enhanced evaluation methods for technical techniques
function evaluateTechnicalComplexity(technique) {
  const complexityScores = {
    structured: 3, conversational: 2, motivational: 2, clinical: 4,
    zero_shot: 1, one_shot: 2, few_shot: 3,
    chain_of_thought: 4, tree_of_thought: 5, self_consistency: 5,
    react: 4, role_playing: 3
  };
  return complexityScores[technique] || 3;
}

function evaluateReasoningDepth(response, technique) {
  // Evaluate how deeply the response demonstrates reasoning
  const reasoningIndicators = [
    'because', 'therefore', 'as a result', 'this leads to', 'consequently',
    'step 1', 'step 2', 'first', 'second', 'then', 'next',
    'perspective', 'approach', 'strategy', 'rationale', 'reasoning'
  ];

  let score = 0;
  const lowerResponse = response.toLowerCase();

  reasoningIndicators.forEach(indicator => {
    const matches = (lowerResponse.match(new RegExp(indicator, 'g')) || []).length;
    score += matches * 2;
  });

  // Bonus for techniques that should show deeper reasoning
  const deepReasoningTechniques = ['chain_of_thought', 'tree_of_thought', 'self_consistency', 'react'];
  if (deepReasoningTechniques.includes(technique)) {
    score *= 1.5;
  }

  return Math.min(score, 100);
}

function analyzeTechnicalPerformance(results) {
  const analysis = {
    best_simple_technique: null,
    best_advanced_technique: null,
    complexity_vs_performance: {},
    reasoning_effectiveness: {},
    technique_categories: {
      traditional: ['structured', 'conversational', 'motivational', 'clinical'],
      few_shot_variants: ['zero_shot', 'one_shot', 'few_shot'],
      reasoning_techniques: ['chain_of_thought', 'tree_of_thought', 'self_consistency'],
      interactive_techniques: ['react', 'role_playing']
    }
  };

  // Find best in each category
  let bestSimpleScore = -1, bestAdvancedScore = -1;

  Object.entries(results).forEach(([technique, result]) => {
    if (result.error) return;

    const compositeScore =
      (result.effectiveness_score * 0.3) +
      (result.readability_score * 0.25) +
      (result.actionability_score * 0.25) +
      (result.reasoning_depth * 0.2);

    const isAdvanced = !analysis.technique_categories.traditional.includes(technique);

    if (isAdvanced && compositeScore > bestAdvancedScore) {
      bestAdvancedScore = compositeScore;
      analysis.best_advanced_technique = { technique, score: compositeScore };
    } else if (!isAdvanced && compositeScore > bestSimpleScore) {
      bestSimpleScore = compositeScore;
      analysis.best_simple_technique = { technique, score: compositeScore };
    }

    // Complexity vs Performance analysis
    analysis.complexity_vs_performance[technique] = {
      complexity: result.technical_complexity || 0,
      performance: compositeScore,
      efficiency: compositeScore / (result.technical_complexity || 1)
    };

    // Reasoning effectiveness
    analysis.reasoning_effectiveness[technique] = {
      reasoning_depth: result.reasoning_depth || 0,
      effectiveness: result.effectiveness_score || 0,
      reasoning_to_effectiveness_ratio: (result.reasoning_depth || 0) / Math.max(result.effectiveness_score || 1, 1)
    };
  });

  return analysis;
}

// Enhanced best technique determination with technical considerations
function determineBestTechnique(results) {
  let bestTechnique = null;
  let highestScore = -1;

  Object.entries(results).forEach(([technique, result]) => {
    if (result.error) return;

    // Enhanced composite score including technical factors
    const compositeScore =
      (result.effectiveness_score * 0.25) +
      (result.readability_score * 0.2) +
      (result.actionability_score * 0.25) +
      (result.reasoning_depth * 0.15) +
      (Math.max(0, 100 - (result.technical_complexity * 10)) * 0.15); // Efficiency bonus

    if (compositeScore > highestScore) {
      highestScore = compositeScore;
      bestTechnique = {
        technique,
        composite_score: compositeScore,
        individual_scores: {
          effectiveness: result.effectiveness_score,
          readability: result.readability_score,
          actionability: result.actionability_score,
          reasoning_depth: result.reasoning_depth,
          technical_complexity: result.technical_complexity
        },
        recommendation_count: result.recommendations?.length || 0,
        technique_category: getTechniqueCategory(technique)
      };
    }
  });

  return bestTechnique;
}

function getTechniqueCategory(technique) {
  const categories = {
    traditional: ['structured', 'conversational', 'motivational', 'clinical'],
    few_shot: ['zero_shot', 'one_shot', 'few_shot'],
    reasoning: ['chain_of_thought', 'tree_of_thought', 'self_consistency'],
    interactive: ['react', 'role_playing']
  };

  for (const [category, techniques] of Object.entries(categories)) {
    if (techniques.includes(technique)) return category;
  }
  return 'other';
}

function parsePatientRecommendations(response, technique) {
  const recommendations = extractRecommendationsFromResponse(response);

  return {
    recommendations: recommendations,
    prompting_technique: technique,
    response_style: analyzeResponseStyle(response, technique),
    patient_focus_score: evaluatePatientFocus(response),
    actionability_score: evaluateActionability(recommendations),
    generated_at: new Date().toISOString(),
    raw_response: response
  };
}

function extractRecommendationsFromResponse(response) {
  const recommendations = [];
  const lines = response.split('\n').filter(line => line.trim());

  let currentCategory = 'General';
  let currentRecommendation = null;

  for (const line of lines) {
    const trimmed = line.trim();

    // Detect category headers
    if (isCategoryHeader(trimmed)) {
      currentCategory = extractCategoryName(trimmed);
      continue;
    }

    // Detect recommendation items
    if (isRecommendationItem(trimmed)) {
      if (currentRecommendation) {
        recommendations.push(currentRecommendation);
      }

      currentRecommendation = {
        category: currentCategory,
        title: extractRecommendationTitle(trimmed),
        description: cleanMessage(trimmed),
        action_items: [],
        expected_benefit: '',
        timeline: '',
        success_metrics: [],
        priority: extractPriority(trimmed),
        difficulty: assessDifficulty(trimmed)
      };
    } else if (currentRecommendation && trimmed) {
      // Continue building current recommendation
      if (trimmed.toLowerCase().includes('benefit:') || trimmed.toLowerCase().includes('expected:')) {
        currentRecommendation.expected_benefit = trimmed.replace(/^.*?benefit:?/i, '').trim();
      } else if (trimmed.toLowerCase().includes('timeline:') || trimmed.toLowerCase().includes('timeframe:')) {
        currentRecommendation.timeline = trimmed.replace(/^.*?timeline:?/i, '').trim();
      } else if (trimmed.toLowerCase().includes('success:') || trimmed.toLowerCase().includes('metric:')) {
        currentRecommendation.success_metrics.push(trimmed.replace(/^.*?success:?/i, '').replace(/^.*?metric:?/i, '').trim());
      } else if (trimmed.startsWith('-') || trimmed.startsWith('•')) {
        currentRecommendation.action_items.push(trimmed.replace(/^[-•]\s*/, ''));
      } else {
        currentRecommendation.description += ' ' + trimmed;
      }
    }
  }

  if (currentRecommendation) {
    recommendations.push(currentRecommendation);
  }

  return recommendations;
}

// Evaluation functions
function evaluateRecommendationEffectiveness(parsed, patientData) {
  let score = 0;
  const recommendations = parsed.recommendations || [];

  // Score based on number of actionable recommendations (max 30 points)
  score += Math.min(recommendations.length * 5, 30);

  // Score based on personalization (max 25 points)
  score += evaluatePersonalization(parsed.raw_response, patientData);

  // Score based on goal alignment (max 25 points)
  score += evaluateGoalAlignment(recommendations, patientData.goals || []);

  // Score based on challenge addressing (max 20 points)
  score += evaluateChallengeAddressing(recommendations, patientData.challenges || []);

  return Math.min(score, 100);
}

function evaluateReadability(response) {
  // Simple readability assessment based on sentence length, complexity, etc.
  const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const avgSentenceLength = sentences.reduce((sum, s) => sum + s.split(' ').length, 0) / sentences.length;

  // Score inversely related to complexity (simpler = higher score)
  const complexityScore = Math.max(0, 100 - (avgSentenceLength - 15) * 2);

  // Bonus for patient-friendly language
  const friendlyWords = ['you', 'your', 'can', 'will', 'help', 'easy', 'simple', 'start'];
  const friendlyScore = friendlyWords.filter(word =>
    response.toLowerCase().includes(word)
  ).length * 5;

  return Math.min(complexityScore + friendlyScore, 100);
}

function evaluateActionability(recommendations) {
  if (!recommendations || recommendations.length === 0) return 0;

  let score = 0;
  recommendations.forEach(rec => {
    // Has specific action items
    if (rec.action_items && rec.action_items.length > 0) score += 15;

    // Has timeline
    if (rec.timeline && rec.timeline.trim().length > 0) score += 10;

    // Has success metrics
    if (rec.success_metrics && rec.success_metrics.length > 0) score += 10;

    // Contains action verbs
    const actionVerbs = ['start', 'begin', 'track', 'monitor', 'reduce', 'increase', 'schedule', 'contact'];
    if (actionVerbs.some(verb => rec.description.toLowerCase().includes(verb))) score += 5;
  });

  return Math.min(score / recommendations.length, 100);
}

function evaluatePatientFocus(response) {
  const patientFocusIndicators = [
    'you', 'your', 'yourself', 'personal', 'personalized',
    'specific to you', 'based on your', 'for you', 'your goals'
  ];

  let score = 0;
  patientFocusIndicators.forEach(indicator => {
    const regex = new RegExp(indicator, 'gi');
    const matches = (response.match(regex) || []).length;
    score += matches * 5;
  });

  return Math.min(score, 100);
}



function generatePatientProfile(patientData) {
  return {
    demographics: {
      age_range: getAgeRange(patientData.age),
      gender: patientData.gender,
      diabetes_type: patientData.diabetesType
    },
    engagement_factors: {
      has_goals: (patientData.goals || []).length > 0,
      has_challenges: (patientData.challenges || []).length > 0,
      active_monitoring: !!patientData.recentGlucoseData
    },
    complexity_level: assessPatientComplexity(patientData)
  };
}

// Helper functions
function isCategoryHeader(line) {
  return /^\d+\.\s*[A-Z\s&]+$/.test(line) ||
    /^[A-Z\s&]+:?\s*$/.test(line) ||
    line.includes('MANAGEMENT') || line.includes('RECOMMENDATIONS');
}

function extractCategoryName(line) {
  return line.replace(/^\d+\.\s*/, '').replace(/:$/, '').trim();
}

function isRecommendationItem(line) {
  return line.match(/^\d+\./) || line.match(/^[-•]\s/) || line.includes('Recommendation:');
}

function extractRecommendationTitle(line) {
  return line.replace(/^\d+\.\s*/, '').replace(/^[-•]\s*/, '').split('.')[0].trim();
}

function extractPriority(text) {
  if (text.toLowerCase().includes('urgent') || text.toLowerCase().includes('immediate')) return 'urgent';
  if (text.toLowerCase().includes('high priority') || text.toLowerCase().includes('important')) return 'high';
  if (text.toLowerCase().includes('medium') || text.toLowerCase().includes('moderate')) return 'medium';
  return 'low';
}

function assessDifficulty(text) {
  const easyIndicators = ['start', 'begin', 'simple', 'easy', 'daily'];
  const hardIndicators = ['major', 'significant', 'comprehensive', 'complex'];

  const easyCount = easyIndicators.filter(w => text.toLowerCase().includes(w)).length;
  const hardCount = hardIndicators.filter(w => text.toLowerCase().includes(w)).length;

  if (easyCount > hardCount) return 'easy';
  if (hardCount > easyCount) return 'challenging';
  return 'moderate';
}

function cleanMessage(text) {
  return text.replace(/^\d+\.|^-|^•|^Priority:|^Risk:|^Recommendation:/i, '').trim();
}

function evaluatePersonalization(response, patientData) {
  let score = 0;

  // Check if patient's specific data is referenced
  if (patientData.age && response.includes(patientData.age.toString())) score += 5;
  if (patientData.diabetesType && response.includes(patientData.diabetesType)) score += 5;
  if (patientData.name && response.includes(patientData.name)) score += 5;

  // Check for personalized language
  const personalizedPhrases = ['based on your', 'given your', 'for your specific', 'considering your'];
  personalizedPhrases.forEach(phrase => {
    if (response.toLowerCase().includes(phrase)) score += 3;
  });

  return Math.min(score, 25);
}

function evaluateGoalAlignment(recommendations, goals) {
  if (goals.length === 0) return 20; // Default score if no goals specified

  let alignmentScore = 0;
  goals.forEach(goal => {
    const goalKeywords = goal.toLowerCase().split(' ');
    recommendations.forEach(rec => {
      const recText = (rec.description + ' ' + rec.title).toLowerCase();
      if (goalKeywords.some(keyword => recText.includes(keyword))) {
        alignmentScore += 5;
      }
    });
  });

  return Math.min(alignmentScore, 25);
}

function evaluateChallengeAddressing(recommendations, challenges) {
  if (challenges.length === 0) return 20; // Default score if no challenges specified

  let addressingScore = 0;
  challenges.forEach(challenge => {
    const challengeKeywords = challenge.toLowerCase().split(' ');
    recommendations.forEach(rec => {
      const recText = (rec.description + ' ' + rec.title).toLowerCase();
      if (challengeKeywords.some(keyword => recText.includes(keyword))) {
        addressingScore += 5;
      }
    });
  });

  return Math.min(addressingScore, 20);
}

function assessPatientComplexity(patientData) {
  let complexity = 0;

  // Age factor
  if (patientData.age > 65) complexity += 1;
  if (patientData.age < 30) complexity += 1;

  // Multiple medications
  if ((patientData.currentMedications || []).length > 3) complexity += 1;

  // Multiple challenges
  if ((patientData.challenges || []).length > 2) complexity += 1;

  // Poor glucose control
  if (patientData.recentGlucoseData?.averageGlucose > 180) complexity += 1;

  if (complexity <= 1) return 'low';
  if (complexity <= 3) return 'moderate';
  return 'high';
}

function analyzeResponseStyle(response, technique) {
  const styles = {
    structured: { formal: true, organized: true, clinical: false },
    conversational: { formal: false, organized: false, clinical: false },
    motivational: { formal: false, organized: false, clinical: false, inspirational: true },
    clinical: { formal: true, organized: true, clinical: true }
  };

  return styles[technique] || styles.structured;
}

function getAgeRange(age) {
  if (!age) return 'Unknown';
  if (age < 18) return 'Pediatric';
  if (age < 30) return 'Young Adult';
  if (age < 50) return 'Adult';
  if (age < 65) return 'Middle Age';
  return 'Older Adult';
}

function parseAIRecommendations(aiResponse) {
  // Simple parsing - in production, you might want more sophisticated parsing
  const recommendations = [];
  const lines = aiResponse.split('\n').filter(line => line.trim());

  let currentRec = null;
  for (const line of lines) {
    if (line.includes('1.') || line.includes('2.') || line.includes('3.') ||
      line.includes('4.') || line.includes('5.')) {
      if (currentRec) {
        recommendations.push(currentRec);
      }
      currentRec = {
        category: 'Clinical Management',
        level: 'medium',
        priority: 'medium',
        message: line.replace(/^\d+\.\s*/, ''),
        recommendation: '',
        evidence_level: 'moderate',
        source: 'AI Analysis'
      };
    } else if (currentRec && line.trim()) {
      currentRec.recommendation += ' ' + line.trim();
    }
  }

  if (currentRec) {
    recommendations.push(currentRec);
  }

  return recommendations.length > 0 ? recommendations : [
    {
      category: 'General',
      level: 'medium',
      priority: 'medium',
      message: 'AI analysis completed successfully',
      recommendation: aiResponse.substring(0, 200) + '...',
      evidence_level: 'moderate',
      source: 'AI Analysis'
    }
  ];
}

function parseAIInsights(aiResponse) {
  // Simple parsing for insights
  return [
    {
      category: 'Clinical Analysis',
      level: 'medium',
      message: aiResponse.substring(0, 300) + (aiResponse.length > 300 ? '...' : ''),
      recommendation: 'Review findings with healthcare provider'
    }
  ];
}

function generateMockAGPRecommendations(agpData, context) {
  const _context = context; // Context available for future use
  const recommendations = [];

  // Mock recommendations based on AGP data
  if (agpData.totalReadings < 100) {
    recommendations.push({
      category: 'Data Quality',
      level: 'medium',
      priority: 'medium',
      message: 'Limited glucose data available for comprehensive analysis',
      recommendation: 'Consider increasing glucose monitoring frequency for better insights',
      evidence_level: 'strong',
      source: 'Clinical Guidelines'
    });
  }

  recommendations.push({
    category: 'Time-in-Range',
    level: 'good',
    priority: 'medium',
    message: 'AGP analysis shows opportunities for glucose optimization',
    recommendation: 'Monitor patterns and consider timing of meals and medication',
    evidence_level: 'strong',
    source: 'Rule-based Analysis'
  });

  recommendations.push({
    category: 'Pattern Analysis',
    level: 'medium',
    priority: 'low',
    message: 'Glucose variability within acceptable ranges',
    recommendation: 'Continue current management approach with regular monitoring',
    evidence_level: 'moderate',
    source: 'Statistical Analysis'
  });

  return {
    recommendations,
    model_used: 'rule-based-mock',
    analysis_type: 'AGP Mock Analysis',
    generated_at: new Date().toISOString(),
    mockMode: true,
    ai_confidence: 'medium'
  };
}

function generateMockClinicalInsights(queryResults, context) {
  const _context = context; // Context available for future use
  return {
    insights: [
      {
        category: 'Data Overview',
        level: 'medium',
        message: `Analysis completed on ${queryResults.length} clinical records`,
        recommendation: 'Data appears consistent with expected clinical patterns'
      },
      {
        category: 'Clinical Patterns',
        level: 'good',
        message: 'No concerning patterns detected in available data',
        recommendation: 'Continue regular monitoring and follow-up'
      }
    ],
    model_used: 'rule-based-mock',
    analysis_type: 'Clinical Mock Analysis',
    generated_at: new Date().toISOString(),
    mockMode: true
  };
}

function successResponse(data) {
  return new Response(JSON.stringify(data), {
    status: 200,
    headers: CORS_HEADERS
  });
}

function errorResponse(message, status = 400) {
  return new Response(JSON.stringify({
    error: true,
    message: message,
    timestamp: new Date().toISOString()
  }), {
    status: status,
    headers: CORS_HEADERS
  });
}
