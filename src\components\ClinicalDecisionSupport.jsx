import { useEffect, useState } from 'react';
import './ClinicalDecisionSupport.css';

const ClinicalDecisionSupport = ({ patientData, agpData, medications = [] }) => {
    const [alerts, setAlerts] = useState([]);
    const [recommendations, setRecommendations] = useState([]);
    const [drugInteractions, setDrugInteractions] = useState([]);
    const [careProtocols, setCareProtocols] = useState([]);
    const [clinicalGuidelines, setClinicalGuidelines] = useState([]);
    const [selectedView, setSelectedView] = useState('overview');

    useEffect(() => {
        if (patientData && agpData) {
            analyzePatientData();
        }
    }, [patientData, agpData, medications]);

    const analyzePatientData = async () => {
        // Generate clinical alerts
        const generatedAlerts = generateClinicalAlerts();
        setAlerts(generatedAlerts);

        // Generate treatment recommendations
        const treatmentRecs = generateTreatmentRecommendations();
        setRecommendations(treatmentRecs);

        // Check drug interactions
        const interactions = checkDrugInteractions();
        setDrugInteractions(interactions);

        // Generate care protocols
        const protocols = generateCareProtocols();
        setCareProtocols(protocols);

        // Load clinical guidelines
        const guidelines = loadClinicalGuidelines();
        setClinicalGuidelines(guidelines);
    };

    const generateClinicalAlerts = () => {
        const alerts = [];

        if (!agpData) return alerts;

        const tir = agpData.timeInRange;
        const stats = agpData.summaryStatistics;

        // Severe hypoglycemia alert
        if (tir?.ranges?.veryLow?.percentage > 1) {
            alerts.push({
                id: 'severe-hypo',
                type: 'critical',
                category: 'Hypoglycemia Risk',
                title: 'Severe Hypoglycemia Risk',
                message: `${tir.ranges.veryLow.percentage.toFixed(1)}% time below 54 mg/dL exceeds safety threshold (>1%)`,
                recommendations: [
                    'Review basal insulin dosing',
                    'Consider CGM with predictive low alerts',
                    'Evaluate meal timing and carbohydrate intake',
                    'Assess for hypoglycemia unawareness'
                ],
                priority: 'high',
                evidence: 'ADA/EASD Consensus Report 2019'
            });
        }

        // Hyperglycemia alert
        if (tir?.ranges?.veryHigh?.percentage > 5) {
            alerts.push({
                id: 'severe-hyper',
                type: 'warning',
                category: 'Hyperglycemia Risk',
                title: 'Excessive Hyperglycemia',
                message: `${tir.ranges.veryHigh.percentage.toFixed(1)}% time above 250 mg/dL exceeds target (<5%)`,
                recommendations: [
                    'Consider insulin dose adjustment',
                    'Evaluate adherence to therapy',
                    'Review carbohydrate counting accuracy',
                    'Check for illness or stress factors'
                ],
                priority: 'medium',
                evidence: 'International Consensus on TIR 2019'
            });
        }

        // High glucose variability
        if (stats?.coefficientOfVariation > 36) {
            alerts.push({
                id: 'high-variability',
                type: 'info',
                category: 'Glucose Variability',
                title: 'High Glucose Variability',
                message: `CV of ${stats.coefficientOfVariation.toFixed(1)}% exceeds optimal range (≤36%)`,
                recommendations: [
                    'Structured diabetes education',
                    'Review injection technique',
                    'Consider continuous glucose monitoring',
                    'Evaluate lifestyle factors'
                ],
                priority: 'medium',
                evidence: 'ATTD Consensus Statement 2019'
            });
        }

        // Poor time in range
        if (tir?.targetRangePercentage < 50) {
            alerts.push({
                id: 'poor-tir',
                type: 'warning',
                category: 'Glycemic Control',
                title: 'Suboptimal Time in Range',
                message: `TIR of ${tir.targetRangePercentage.toFixed(1)}% is below acceptable threshold (>50%)`,
                recommendations: [
                    'Comprehensive diabetes management review',
                    'Consider therapy intensification',
                    'Diabetes self-management education',
                    'Frequent follow-up appointments'
                ],
                priority: 'high',
                evidence: 'ADA Standards of Care 2023'
            });
        }

        return alerts.sort((a, b) => {
            const priorityOrder = { high: 3, medium: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
    };

    const generateTreatmentRecommendations = () => {
        const recommendations = [];

        if (!patientData || !agpData) return recommendations;

        const condition = patientData.condition || '';
        const age = patientData.age || 0;
        const tir = agpData.timeInRange;
        const avgGlucose = agpData.summaryStatistics?.mean || 0;

        // Type 1 Diabetes recommendations
        if (condition.includes('Type 1')) {
            if (tir?.targetRangePercentage < 70) {
                recommendations.push({
                    category: 'Insulin Therapy',
                    title: 'Consider Insulin Pump Therapy',
                    description: 'Continuous subcutaneous insulin infusion may improve glycemic control',
                    rationale: 'Studies show improved TIR with pump therapy in T1D patients',
                    contraindications: ['Patient unwillingness', 'Severe DKA episodes', 'Poor adherence history'],
                    monitoring: 'Monitor TIR, hypoglycemia episodes, and patient satisfaction'
                });
            }

            if (agpData.summaryStatistics?.coefficientOfVariation > 36) {
                recommendations.push({
                    category: 'Technology',
                    title: 'Continuous Glucose Monitoring',
                    description: 'CGM with predictive alerts can reduce glucose variability',
                    rationale: 'Real-time glucose data enables better dosing decisions',
                    contraindications: ['Inability to respond to alarms', 'Severe skin reactions'],
                    monitoring: 'Track CV, TIR, and alarm fatigue'
                });
            }
        }

        // Type 2 Diabetes recommendations
        if (condition.includes('Type 2')) {
            if (avgGlucose > 180) {
                recommendations.push({
                    category: 'Medication',
                    title: 'Consider SGLT2 Inhibitor',
                    description: 'Add SGLT2 inhibitor for cardiovascular and renal benefits',
                    rationale: 'Evidence for CV outcome benefits in T2D with established CV disease',
                    contraindications: ['eGFR <30', 'Recurrent UTIs', 'DKA history'],
                    monitoring: 'Monitor kidney function, volume status, and ketones'
                });
            }

            if (tir?.targetRangePercentage < 50 && age > 65) {
                recommendations.push({
                    category: 'Glycemic Targets',
                    title: 'Individualized Glycemic Targets',
                    description: 'Consider less stringent targets for elderly patients',
                    rationale: 'Balance glycemic control with hypoglycemia risk in elderly',
                    contraindications: ['Limited life expectancy', 'Extensive comorbidities'],
                    monitoring: 'Assess functional status and hypoglycemia episodes'
                });
            }
        }

        // Lifestyle recommendations
        if (tir?.targetRangePercentage < 60) {
            recommendations.push({
                category: 'Lifestyle',
                title: 'Structured Exercise Program',
                description: 'Regular aerobic and resistance training can improve glycemic control',
                rationale: 'Exercise improves insulin sensitivity and glucose uptake',
                contraindications: ['Severe cardiac disease', 'Active retinopathy', 'Severe neuropathy'],
                monitoring: 'Monitor blood glucose before/after exercise, adjust therapy'
            });
        }

        return recommendations;
    };

    const checkDrugInteractions = () => {
        if (!medications || medications.length < 2) return [];

        const interactions = [];
        const commonInteractions = {
            'insulin': {
                'beta-blockers': {
                    severity: 'moderate',
                    mechanism: 'Beta-blockers may mask hypoglycemia symptoms',
                    management: 'Monitor glucose more frequently, consider selective beta-blockers'
                },
                'ace-inhibitors': {
                    severity: 'mild',
                    mechanism: 'ACE inhibitors may enhance insulin sensitivity',
                    management: 'Monitor for hypoglycemia, may need insulin dose reduction'
                }
            },
            'metformin': {
                'contrast-dye': {
                    severity: 'severe',
                    mechanism: 'Risk of contrast-induced nephropathy and lactic acidosis',
                    management: 'Discontinue 48 hours before and after contrast procedures'
                },
                'alcohol': {
                    severity: 'moderate',
                    mechanism: 'Increased risk of lactic acidosis',
                    management: 'Avoid excessive alcohol consumption'
                }
            },
            'sulfonylureas': {
                'warfarin': {
                    severity: 'moderate',
                    mechanism: 'Enhanced anticoagulant effect',
                    management: 'Monitor INR more frequently'
                },
                'alcohol': {
                    severity: 'moderate',
                    mechanism: 'Increased hypoglycemia risk',
                    management: 'Avoid alcohol or monitor glucose closely'
                }
            }
        };

        // Check for interactions between current medications
        for (let i = 0; i < medications.length; i++) {
            for (let j = i + 1; j < medications.length; j++) {
                const med1 = medications[i].name?.toLowerCase() || '';
                const med2 = medications[j].name?.toLowerCase() || '';

                // Check if interaction exists
                Object.keys(commonInteractions).forEach(drug1 => {
                    if (med1.includes(drug1)) {
                        Object.keys(commonInteractions[drug1]).forEach(drug2 => {
                            if (med2.includes(drug2)) {
                                const interaction = commonInteractions[drug1][drug2];
                                interactions.push({
                                    drug1: medications[i].name,
                                    drug2: medications[j].name,
                                    severity: interaction.severity,
                                    mechanism: interaction.mechanism,
                                    management: interaction.management
                                });
                            }
                        });
                    }
                });
            }
        }

        return interactions;
    };

    const generateCareProtocols = () => {
        const protocols = [];

        if (!patientData) return protocols;

        const condition = patientData.condition || '';
        const age = patientData.age || 0;

        // Diabetes care protocols
        protocols.push({
            id: 'annual-eye-exam',
            title: 'Annual Dilated Eye Examination',
            description: 'Screen for diabetic retinopathy and macular edema',
            frequency: 'Annually',
            nextDue: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            evidence: 'ADA Standards of Care 2023',
            urgency: age > 40 ? 'high' : 'medium'
        });

        protocols.push({
            id: 'foot-exam',
            title: 'Comprehensive Foot Examination',
            description: 'Assess for neuropathy, vascular disease, and ulcerations',
            frequency: 'Annually or more frequent if high risk',
            nextDue: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            evidence: 'ADA Standards of Care 2023',
            urgency: 'medium'
        });

        protocols.push({
            id: 'kidney-function',
            title: 'Kidney Function Assessment',
            description: 'Monitor eGFR and urine albumin for diabetic nephropathy',
            frequency: 'Annually',
            nextDue: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toLocaleDateString(),
            evidence: 'KDIGO Guidelines 2020',
            urgency: 'high'
        });

        if (age > 40 || condition.includes('Type 1')) {
            protocols.push({
                id: 'lipid-profile',
                title: 'Lipid Profile Screening',
                description: 'Screen for cardiovascular risk factors',
                frequency: 'Annually if normal, more frequent if abnormal',
                nextDue: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toLocaleDateString(),
                evidence: 'AHA/ACC Guidelines',
                urgency: 'medium'
            });
        }

        return protocols;
    };

    const loadClinicalGuidelines = () => {
        return [
            {
                organization: 'American Diabetes Association',
                title: 'Standards of Medical Care in Diabetes-2023',
                section: 'Glycemic Targets',
                guideline: 'A1C <7% for most adults, individualize based on patient factors',
                url: 'https://diabetesjournals.org/care/issue/46/Supplement_1'
            },
            {
                organization: 'International Consensus',
                title: 'Time in Range Consensus',
                section: 'CGM Metrics',
                guideline: 'Target >70% time in range (70-180 mg/dL) for most adults',
                url: 'https://care.diabetesjournals.org/content/42/8/1593'
            },
            {
                organization: 'EASD/ADA',
                title: 'Management of Type 2 Diabetes',
                section: 'Treatment Algorithm',
                guideline: 'Individualized approach based on patient characteristics',
                url: 'https://diabetesjournals.org/care/article/42/12/2200/40768/Management-of-Hyperglycemia-in-Type-2-Diabetes'
            }
        ];
    };

    const renderOverview = () => (
        <div className="cds-overview">
            <div className="overview-grid">
                <div className="overview-card alerts">
                    <h4>🚨 Active Alerts</h4>
                    <div className="metric-value">{alerts.length}</div>
                    <div className="metric-breakdown">
                        <span className="high-priority">
                            {alerts.filter(a => a.priority === 'high').length} High
                        </span>
                        <span className="medium-priority">
                            {alerts.filter(a => a.priority === 'medium').length} Medium
                        </span>
                    </div>
                </div>

                <div className="overview-card recommendations">
                    <h4>💡 Recommendations</h4>
                    <div className="metric-value">{recommendations.length}</div>
                    <div className="metric-breakdown">
                        {recommendations.slice(0, 2).map((rec, idx) => (
                            <span key={idx} className="recommendation-preview">
                                {rec.title}
                            </span>
                        ))}
                    </div>
                </div>

                <div className="overview-card interactions">
                    <h4>⚠️ Drug Interactions</h4>
                    <div className="metric-value">{drugInteractions.length}</div>
                    <div className="metric-breakdown">
                        {drugInteractions.length > 0 ? (
                            <span className="interaction-warning">Review Required</span>
                        ) : (
                            <span className="no-interactions">None Detected</span>
                        )}
                    </div>
                </div>

                <div className="overview-card protocols">
                    <h4>📋 Care Protocols</h4>
                    <div className="metric-value">{careProtocols.length}</div>
                    <div className="metric-breakdown">
                        <span className="due-soon">
                            {careProtocols.filter(p => p.urgency === 'high').length} Due Soon
                        </span>
                    </div>
                </div>
            </div>
        </div>
    );

    const renderAlerts = () => (
        <div className="alerts-section">
            <h3>🚨 Clinical Alerts</h3>
            {alerts.length === 0 ? (
                <div className="no-alerts">
                    <p>✅ No active clinical alerts</p>
                    <p>Patient parameters are within acceptable ranges.</p>
                </div>
            ) : (
                <div className="alerts-list">
                    {alerts.map(alert => (
                        <div key={alert.id} className={`alert-card ${alert.type} ${alert.priority}`}>
                            <div className="alert-header">
                                <div className="alert-title">
                                    <h4>{alert.title}</h4>
                                    <span className={`priority-badge ${alert.priority}`}>
                                        {alert.priority}
                                    </span>
                                </div>
                                <span className="alert-category">{alert.category}</span>
                            </div>

                            <div className="alert-content">
                                <p className="alert-message">{alert.message}</p>

                                <div className="alert-recommendations">
                                    <h5>Recommendations:</h5>
                                    <ul>
                                        {alert.recommendations.map((rec, idx) => (
                                            <li key={idx}>{rec}</li>
                                        ))}
                                    </ul>
                                </div>

                                <div className="alert-evidence">
                                    <span>Evidence: {alert.evidence}</span>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );

    const renderRecommendations = () => (
        <div className="recommendations-section">
            <h3>💡 Treatment Recommendations</h3>
            <div className="recommendations-list">
                {recommendations.map((rec, idx) => (
                    <div key={idx} className="recommendation-card">
                        <div className="recommendation-header">
                            <h4>{rec.title}</h4>
                            <span className="recommendation-category">{rec.category}</span>
                        </div>

                        <div className="recommendation-content">
                            <p className="recommendation-description">{rec.description}</p>

                            <div className="recommendation-rationale">
                                <h5>Clinical Rationale:</h5>
                                <p>{rec.rationale}</p>
                            </div>

                            {rec.contraindications && (
                                <div className="contraindications">
                                    <h5>Contraindications:</h5>
                                    <ul>
                                        {rec.contraindications.map((contra, cidx) => (
                                            <li key={cidx}>{contra}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}

                            <div className="monitoring-requirements">
                                <h5>Monitoring:</h5>
                                <p>{rec.monitoring}</p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );

    const renderDrugInteractions = () => (
        <div className="interactions-section">
            <h3>⚠️ Drug Interactions</h3>
            {drugInteractions.length === 0 ? (
                <div className="no-interactions">
                    <p>✅ No significant drug interactions detected</p>
                    <p>Current medications appear compatible.</p>
                </div>
            ) : (
                <div className="interactions-list">
                    {drugInteractions.map((interaction, idx) => (
                        <div key={idx} className={`interaction-card ${interaction.severity}`}>
                            <div className="interaction-header">
                                <h4>{interaction.drug1} ↔ {interaction.drug2}</h4>
                                <span className={`severity-badge ${interaction.severity}`}>
                                    {interaction.severity}
                                </span>
                            </div>

                            <div className="interaction-content">
                                <div className="interaction-mechanism">
                                    <h5>Mechanism:</h5>
                                    <p>{interaction.mechanism}</p>
                                </div>

                                <div className="interaction-management">
                                    <h5>Management:</h5>
                                    <p>{interaction.management}</p>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );

    const renderCareProtocols = () => (
        <div className="protocols-section">
            <h3>📋 Care Protocols & Screenings</h3>
            <div className="protocols-list">
                {careProtocols.map(protocol => (
                    <div key={protocol.id} className={`protocol-card ${protocol.urgency}`}>
                        <div className="protocol-header">
                            <h4>{protocol.title}</h4>
                            <span className={`urgency-badge ${protocol.urgency}`}>
                                {protocol.urgency} priority
                            </span>
                        </div>

                        <div className="protocol-content">
                            <p className="protocol-description">{protocol.description}</p>

                            <div className="protocol-details">
                                <div className="protocol-frequency">
                                    <strong>Frequency:</strong> {protocol.frequency}
                                </div>
                                <div className="protocol-due">
                                    <strong>Next Due:</strong> {protocol.nextDue}
                                </div>
                                <div className="protocol-evidence">
                                    <strong>Evidence:</strong> {protocol.evidence}
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );

    const renderGuidelines = () => (
        <div className="guidelines-section">
            <h3>📚 Clinical Guidelines</h3>
            <div className="guidelines-list">
                {clinicalGuidelines.map((guideline, idx) => (
                    <div key={idx} className="guideline-card">
                        <div className="guideline-header">
                            <h4>{guideline.title}</h4>
                            <span className="guideline-organization">{guideline.organization}</span>
                        </div>

                        <div className="guideline-content">
                            <div className="guideline-section">
                                <strong>{guideline.section}:</strong>
                            </div>
                            <p className="guideline-text">{guideline.guideline}</p>

                            {guideline.url && (
                                <a
                                    href={guideline.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="guideline-link"
                                >
                                    📖 View Full Guideline
                                </a>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );

    return (
        <div className="clinical-decision-support">
            <div className="cds-header">
                <h2>🏥 Clinical Decision Support</h2>
                <p>Evidence-based alerts, recommendations, and clinical guidelines</p>
            </div>

            <div className="cds-navigation">
                <button
                    className={selectedView === 'overview' ? 'active' : ''}
                    onClick={() => setSelectedView('overview')}
                >
                    📊 Overview
                </button>
                <button
                    className={selectedView === 'alerts' ? 'active' : ''}
                    onClick={() => setSelectedView('alerts')}
                >
                    🚨 Alerts ({alerts.length})
                </button>
                <button
                    className={selectedView === 'recommendations' ? 'active' : ''}
                    onClick={() => setSelectedView('recommendations')}
                >
                    💡 Recommendations ({recommendations.length})
                </button>
                <button
                    className={selectedView === 'interactions' ? 'active' : ''}
                    onClick={() => setSelectedView('interactions')}
                >
                    ⚠️ Drug Interactions ({drugInteractions.length})
                </button>
                <button
                    className={selectedView === 'protocols' ? 'active' : ''}
                    onClick={() => setSelectedView('protocols')}
                >
                    📋 Care Protocols ({careProtocols.length})
                </button>
                <button
                    className={selectedView === 'guidelines' ? 'active' : ''}
                    onClick={() => setSelectedView('guidelines')}
                >
                    📚 Guidelines
                </button>
            </div>

            <div className="cds-content">
                {selectedView === 'overview' && renderOverview()}
                {selectedView === 'alerts' && renderAlerts()}
                {selectedView === 'recommendations' && renderRecommendations()}
                {selectedView === 'interactions' && renderDrugInteractions()}
                {selectedView === 'protocols' && renderCareProtocols()}
                {selectedView === 'guidelines' && renderGuidelines()}
            </div>
        </div>
    );
};

export default ClinicalDecisionSupport;
