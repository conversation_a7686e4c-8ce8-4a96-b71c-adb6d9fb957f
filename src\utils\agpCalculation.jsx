// AGP (Ambulatory Glucose Profile) calculation utility
// Based on historical implementation from FastAPI backend

// Default glucose thresholds for AGP analysis
export const DEFAULT_THRESHOLDS = {
  HYPO: 70,      // Below 70 mg/dL is considered hypoglycemic
  HYPER: 180,    // Above 180 mg/dL is considered hyperglycemic
  TARGET_LOW: 70,
  TARGET_HIGH: 180,
  VERY_LOW: 54,  // Very low glucose
  LOW: 70,       // Low glucose
  HIGH: 250,     // High glucose
  VERY_HIGH: 400 // Very high glucose
};

// Default glucose ranges for Time-in-Range calculations
export const DEFAULT_RANGES = {
  veryLow: { min: 0, max: 54, label: 'Very Low (<54)', color: '#dc2626' },
  low: { min: 54, max: 70, label: 'Low (54-70)', color: '#f59e0b' },
  target: { min: 70, max: 180, label: 'Target Range (70-180)', color: '#10b981' },
  high: { min: 180, max: 250, label: 'High (180-250)', color: '#f59e0b' },
  veryHigh: { min: 250, max: 400, label: 'Very High (>250)', color: '#dc2626' }
};

// Helper functions
function calculateMedian(values) {
  const sorted = values.slice().sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);
  return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
}

function calculatePercentile(values, percentile) {
  const sorted = values.slice().sort((a, b) => a - b);
  const index = (percentile / 100) * (sorted.length - 1);
  const lower = Math.floor(index);
  const upper = Math.ceil(index);

  if (lower === upper) {
    return sorted[lower];
  }

  const weight = index - lower;
  return sorted[lower] * (1 - weight) + sorted[upper] * weight;
}

// Calculate Time-in-Range (TIR) Statistics - CRITICAL AGP FEATURE
export function calculateTimeInRange(data, customRanges = null) {
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.warn("calculateTimeInRange received invalid data:", data);
    return null;
  }

  const ranges = customRanges || DEFAULT_RANGES;

  // Extract glucose values safely
  const glucoseValues = data.map(reading => {
    let glucose = reading.glucose || reading['g.glucose'] || reading['glucose.value'];

    // Handle Neo4j Integer objects
    if (typeof glucose === 'object' && glucose !== null && typeof glucose.toNumber === 'function') {
      glucose = glucose.toNumber();
    }

    return typeof glucose === 'number' && !isNaN(glucose) ? glucose : null;
  }).filter(g => g !== null);

  if (glucoseValues.length === 0) {
    console.warn("No valid glucose values found for Time-in-Range calculation");
    return null;
  }

  const totalReadings = glucoseValues.length;
  const stats = {};

  // Count readings in each range
  for (const [key, range] of Object.entries(ranges)) {
    const count = glucoseValues.filter(glucose =>
      glucose >= range.min && glucose <= range.max
    ).length;

    stats[key] = {
      count,
      percentage: ((count / totalReadings) * 100).toFixed(1),
      label: range.label,
      color: range.color
    };
  }

  return {
    totalReadings,
    ranges: stats,
    targetRangePercentage: stats.target?.percentage || 0
  };
}

// Calculate Glucose Management Indicator (GMI) - CRITICAL AGP FEATURE
export function calculateGMI(data) {
  if (!data || data.length === 0) return null;

  const glucoseValues = data.map(reading => {
    return typeof reading.glucose === 'number' ? reading.glucose : reading['g.glucose'];
  }).filter(g => g && !isNaN(g));

  if (glucoseValues.length === 0) return null;

  // Calculate mean glucose
  const meanGlucose = glucoseValues.reduce((sum, g) => sum + g, 0) / glucoseValues.length;

  // Convert to GMI using the formula: GMI = 3.31 + (0.02392 × mean glucose in mg/dL)
  const gmi = 3.31 + (0.02392 * meanGlucose);

  return {
    gmi: gmi.toFixed(1),
    meanGlucose: meanGlucose.toFixed(1),
    estimatedA1C: gmi.toFixed(1) // GMI is equivalent to estimated A1C
  };
}

// Calculate AGP Summary Statistics
export function calculateAGPSummary(data) {
  if (!data || data.length === 0) return null;

  const glucoseValues = data.map(reading => {
    return typeof reading.glucose === 'number' ? reading.glucose : reading['g.glucose'];
  }).filter(g => g && !isNaN(g));

  if (glucoseValues.length === 0) return null;

  const sorted = glucoseValues.slice().sort((a, b) => a - b);
  const mean = glucoseValues.reduce((sum, g) => sum + g, 0) / glucoseValues.length;
  const variance = glucoseValues.reduce((sum, g) => sum + Math.pow(g - mean, 2), 0) / glucoseValues.length;
  const standardDeviation = Math.sqrt(variance);

  // Calculate coefficient of variation (CV) - important diabetes metric
  const coefficientOfVariation = (standardDeviation / mean) * 100;

  return {
    count: glucoseValues.length,
    mean: mean.toFixed(1),
    median: calculateMedian(glucoseValues).toFixed(1),
    standardDeviation: standardDeviation.toFixed(1),
    coefficientOfVariation: coefficientOfVariation.toFixed(1),
    minimum: Math.min(...glucoseValues),
    maximum: Math.max(...glucoseValues),
    range: Math.max(...glucoseValues) - Math.min(...glucoseValues),
    percentile25: calculatePercentile(glucoseValues, 25).toFixed(1),
    percentile75: calculatePercentile(glucoseValues, 75).toFixed(1)
  };
}

// Analyze daily patterns
export function analyzeDailyPatterns(data) {
  if (!data || data.length === 0) return null;

  const patterns = {
    dawn: [], // 4-8 AM
    morning: [], // 8-12 PM
    afternoon: [], // 12-6 PM
    evening: [], // 6-10 PM
    night: [] // 10 PM - 4 AM
  };

  for (const reading of data) {
    const timestamp = typeof reading.timestamp === 'string'
      ? new Date(reading.timestamp)
      : reading.timestamp;

    if (!timestamp || !(timestamp instanceof Date)) continue;

    const hour = timestamp.getHours();
    const glucose = typeof reading.glucose === 'number' ? reading.glucose : reading['g.glucose'];

    if (!glucose || isNaN(glucose)) continue;

    if (hour >= 4 && hour < 8) patterns.dawn.push(glucose);
    else if (hour >= 8 && hour < 12) patterns.morning.push(glucose);
    else if (hour >= 12 && hour < 18) patterns.afternoon.push(glucose);
    else if (hour >= 18 && hour < 22) patterns.evening.push(glucose);
    else patterns.night.push(glucose);
  }

  const summary = {};
  for (const [period, values] of Object.entries(patterns)) {
    if (values.length > 0) {
      const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
      const sorted = values.slice().sort((a, b) => a - b);
      summary[period] = {
        count: values.length,
        mean: mean.toFixed(1),
        median: calculateMedian(values).toFixed(1),
        min: Math.min(...values),
        max: Math.max(...values),
        standardDeviation: Math.sqrt(
          values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length
        ).toFixed(1)
      };
    } else {
      summary[period] = null;
    }
  }

  return summary;
}

// Main AGP calculation function that combines all the above
export function calculateAGPData(records) {
  if (!records || !Array.isArray(records) || records.length === 0) {
    console.info("calculate_agp_data received no valid records array.");
    return null;
  }

  try {
    // Calculate all AGP components
    const timeInRange = calculateTimeInRange(records);
    const gmi = calculateGMI(records);
    const summary = calculateAGPSummary(records);
    const dailyPatterns = analyzeDailyPatterns(records);

    return {
      timeInRange,
      gmi,
      summary,
      dailyPatterns,
      recordCount: records.length,
      calculatedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error("Error calculating AGP data:", error);
    return null;
  }
}
