import React, { useState, useEffect } from 'react';
import './InputModal.css';

const InputModal = ({ 
    isOpen, 
    title, 
    message, 
    placeholder = '',
    defaultValue = '',
    confirmText = 'Save', 
    cancelText = 'Cancel',
    onConfirm, 
    onCancel,
    required = false
}) => {
    const [inputValue, setInputValue] = useState(defaultValue);

    useEffect(() => {
        if (isOpen) {
            setInputValue(defaultValue);
        }
    }, [isOpen, defaultValue]);

    if (!isOpen) return null;

    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget) {
            onCancel();
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
            onCancel();
        } else if (e.key === 'Enter' && (!required || inputValue.trim())) {
            onConfirm(inputValue.trim());
        }
    };

    const handleConfirm = () => {
        if (required && !inputValue.trim()) {
            return;
        }
        onConfirm(inputValue.trim());
    };

    useEffect(() => {
        if (isOpen) {
            document.addEventListener('keydown', handleKeyDown);
            // Focus the input
            const input = document.querySelector('.input-modal-input');
            if (input) {
                input.focus();
                input.select();
            }
            
            return () => {
                document.removeEventListener('keydown', handleKeyDown);
            };
        }
    }, [isOpen, inputValue]);

    return (
        <div 
            className="input-modal-overlay" 
            onClick={handleOverlayClick}
            role="dialog"
            aria-modal="true"
            aria-labelledby="input-modal-title"
            aria-describedby="input-modal-message"
        >
            <div className="input-modal">
                <div className="input-modal-header">
                    <h3 id="input-modal-title" className="input-modal-title">
                        {title}
                    </h3>
                    <button 
                        className="input-modal-close"
                        onClick={onCancel}
                        aria-label="Close modal"
                    >
                        ×
                    </button>
                </div>
                
                <div className="input-modal-body">
                    {message && (
                        <p id="input-modal-message" className="input-modal-message">
                            {message}
                        </p>
                    )}
                    <input
                        type="text"
                        className="input-modal-input"
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder={placeholder}
                        required={required}
                    />
                    {required && !inputValue.trim() && (
                        <small className="input-modal-error">This field is required</small>
                    )}
                </div>
                
                <div className="input-modal-actions">
                    <button 
                        className="input-modal-button cancel-button"
                        onClick={onCancel}
                    >
                        {cancelText}
                    </button>
                    <button 
                        className={`input-modal-button confirm-button ${(!required || inputValue.trim()) ? 'primary' : 'disabled'}`}
                        onClick={handleConfirm}
                        disabled={required && !inputValue.trim()}
                    >
                        {confirmText}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default InputModal;