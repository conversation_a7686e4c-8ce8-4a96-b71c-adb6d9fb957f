import {
    CategoryScale,
    Chart as ChartJS,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    Tooltip,
} from 'chart.js';
import { useEffect, useRef, useState } from 'react';
import { Line } from 'react-chartjs-2';
import './ECGWaveformViewer.css';

ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip,
    Legend
);

const ECGWaveformViewer = ({
    ecgData,
    selectedLead = 'II',
    onLeadChange,
    showGrid = true,
    autoScale = true,
    samplingRate = 1000
}) => {
    const [displayData, setDisplayData] = useState(null);
    const [timeRange, setTimeRange] = useState({ start: 0, end: 10 }); // seconds
    const [amplitude, setAmplitude] = useState({ min: -2, max: 2 }); // mV
    const [isPlaying, setIsPlaying] = useState(false);
    const [playbackSpeed, setPlaybackSpeed] = useState(1);
    const [autoScaleState, setAutoScale] = useState(autoScale);
    const animationRef = useRef(null);

    // ECG Lead configurations
    const ECG_LEADS = [
        { name: 'I', group: 'Limb Leads', color: '#FF6384' },
        { name: 'II', group: 'Limb Leads', color: '#36A2EB' },
        { name: 'III', group: 'Limb Leads', color: '#FFCE56' },
        { name: 'aVR', group: 'Augmented Leads', color: '#4BC0C0' },
        { name: 'aVL', group: 'Augmented Leads', color: '#9966FF' },
        { name: 'aVF', group: 'Augmented Leads', color: '#FF9F40' },
        { name: 'V1', group: 'Precordial Leads', color: '#FF6384' },
        { name: 'V2', group: 'Precordial Leads', color: '#36A2EB' },
        { name: 'V3', group: 'Precordial Leads', color: '#FFCE56' },
        { name: 'V4', group: 'Precordial Leads', color: '#4BC0C0' },
        { name: 'V5', group: 'Precordial Leads', color: '#9966FF' },
        { name: 'V6', group: 'Precordial Leads', color: '#FF9F40' }
    ];

    // Chart options
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            title: {
                display: true,
                text: `ECG Lead ${selectedLead}`,
                font: {
                    size: 16,
                    weight: 'bold'
                }
            },
            tooltip: {
                mode: 'nearest',
                intersect: false,
                callbacks: {
                    title: (tooltipItems) => {
                        const time = parseFloat(tooltipItems[0].label);
                        return `Time: ${time.toFixed(3)}s`;
                    },
                    label: (context) => {
                        return `Amplitude: ${context.parsed.y.toFixed(3)} mV`;
                    }
                }
            }
        },
        scales: {
            x: {
                type: 'linear',
                position: 'bottom',
                title: {
                    display: true,
                    text: 'Time (seconds)'
                },
                min: timeRange.start,
                max: timeRange.end,
                grid: {
                    display: showGrid,
                    color: '#E0E0E0',
                    lineWidth: 1
                },
                ticks: {
                    stepSize: 0.2 // 200ms intervals
                }
            },
            y: {
                title: {
                    display: true,
                    text: 'Amplitude (mV)'
                },
                min: autoScaleState ? undefined : amplitude.min,
                max: autoScaleState ? undefined : amplitude.max,
                grid: {
                    display: showGrid,
                    color: '#E0E0E0',
                    lineWidth: 1
                },
                ticks: {
                    stepSize: 0.5
                }
            }
        },
        elements: {
            point: {
                radius: 0 // Hide individual points for smooth line
            },
            line: {
                tension: 0 // Sharp lines for ECG
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        },
        animation: {
            duration: 0 // Disable animations for real-time feel
        }
    };

    // Process ECG data for display
    useEffect(() => {
        if (!ecgData || !ecgData.length) return;

        // Find data for selected lead
        const leadData = ecgData.find(lead => lead.leadName === selectedLead);
        if (!leadData || !leadData.samples) return;

        // Convert samples to time-series data
        const samples = Array.isArray(leadData.samples) ? leadData.samples : [];
        const timeStep = 1 / samplingRate; // Time between samples

        const chartData = samples.map((amplitude, index) => ({
            x: index * timeStep,
            y: amplitude
        }));

        // Filter data based on time range
        const filteredData = chartData.filter(
            point => point.x >= timeRange.start && point.x <= timeRange.end
        );

        setDisplayData({
            datasets: [
                {
                    label: `Lead ${selectedLead}`,
                    data: filteredData,
                    borderColor: ECG_LEADS.find(lead => lead.name === selectedLead)?.color || '#36A2EB',
                    backgroundColor: 'transparent',
                    borderWidth: 1.5,
                    pointRadius: 0,
                    pointHoverRadius: 3
                }
            ]
        });
    }, [ecgData, selectedLead, timeRange, samplingRate]);

    // Handle lead selection
    const handleLeadChange = (leadName) => {
        if (onLeadChange) {
            onLeadChange(leadName);
        }
    };

    // Time range controls
    const handleTimeRangeChange = (start, end) => {
        setTimeRange({ start: Math.max(0, start), end });
    };

    // Playback animation
    useEffect(() => {
        if (!isPlaying || !ecgData?.length) return;

        const animate = () => {
            setTimeRange(prev => {
                const duration = prev.end - prev.start;
                const newStart = prev.start + (0.1 * playbackSpeed);
                const maxTime = 30; // Maximum ECG duration

                if (newStart + duration > maxTime) {
                    return { start: 0, end: duration };
                }

                return { start: newStart, end: newStart + duration };
            });

            animationRef.current = requestAnimationFrame(animate);
        };

        animationRef.current = requestAnimationFrame(animate);

        return () => {
            if (animationRef.current) {
                cancelAnimationFrame(animationRef.current);
            }
        };
    }, [isPlaying, playbackSpeed, ecgData]);

    // Calculate ECG metrics for display
    const calculateECGMetrics = () => {
        if (!displayData?.datasets?.[0]?.data?.length) return {};

        const data = displayData.datasets[0].data;
        const amplitudes = data.map(point => point.y);

        return {
            peak: Math.max(...amplitudes).toFixed(3),
            trough: Math.min(...amplitudes).toFixed(3),
            range: (Math.max(...amplitudes) - Math.min(...amplitudes)).toFixed(3),
            samples: amplitudes.length
        };
    };

    const metrics = calculateECGMetrics();

    // Group leads by category
    const leadGroups = ECG_LEADS.reduce((groups, lead) => {
        if (!groups[lead.group]) groups[lead.group] = [];
        groups[lead.group].push(lead);
        return groups;
    }, {});

    return (
        <div className="ecg-waveform-viewer">
            <div className="ecg-controls">
                <div className="lead-selector">
                    <h4>ECG Leads</h4>
                    {Object.entries(leadGroups).map(([groupName, leads]) => (
                        <div key={groupName} className="lead-group">
                            <span className="lead-group-name">{groupName}</span>
                            <div className="lead-buttons">
                                {leads.map(lead => (
                                    <button
                                        key={lead.name}
                                        className={`lead-button ${selectedLead === lead.name ? 'active' : ''}`}
                                        onClick={() => handleLeadChange(lead.name)}
                                        style={{
                                            borderColor: lead.color,
                                            backgroundColor: selectedLead === lead.name ? lead.color : 'transparent',
                                            color: selectedLead === lead.name ? 'white' : lead.color
                                        }}
                                    >
                                        {lead.name}
                                    </button>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>

                <div className="display-controls">
                    <div className="time-controls">
                        <h4>Time Range</h4>
                        <label>
                            Start (s):
                            <input
                                type="number"
                                value={timeRange.start}
                                onChange={(e) => handleTimeRangeChange(parseFloat(e.target.value), timeRange.end)}
                                step="0.1"
                                min="0"
                                max="30"
                            />
                        </label>
                        <label>
                            Duration (s):
                            <input
                                type="number"
                                value={timeRange.end - timeRange.start}
                                onChange={(e) => handleTimeRangeChange(timeRange.start, timeRange.start + parseFloat(e.target.value))}
                                step="1"
                                min="1"
                                max="30"
                            />
                        </label>
                    </div>

                    <div className="playback-controls">
                        <h4>Playback</h4>
                        <button
                            className={`play-button ${isPlaying ? 'playing' : ''}`}
                            onClick={() => setIsPlaying(!isPlaying)}
                        >
                            {isPlaying ? '⏸️ Pause' : '▶️ Play'}
                        </button>
                        <label>
                            Speed:
                            <select
                                value={playbackSpeed}
                                onChange={(e) => setPlaybackSpeed(parseFloat(e.target.value))}
                            >
                                <option value="0.25">0.25x</option>
                                <option value="0.5">0.5x</option>
                                <option value="1">1x</option>
                                <option value="2">2x</option>
                                <option value="4">4x</option>
                            </select>
                        </label>
                    </div>

                    <div className="display-options">
                        <h4>Display</h4>
                        <label>
                            <input
                                type="checkbox"
                                checked={showGrid}
                                onChange={(e) => {
                                    // Grid toggle would need to be passed up to parent
                                }}
                            />
                            Show Grid
                        </label>
                        <label>
                            <input
                                type="checkbox"
                                checked={autoScaleState}
                                onChange={(e) => setAutoScale(e.target.checked)}
                            />
                            Auto Scale
                        </label>
                    </div>
                </div>
            </div>

            <div className="ecg-chart-container">
                {displayData ? (
                    <Line data={displayData} options={chartOptions} />
                ) : (
                    <div className="no-data">
                        <p>No ECG data available for lead {selectedLead}</p>
                        <p>Please select a different lead or check your data source.</p>
                    </div>
                )}
            </div>

            <div className="ecg-metrics">
                <h4>Waveform Metrics</h4>
                <div className="metrics-grid">
                    <div className="metric">
                        <span className="metric-label">Peak:</span>
                        <span className="metric-value">{metrics.peak || 'N/A'} mV</span>
                    </div>
                    <div className="metric">
                        <span className="metric-label">Trough:</span>
                        <span className="metric-value">{metrics.trough || 'N/A'} mV</span>
                    </div>
                    <div className="metric">
                        <span className="metric-label">Range:</span>
                        <span className="metric-value">{metrics.range || 'N/A'} mV</span>
                    </div>
                    <div className="metric">
                        <span className="metric-label">Samples:</span>
                        <span className="metric-value">{metrics.samples || 'N/A'}</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ECGWaveformViewer;
