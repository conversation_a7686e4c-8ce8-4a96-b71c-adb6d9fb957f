/**
 * Comprehensive Prompt Testing Component for HealthHub Research Platform
 *
 * Features:
 * - Multi-technique prompt testing
 * - Performance comparison and analytics
 * - Test suite management
 * - A/B testing capabilities
 * - Visual results comparison
 * - Export/import test results
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import './PromptTesting.css';

// Constants for delays to avoid magic numbers
const ITERATION_DELAY_MS = 500;
const TOKEN_ESTIMATION_CHARS_PER_TOKEN = 4;

// Constants for complexity level mapping
const COMPLEXITY_LEVELS = {
    low: { percentage: 33, label: 'Low' },
    medium: { percentage: 66, label: 'Medium' },
    high: { percentage: 100, label: 'High' }
};

// Utility function for technique name transformation
const formatTechniqueName = (technique) => {
    return technique.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

// Utility function for safe property access with rounding
const safeRound = (value, defaultValue = 0) => {
    return Math.round(value || defaultValue);
};

const PromptTesting = () => {
    // Core state
    const [testConfig, setTestConfig] = useState({
        patientData: {
            name: 'Test Patient',
            age: 45,
            gender: 'Female',
            diabetesType: 'Type 2',
            diagnosisDate: '2020-01-15',
            currentMedications: ['Metformin', 'Insulin'],
            recentGlucoseData: {
                averageGlucose: 165,
                timeInRange: 58,
                variability: 'moderate'
            },
            lifestyle: {
                exercise: 'light',
                diet: 'moderate carb',
                stress: 'moderate'
            },
            challenges: ['Post-meal spikes', 'Dawn phenomenon'],
            goals: ['Improve time in range', 'Reduce A1C']
        },
        techniques: {
            structured: true,
            conversational: true,
            motivational: false,
            clinical: false,
            zero_shot: false,
            one_shot: false,
            few_shot: false,
            chain_of_thought: false,
            tree_of_thought: false,
            self_consistency: false,
            react: false,
            role_playing: false
        },
        customPrompt: '',
        testIterations: 1,
        compareBaseline: true
    });

    const [testResults, setTestResults] = useState([]);
    const [currentTest, setCurrentTest] = useState(null);
    const [isRunning, setIsRunning] = useState(false);
    const [testHistory, setTestHistory] = useState([]);
    const [selectedResults, setSelectedResults] = useState([]);
    const [viewMode, setViewMode] = useState('setup'); // setup, running, results, comparison, analytics
    const [testSuites, setTestSuites] = useState([]);
    const [currentSuite, setCurrentSuite] = useState(null);
    const [notification, setNotification] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, type: '', data: {} });

    // Ref for file input - avoids imperative DOM access (e.g., document.getElementById or querySelector) by using React's useRef
    const fileInputRef = useRef(null);

    // Load test history from localStorage
    useEffect(() => {
        const savedHistory = localStorage.getItem('promptTestingHistory');
        if (savedHistory) {
            setTestHistory(JSON.parse(savedHistory));
        }

        const savedSuites = localStorage.getItem('promptTestingSuites');
        if (savedSuites) {
            setTestSuites(JSON.parse(savedSuites));
        }
    }, []);

    // Save test history
    useEffect(() => {
        if (testHistory.length > 0) {
            localStorage.setItem('promptTestingHistory', JSON.stringify(testHistory));
        }
    }, [testHistory]);

    // Save test suites
    useEffect(() => {
        if (testSuites.length > 0) {
            localStorage.setItem('promptTestingSuites', JSON.stringify(testSuites));
        }
    }, [testSuites]);

    // Get selected techniques
    const selectedTechniques = useMemo(() => {
        return Object.entries(testConfig.techniques)
            .filter(([_, enabled]) => enabled)
            .map(([technique, _]) => technique);
    }, [testConfig.techniques]);

    // Show notification
    const showNotification = (message, type = 'info') => {
        setNotification({ message, type });
        setTimeout(() => setNotification(null), 5000);
    };

    // Run prompt testing
    const runPromptTest = useCallback(async () => {
        if (selectedTechniques.length === 0) {
            showNotification('Please select at least one prompting technique', 'warning');
            return;
        }

        setIsRunning(true);
        setViewMode('running');
        const testId = `test_${Date.now()}`;

        const newTest = {
            id: testId,
            timestamp: new Date().toISOString(),
            config: { ...testConfig },
            results: {},
            status: 'running',
            progress: 0,
            totalTechniques: selectedTechniques.length
        };

        setCurrentTest(newTest);
        const results = {};

        try {
            for (let i = 0; i < selectedTechniques.length; i++) {
                const technique = selectedTechniques[i];

                // Update progress
                setCurrentTest(prev => ({
                    ...prev,
                    progress: ((i / selectedTechniques.length) * 100),
                    currentTechnique: technique
                }));

                // Run test iterations for this technique
                const techniqueResults = [];

                for (let iteration = 0; iteration < testConfig.testIterations; iteration++) {
                    try {
                        const result = await runSingleTest(technique, testConfig.patientData, iteration);
                        techniqueResults.push(result);

                        // Small delay between iterations
                        await new Promise(resolve => setTimeout(resolve, ITERATION_DELAY_MS));
                    } catch (error) {
                        console.error(`Error in iteration ${iteration} for ${technique}:`, error);
                        techniqueResults.push({
                            error: error.message,
                            iteration,
                            technique,
                            timestamp: new Date().toISOString()
                        });
                    }
                }

                results[technique] = {
                    technique,
                    iterations: techniqueResults,
                    averageScores: calculateAverageScores(techniqueResults),
                    consistency: calculateConsistency(techniqueResults),
                    reliability: calculateReliability(techniqueResults)
                };
            }

            const completedTest = {
                ...newTest,
                results,
                status: 'completed',
                progress: 100,
                completedAt: new Date().toISOString(),
                summary: generateTestSummary(results),
                bestTechnique: findBestTechnique(results),
                recommendations: generateTestRecommendations(results)
            };

            setCurrentTest(completedTest);
            setTestResults(Object.values(results));
            setTestHistory(prev => [completedTest, ...prev.slice(0, 19)]); // Keep last 20 tests
            setViewMode('results');

        } catch (error) {
            console.error('Test execution error:', error);
            setCurrentTest(prev => ({
                ...prev,
                status: 'error',
                error: error.message
            }));
        } finally {
            setIsRunning(false);
        }
    }, [selectedTechniques, testConfig]);

    // Run single test against API with performance optimization
    const runSingleTest = async (technique, patientData, iteration) => {
        return new Promise((resolve, reject) => {
            const performTest = async () => {
                try {
                    const startTime = performance.now();

                    // For custom prompts, use different endpoint
                    if (testConfig.customPrompt && testConfig.customPrompt.trim()) {
                        const response = await fetch('/api/ai', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                action: 'custom-prompt-test',
                                data: patientData,
                                context: {
                                    customPrompt: testConfig.customPrompt,
                                    technique: technique,
                                    iteration,
                                    testId: currentTest?.id || `temp_${Date.now()}_${iteration}`
                                }
                            })
                        });

                        if (!response.ok) {
                            reject(new Error(`API Error: ${response.status} ${response.statusText}`));
                            return;
                        }

                        const result = await response.json();
                        const endTime = performance.now();

                        resolve({
                            ...result,
                            technique: technique,
                            iteration,
                            responseTime: endTime - startTime,
                            timestamp: new Date().toISOString(),
                            tokenCount: estimateTokenCount(result.raw_response || ''),
                            wordCount: (result.raw_response || '').split(' ').length
                        });
                        return;
                    }

                    // Standard technique testing
                    const response = await fetch('/api/ai', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'patient-health-recommendations',
                            data: patientData,
                            context: {
                                promptingTechnique: technique,
                                iteration,
                                testId: currentTest?.id || `temp_${Date.now()}_${iteration}`,
                                customPrompt: testConfig.customPrompt || null
                            }
                        })
                    });

                    if (!response.ok) {
                        reject(new Error(`API Error: ${response.status} ${response.statusText}`));
                        return;
                    }

                    const result = await response.json();
                    const endTime = performance.now();

                    resolve({
                        ...result,
                        iteration,
                        responseTime: endTime - startTime,
                        timestamp: new Date().toISOString(),
                        tokenCount: estimateTokenCount(result.raw_response || ''),
                        wordCount: (result.raw_response || '').split(' ').length
                    });
                } catch (err) {
                    reject(err);
                }
            };

            // Use requestIdleCallback for performance optimization
            if ('requestIdleCallback' in window) {
                window.requestIdleCallback(performTest);
            } else {
                // Fallback for browsers without requestIdleCallback
                setTimeout(performTest, 0);
            }
        });
    };

    // Helper functions
    const calculateAverageScores = (results) => {
        const validResults = results.filter(r => !r.error);
        if (validResults.length === 0) return null;

        const scores = {
            effectiveness: 0,
            readability: 0,
            actionability: 0,
            reasoning_depth: 0,
            response_time: 0,
            token_count: 0
        };

        validResults.forEach(result => {
            scores.effectiveness += result.effectiveness_score || 0;
            scores.readability += result.readability_score || 0;
            scores.actionability += result.actionability_score || 0;
            scores.reasoning_depth += result.reasoning_depth || 0;
            scores.response_time += result.responseTime || 0;
            scores.token_count += result.tokenCount || 0;
        });

        Object.keys(scores).forEach(key => {
            scores[key] = scores[key] / validResults.length;
        });

        return scores;
    };

    const calculateConsistency = (results) => {
        const validResults = results.filter(r => !r.error);
        if (validResults.length <= 1) return 100;

        const scores = validResults.map(r => r.effectiveness_score || 0);
        const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        const standardDeviation = Math.sqrt(variance);

        // Lower standard deviation = higher consistency
        return Math.max(0, 100 - (standardDeviation * 2));
    };

    const calculateReliability = (results) => {
        const validResults = results.filter(r => !r.error);
        const errorRate = (results.length - validResults.length) / results.length * 100;

        return Math.max(0, 100 - errorRate);
    };

    const generateTestSummary = (results) => {
        const techniques = Object.keys(results);
        const totalTests = techniques.length;
        const successfulTests = techniques.filter(t =>
            results[t].iterations.some(iter => !iter.error)
        ).length;

        return {
            totalTechniques: totalTests,
            successfulTechniques: successfulTests,
            successRate: (successfulTests / totalTests) * 100,
            averageResponseTime: calculateOverallAverageResponseTime(results),
            recommendationCount: calculateTotalRecommendations(results)
        };
    };

    const findBestTechnique = (results) => {
        let bestTechnique = null;
        let highestScore = -1;

        Object.entries(results).forEach(([technique, result]) => {
            if (result.averageScores) {
                const compositeScore = (
                    (result.averageScores.effectiveness * 0.3) +
                    (result.averageScores.readability * 0.2) +
                    (result.averageScores.actionability * 0.25) +
                    (result.averageScores.reasoning_depth * 0.15) +
                    (result.consistency * 0.1)
                );

                if (compositeScore > highestScore) {
                    highestScore = compositeScore;
                    bestTechnique = {
                        technique,
                        score: compositeScore,
                        ...result
                    };
                }
            }
        });

        return bestTechnique;
    };

    const generateTestRecommendations = (results) => {
        const recommendations = [];

        // Performance recommendations
        const techniques = Object.keys(results);
        const avgResponseTimes = techniques.map(t =>
            results[t].averageScores?.response_time || 0
        );
        const fastestTime = Math.min(...avgResponseTimes);
        const slowestTime = Math.max(...avgResponseTimes);

        if (slowestTime > fastestTime * 2) {
            recommendations.push({
                type: 'performance',
                priority: 'medium',
                title: 'Response Time Optimization',
                description: 'Consider using faster techniques for real-time applications'
            });
        }

        // Consistency recommendations
        const consistencyScores = techniques.map(t => results[t].consistency || 0);
        const avgConsistency = consistencyScores.reduce((sum, score) => sum + score, 0) / consistencyScores.length;

        if (avgConsistency < 70) {
            recommendations.push({
                type: 'consistency',
                priority: 'high',
                title: 'Improve Response Consistency',
                description: 'Consider techniques with higher consistency scores for production use'
            });
        }

        return recommendations;
    };

    const calculateOverallAverageResponseTime = (results) => {
        const allTimes = Object.values(results)
            .map(r => r.averageScores?.response_time || 0)
            .filter(time => time > 0);

        return allTimes.length > 0
            ? allTimes.reduce((sum, time) => sum + time, 0) / allTimes.length
            : 0;
    };

    const calculateTotalRecommendations = (results) => {
        return Object.values(results)
            .flatMap(r => r.iterations || [])
            .filter(iter => !iter.error)
            .reduce((total, iter) => total + (iter.recommendations?.length || 0), 0);
    };

    const estimateTokenCount = (text) => {
        // More accurate token estimation considering word boundaries and whitespace
        if (!text || typeof text !== 'string') return 0;

        // Split by whitespace and count words, then apply token-to-word ratio
        const words = text.trim().split(/\s+/).filter(word => word.length > 0);

        // GPT models average roughly 1.3 tokens per word for English text
        // Account for punctuation and special characters
        const punctuationCount = (text.match(/[.,!?;:'"()[\]{}-]/g) || []).length;
        const tokenEstimate = Math.ceil((words.length * 1.3) + (punctuationCount * 0.5));

        return Math.max(tokenEstimate, Math.ceil(text.length / TOKEN_ESTIMATION_CHARS_PER_TOKEN));
    };

    // Save test suite
    const saveTestSuite = () => {
        setModalState({
            isOpen: true,
            type: 'input',
            data: {
                title: 'Save Test Suite',
                message: 'Enter test suite name:',
                onConfirm: (suiteName) => {
                    if (!suiteName?.trim()) {
                        showNotification('Please enter a valid suite name', 'warning');
                        return;
                    }

                    const newSuite = {
                        id: `suite_${Date.now()}`,
                        name: suiteName.trim(),
                        config: { ...testConfig },
                        createdAt: new Date().toISOString(),
                        tests: []
                    };

                    setTestSuites(prev => [...prev, newSuite]);
                    setCurrentSuite(newSuite);
                    showNotification(`Test suite "${suiteName}" saved successfully!`, 'success');
                    setModalState({ isOpen: false, type: '', data: {} });
                },
                onCancel: () => setModalState({ isOpen: false, type: '', data: {} })
            }
        });
    };

    // Load test suite
    const loadTestSuite = (suite) => {
        setTestConfig(suite.config);
        setCurrentSuite(suite);
    };

    // Export results
    const exportResults = () => {
        if (!currentTest) return;

        const exportData = {
            test: currentTest,
            exportedAt: new Date().toISOString(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `prompt-test-${currentTest.id}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    // Handle file input change for importing test results
    const handleFileImport = (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
                try {
                    const importedData = JSON.parse(event.target.result);

                    // Check if it's a valid test result JSON
                    if (importedData.test && importedData.test.id) {
                        const test = importedData.test;

                        // Add to test history
                        const updatedHistory = [test, ...testHistory.slice(0, 19)];
                        setTestHistory(updatedHistory);
                        localStorage.setItem('promptTestingHistory', JSON.stringify(updatedHistory));

                        // Set as current test and show results
                        setCurrentTest(test);
                        if (test.results) {
                            setTestResults(Object.values(test.results));
                        }
                        setViewMode('results');

                        showNotification('Test results imported successfully!', 'success');
                    } else {
                        showNotification('Invalid test result file format', 'error');
                    }
                } catch (error) {
                    showNotification('Failed to parse JSON file: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }
        // Reset the input value to allow selecting the same file again
        e.target.value = '';
    };

    // Import test results from JSON file - React best practice approach
    const importResults = () => {
        fileInputRef.current?.click();
    };

    return (
        <div className="prompt-testing">
            <div className="prompt-testing-header">
                <h2>🧪 Prompt Testing Laboratory</h2>
                <div className="header-actions">
                    <button onClick={saveTestSuite} disabled={isRunning}>
                        💾 Save Suite
                    </button>
                    <button onClick={importResults} disabled={isRunning}>
                        📁 Import Results
                    </button>
                    <button onClick={exportResults} disabled={!currentTest || isRunning}>
                        📊 Export Results
                    </button>
                    <select
                        value={viewMode}
                        onChange={(e) => setViewMode(e.target.value)}
                        disabled={isRunning}
                    >
                        <option value="setup">Setup</option>
                        <option value="results">Results</option>
                        <option value="comparison">Comparison</option>
                        <option value="analytics">Analytics</option>
                        <option value="history">History</option>
                    </select>
                </div>
            </div>

            {viewMode === 'setup' && (
                <TestSetupPanel
                    config={testConfig}
                    setConfig={setTestConfig}
                    testSuites={testSuites}
                    onLoadSuite={loadTestSuite}
                    onRunTest={runPromptTest}
                    isRunning={isRunning}
                    selectedTechniques={selectedTechniques}
                />
            )}

            {viewMode === 'running' && currentTest && (
                <TestRunningPanel
                    currentTest={currentTest}
                />
            )}

            {viewMode === 'results' && currentTest && (
                <TestResultsPanel
                    test={currentTest}
                    results={testResults}
                />
            )}

            {viewMode === 'comparison' && (
                <TestComparisonPanel
                    results={testResults}
                    selectedResults={selectedResults}
                    setSelectedResults={setSelectedResults}
                />
            )}

            {viewMode === 'analytics' && (
                <TestAnalyticsPanel
                    testHistory={testHistory}
                    currentTest={currentTest}
                />
            )}

            {viewMode === 'history' && (
                <TestHistoryPanel
                    testHistory={testHistory}
                    onSelectTest={(test) => {
                        setCurrentTest(test);
                        setTestResults(Object.values(test.results));
                        setViewMode('results');
                    }}
                />
            )}

            {/* Notification System */}
            {notification && (
                <div className={`notification ${notification.type}`}>
                    <span>{notification.message}</span>
                    <button onClick={() => setNotification(null)}>×</button>
                </div>
            )}

            {/* Modal System */}
            {modalState.isOpen && (
                <Modal
                    type={modalState.type}
                    data={modalState.data}
                    onClose={() => setModalState({ isOpen: false, type: '', data: {} })}
                />
            )}

            {/* Hidden file input for importing test results - React best practice */}
            <input
                type="file"
                accept=".json"
                ref={fileInputRef}
                onChange={handleFileImport}
                style={{ display: 'none' }}
            />
        </div>
    );
};

// Sub-components for different panels
const TestSetupPanel = ({ config, setConfig, testSuites, onLoadSuite, onRunTest, isRunning, selectedTechniques }) => (
    <div className="test-setup-panel">
        <div className="setup-grid">
            <div className="patient-config">
                <h3>👤 Patient Configuration</h3>
                <PatientDataEditor
                    patientData={config.patientData}
                    onChange={(data) => setConfig(prev => ({ ...prev, patientData: data }))}
                />
            </div>

            <div className="technique-selection">
                <h3>🔬 Prompting Techniques</h3>
                <TechniqueSelector
                    techniques={config.techniques}
                    onChange={(techniques) => setConfig(prev => ({ ...prev, techniques }))}
                />
                <div className="selected-count">
                    Selected: {selectedTechniques.length} techniques
                </div>
            </div>

            <div className="test-configuration">
                <h3>⚙️ Test Configuration</h3>
                <TestConfigEditor
                    config={config}
                    onChange={setConfig}
                />
            </div>

            <div className="test-suites">
                <h3>💼 Test Suites</h3>
                <TestSuiteManager
                    suites={testSuites}
                    onLoad={onLoadSuite}
                />
            </div>
        </div>

        <div className="setup-actions">
            <button
                className="run-test-btn"
                onClick={onRunTest}
                disabled={isRunning || selectedTechniques.length === 0}
            >
                {isRunning ? '🔄 Running Tests...' : `🚀 Run Tests (${selectedTechniques.length} techniques)`}
            </button>
        </div>
    </div>
);

const TestRunningPanel = ({ currentTest }) => (
    <div className="test-running-panel">
        <div className="running-header">
            <h3>🔄 Running Tests...</h3>
            <div className="progress-info">
                Progress: {Math.round(currentTest.progress)}%
                {currentTest.currentTechnique && ` • Testing: ${currentTest.currentTechnique}`}
            </div>
        </div>

        <div className="progress-bar">
            <div
                className="progress-fill"
                style={{ width: `${currentTest.progress}%` }}
            />
        </div>

        <div className="test-status">
            <div className="status-item">
                <span className="label">Total Techniques:</span>
                <span className="value">{currentTest.totalTechniques}</span>
            </div>
            <div className="status-item">
                <span className="label">Started:</span>
                <span className="value">{new Date(currentTest.timestamp).toLocaleTimeString()}</span>
            </div>
            <div className="status-item">
                <span className="label">Status:</span>
                <span className="value">{currentTest.status}</span>
            </div>
        </div>

        {currentTest.error && (
            <div className="error-display">
                <h4>❌ Error Occurred</h4>
                <p>{currentTest.error}</p>
            </div>
        )}
    </div>
);

const TestResultsPanel = ({ test, results }) => (
    <div className="test-results-panel">
        <div className="results-header">
            <h3>📊 Test Results</h3>
            <div className="test-summary">
                <div className="summary-item">
                    <span className="label">Test ID:</span>
                    <span className="value">{test.id}</span>
                </div>
                <div className="summary-item">
                    <span className="label">Completed:</span>
                    <span className="value">{new Date(test.completedAt).toLocaleString()}</span>
                </div>
                <div className="summary-item">
                    <span className="label">Success Rate:</span>
                    <span className="value">{safeRound(test.summary?.successRate)}%</span>
                </div>
                <div className="summary-item">
                    <span className="label">Best Technique:</span>
                    <span className="value">{test.bestTechnique?.technique || 'None'}</span>
                </div>
            </div>
        </div>

        {/* Test Configuration Display */}
        <TestConfigurationDisplay test={test} />

        {/* Patient Profile Display */}
        <PatientProfileDisplay test={test} />

        {/* Detailed Results Grid */}
        <div className="detailed-results">
            <h4>🔍 Detailed Technique Results</h4>
            <div className="results-grid">
                {results.map((result) => (
                    <EnhancedResultCard key={result.technique} result={result} test={test} />
                ))}
            </div>
        </div>

        {test.recommendations && test.recommendations.length > 0 && (
            <div className="test-recommendations">
                <h4>💡 Recommendations</h4>
                {test.recommendations.map((rec, index) => (
                    <div key={index} className={`recommendation ${rec.priority}`}>
                        <h5>{rec.title}</h5>
                        <p>{rec.description}</p>
                    </div>
                ))}
            </div>
        )}
    </div>
);

const TestComparisonPanel = ({ results, selectedResults, setSelectedResults }) => (
    <div className="test-comparison-panel">
        <h3>🔍 Technique Comparison</h3>
        <ComparisonMatrix results={results} />
    </div>
);

const TestAnalyticsPanel = ({ testHistory, currentTest }) => (
    <div className="test-analytics-panel">
        <h3>📈 Analytics Dashboard</h3>
        <AnalyticsDashboard history={testHistory} currentTest={currentTest} />
    </div>
);

const TestHistoryPanel = ({ testHistory, onSelectTest }) => (
    <div className="test-history-panel">
        <h3>📚 Test History</h3>
        <div className="history-list">
            {testHistory.map((test) => (
                <div
                    key={test.id}
                    className="history-item"
                    onClick={() => onSelectTest(test)}
                >
                    <div className="history-header">
                        <span className="test-id">{test.id}</span>
                        <span className="test-date">{new Date(test.timestamp).toLocaleString()}</span>
                    </div>
                    <div className="history-details">
                        <span>Techniques: {Object.keys(test.results).length}</span>
                        <span>Success: {safeRound(test.summary?.successRate)}%</span>
                        <span className={`status ${test.status}`}>{test.status}</span>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

// Helper Components
const PatientDataEditor = ({ patientData, onChange }) => {
    const handleFieldChange = (field, value) => {
        onChange({ ...patientData, [field]: value });
    };

    const handleNestedFieldChange = (parent, field, value) => {
        onChange({
            ...patientData,
            [parent]: { ...patientData[parent], [field]: value }
        });
    };

    return (
        <div className="patient-data-editor">
            <div className="form-row">
                <label>Name:</label>
                <input
                    type="text"
                    value={patientData.name}
                    onChange={(e) => handleFieldChange('name', e.target.value)}
                />
            </div>
            <div className="form-row">
                <label>Age:</label>
                <input
                    type="number"
                    value={patientData.age}
                    onChange={(e) => handleFieldChange('age', parseInt(e.target.value))}
                />
            </div>
            <div className="form-row">
                <label>Diabetes Type:</label>
                <select
                    value={patientData.diabetesType}
                    onChange={(e) => handleFieldChange('diabetesType', e.target.value)}
                >
                    <option value="Type 1">Type 1</option>
                    <option value="Type 2">Type 2</option>
                    <option value="Gestational">Gestational</option>
                </select>
            </div>
            <div className="form-row">
                <label>Average Glucose:</label>
                <input
                    type="number"
                    value={patientData.recentGlucoseData.averageGlucose}
                    onChange={(e) => handleNestedFieldChange('recentGlucoseData', 'averageGlucose', parseInt(e.target.value))}
                />
            </div>
            <div className="form-row">
                <label>Time in Range (%):</label>
                <input
                    type="number"
                    value={patientData.recentGlucoseData.timeInRange}
                    onChange={(e) => handleNestedFieldChange('recentGlucoseData', 'timeInRange', parseInt(e.target.value))}
                />
            </div>
        </div>
    );
};

const TechniqueSelector = ({ techniques, onChange }) => {
    const techniqueGroups = {
        'Traditional Approaches': ['structured', 'conversational', 'motivational', 'clinical'],
        'Few-Shot Variants': ['zero_shot', 'one_shot', 'few_shot'],
        'Advanced Reasoning': ['chain_of_thought', 'tree_of_thought', 'self_consistency'],
        'Interactive Methods': ['react', 'role_playing']
    };

    const handleTechniqueChange = (technique, enabled) => {
        onChange({ ...techniques, [technique]: enabled });
    };

    const selectAll = (group) => {
        const updates = {};
        techniqueGroups[group].forEach(technique => {
            updates[technique] = true;
        });
        onChange({ ...techniques, ...updates });
    };

    const selectNone = (group) => {
        const updates = {};
        techniqueGroups[group].forEach(technique => {
            updates[technique] = false;
        });
        onChange({ ...techniques, ...updates });
    };

    return (
        <div className="technique-selector">
            {Object.entries(techniqueGroups).map(([groupName, groupTechniques]) => (
                <div key={groupName} className="technique-group">
                    <div className="group-header">
                        <h4>{groupName}</h4>
                        <div className="group-actions">
                            <button onClick={() => selectAll(groupName)}>All</button>
                            <button onClick={() => selectNone(groupName)}>None</button>
                        </div>
                    </div>
                    <div className="technique-list">
                        {groupTechniques.map(technique => (
                            <label key={technique} className="technique-item">
                                <input
                                    type="checkbox"
                                    checked={techniques[technique]}
                                    onChange={(e) => handleTechniqueChange(technique, e.target.checked)}
                                />
                                <span className="technique-name">
                                    {formatTechniqueName(technique)}
                                </span>
                            </label>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );
};

const TestConfigEditor = ({ config, onChange }) => (
    <div className="test-config-editor">
        <div className="form-row">
            <label>Test Iterations:</label>
            <input
                type="number"
                min="1"
                max="10"
                value={config.testIterations}
                onChange={(e) => onChange({ ...config, testIterations: parseInt(e.target.value) })}
            />
        </div>
        <div className="form-row">
            <label>
                <input
                    type="checkbox"
                    checked={config.compareBaseline}
                    onChange={(e) => onChange({ ...config, compareBaseline: e.target.checked })}
                />
                Compare with baseline
            </label>
        </div>
        <div className="form-row">
            <label>Custom Prompt (optional):</label>
            <textarea
                value={config.customPrompt}
                onChange={(e) => onChange({ ...config, customPrompt: e.target.value })}
                placeholder="Enter custom prompt template..."
                rows="4"
            />
        </div>
    </div>
);

const TestSuiteManager = ({ suites, onLoad }) => (
    <div className="test-suite-manager">
        <div className="suite-list">
            {suites.length === 0 ? (
                <p className="no-suites">No saved test suites</p>
            ) : (
                suites.map(suite => (
                    <div key={suite.id} className="suite-item">
                        <div className="suite-info">
                            <h4>{suite.name}</h4>
                            <p>{new Date(suite.createdAt).toLocaleDateString()}</p>
                        </div>
                        <button onClick={() => onLoad(suite)}>Load</button>
                    </div>
                ))
            )}
        </div>
    </div>
);

// Enhanced display components for comprehensive test results
const TestConfigurationDisplay = ({ test }) => (
    <div className="test-configuration-display">
        <h4>⚙️ Test Configuration</h4>
        <div className="config-grid">
            <div className="config-section">
                <h5>👤 Patient Data</h5>
                <div className="config-details">
                    <div className="config-item">
                        <span className="label">Name:</span>
                        <span className="value">{test.config?.patientData?.name || 'N/A'}</span>
                    </div>
                    <div className="config-item">
                        <span className="label">Age:</span>
                        <span className="value">{test.config?.patientData?.age || 'N/A'}</span>
                    </div>
                    <div className="config-item">
                        <span className="label">Gender:</span>
                        <span className="value">{test.config?.patientData?.gender || 'N/A'}</span>
                    </div>
                    <div className="config-item">
                        <span className="label">Diabetes Type:</span>
                        <span className="value">{test.config?.patientData?.diabetesType || 'N/A'}</span>
                    </div>
                    <div className="config-item">
                        <span className="label">Diagnosis Date:</span>
                        <span className="value">{test.config?.patientData?.diagnosisDate || 'N/A'}</span>
                    </div>
                </div>

                {test.config?.patientData?.currentMedications && (
                    <div className="medications">
                        <span className="label">Current Medications:</span>
                        <div className="medication-list">
                            {test.config.patientData.currentMedications.map((med, idx) => (
                                <span key={idx} className="medication-tag">{med}</span>
                            ))}
                        </div>
                    </div>
                )}

                {test.config?.patientData?.challenges && (
                    <div className="challenges">
                        <span className="label">Challenges:</span>
                        <div className="challenge-list">
                            {test.config.patientData.challenges.map((challenge, idx) => (
                                <span key={idx} className="challenge-tag">{challenge}</span>
                            ))}
                        </div>
                    </div>
                )}

                {test.config?.patientData?.goals && (
                    <div className="goals">
                        <span className="label">Goals:</span>
                        <div className="goal-list">
                            {test.config.patientData.goals.map((goal, idx) => (
                                <span key={idx} className="goal-tag">{goal}</span>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            <div className="config-section">
                <h5>⚡ Test Settings</h5>
                <div className="config-details">
                    <div className="config-item">
                        <span className="label">Test Iterations:</span>
                        <span className="value">{test.config?.testIterations || 1}</span>
                    </div>
                    <div className="config-item">
                        <span className="label">Compare Baseline:</span>
                        <span className="value">{test.config?.compareBaseline ? '✅ Yes' : '❌ No'}</span>
                    </div>
                    <div className="config-item">
                        <span className="label">Total Techniques:</span>
                        <span className="value">{test.totalTechniques || 0}</span>
                    </div>
                </div>

                {test.config?.techniques && (
                    <div className="selected-techniques">
                        <span className="label">Selected Techniques:</span>
                        <div className="technique-list">
                            {Object.entries(test.config.techniques)
                                .filter(([_, enabled]) => enabled)
                                .map(([technique, _]) => (
                                    <span key={technique} className="technique-tag">{technique}</span>
                                ))}
                        </div>
                    </div>
                )}
            </div>

            {test.config?.patientData?.recentGlucoseData && (
                <div className="config-section">
                    <h5>🍯 Glucose Data</h5>
                    <div className="config-details">
                        <div className="config-item">
                            <span className="label">Average Glucose:</span>
                            <span className="value">{test.config.patientData.recentGlucoseData.averageGlucose} mg/dL</span>
                        </div>
                        <div className="config-item">
                            <span className="label">Time in Range:</span>
                            <span className="value">{test.config.patientData.recentGlucoseData.timeInRange}%</span>
                        </div>
                        <div className="config-item">
                            <span className="label">Variability:</span>
                            <span className="value">{test.config.patientData.recentGlucoseData.variability}</span>
                        </div>
                    </div>
                </div>
            )}

            {test.config?.patientData?.lifestyle && (
                <div className="config-section">
                    <h5>🏃‍♀️ Lifestyle</h5>
                    <div className="config-details">
                        <div className="config-item">
                            <span className="label">Exercise:</span>
                            <span className="value">{test.config.patientData.lifestyle.exercise}</span>
                        </div>
                        <div className="config-item">
                            <span className="label">Diet:</span>
                            <span className="value">{test.config.patientData.lifestyle.diet}</span>
                        </div>
                        <div className="config-item">
                            <span className="label">Stress Level:</span>
                            <span className="value">{test.config.patientData.lifestyle.stress}</span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    </div>
);

const PatientProfileDisplay = ({ test }) => {
    const resultData = test.results && Object.values(test.results)[0];
    const patientProfile = resultData?.iterations?.[0]?.patient_profile;

    if (!patientProfile) return null;

    return (
        <div className="patient-profile-display">
            <h4>👤 Patient Profile Analysis</h4>
            <div className="profile-grid">
                <div className="profile-section">
                    <h5>📊 Demographics</h5>
                    <div className="profile-details">
                        <div className="profile-item">
                            <span className="label">Age Range:</span>
                            <span className="value">{patientProfile.demographics?.age_range || 'N/A'}</span>
                        </div>
                        <div className="profile-item">
                            <span className="label">Gender:</span>
                            <span className="value">{patientProfile.demographics?.gender || 'N/A'}</span>
                        </div>
                        <div className="profile-item">
                            <span className="label">Diabetes Type:</span>
                            <span className="value">{patientProfile.demographics?.diabetes_type || 'N/A'}</span>
                        </div>
                    </div>
                </div>

                <div className="profile-section">
                    <h5>🎯 Engagement Factors</h5>
                    <div className="profile-details">
                        <div className="profile-item">
                            <span className="label">Has Goals:</span>
                            <span className="value">{patientProfile.engagement_factors?.has_goals ? '✅' : '❌'}</span>
                        </div>
                        <div className="profile-item">
                            <span className="label">Has Challenges:</span>
                            <span className="value">{patientProfile.engagement_factors?.has_challenges ? '✅' : '❌'}</span>
                        </div>
                        <div className="profile-item">
                            <span className="label">Active Monitoring:</span>
                            <span className="value">{patientProfile.engagement_factors?.active_monitoring ? '✅' : '❌'}</span>
                        </div>
                    </div>
                </div>

                <div className="profile-section">
                    <h5>🔧 Complexity Level</h5>
                    <div className="complexity-indicator">
                        <span className="complexity-value">{patientProfile.complexity_level || 'N/A'}</span>
                        <div className="complexity-bar">
                            <div
                                className="complexity-fill"
                                style={{
                                    width: `${COMPLEXITY_LEVELS[patientProfile.complexity_level]?.percentage || 0}%`
                                }}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const EnhancedResultCard = ({ result, test }) => {
    const [expanded, setExpanded] = useState(false);
    const averageScores = result.averageScores || {};
    const firstIteration = result.iterations?.[0];

    return (
        <div className="enhanced-result-card">
            <div className="card-header" onClick={() => setExpanded(!expanded)}>
                <div className="technique-info">
                    <h4>{formatTechniqueName(result.technique)}</h4>
                    <div className="technique-badge">{result.technique}</div>
                </div>
                <div className="expand-icon">{expanded ? '▼' : '▶'}</div>
            </div>

            <div className="score-grid">
                <div className="score-item">
                    <span className="label">Patient Focus</span>
                    <span className="value">{firstIteration?.recommendations?.patient_focus_score || 0}%</span>
                </div>
                <div className="score-item">
                    <span className="label">Actionability</span>
                    <span className="value">{safeRound(firstIteration?.recommendations?.actionability_score)}</span>
                </div>
                <div className="score-item">
                    <span className="label">Effectiveness</span>
                    <span className="value">{safeRound(averageScores.effectiveness)}%</span>
                </div>
                <div className="score-item">
                    <span className="label">Consistency</span>
                    <span className="value">{safeRound(result.consistency)}%</span>
                </div>
                <div className="score-item">
                    <span className="label">Reliability</span>
                    <span className="value">{safeRound(result.reliability)}%</span>
                </div>
                <div className="score-item">
                    <span className="label">Response Time</span>
                    <span className="value">{Math.round(averageScores.response_time || 0)}ms</span>
                </div>
            </div>

            {expanded && (
                <div className="expanded-content">
                    <IterationDetails result={result} />
                    <RecommendationDetails result={result} />
                    {firstIteration?.recommendations?.raw_response && (
                        <RawResponseDisplay response={firstIteration.recommendations.raw_response} />
                    )}
                </div>
            )}
        </div>
    );
};

const IterationDetails = ({ result }) => (
    <div className="iteration-details">
        <h5>🔄 Iteration Details</h5>
        {result.iterations?.map((iteration, idx) => (
            <div key={idx} className="iteration-item">
                <div className="iteration-header">
                    <span className="iteration-number">Iteration {idx + 1}</span>
                    <span className="iteration-timestamp">
                        {new Date(iteration.timestamp).toLocaleString()}
                    </span>
                </div>
                <div className="iteration-meta">
                    <div className="meta-item">
                        <span className="label">Model:</span>
                        <span className="value">{iteration.model_used || 'N/A'}</span>
                    </div>
                    <div className="meta-item">
                        <span className="label">Analysis Type:</span>
                        <span className="value">{iteration.analysis_type || 'N/A'}</span>
                    </div>
                    <div className="meta-item">
                        <span className="label">Mock Mode:</span>
                        <span className="value">{iteration.mockMode ? '✅' : '❌'}</span>
                    </div>
                    <div className="meta-item">
                        <span className="label">Token Count:</span>
                        <span className="value">{iteration.tokenCount || 0}</span>
                    </div>
                    <div className="meta-item">
                        <span className="label">Word Count:</span>
                        <span className="value">{iteration.wordCount || 0}</span>
                    </div>
                </div>
            </div>
        ))}
    </div>
);

const RecommendationDetails = ({ result }) => {
    const recommendations = result.iterations?.[0]?.recommendations?.recommendations || [];

    if (recommendations.length === 0) return null;

    return (
        <div className="recommendation-details">
            <h5>💡 Generated Recommendations ({recommendations.length})</h5>
            <div className="recommendations-list">
                {recommendations.map((rec, idx) => (
                    <div key={idx} className="recommendation-item">
                        <div className="rec-header">
                            <h6>{rec.title}</h6>
                            <div className="rec-meta">
                                <span className={`priority ${rec.priority}`}>{rec.priority}</span>
                                <span className={`difficulty ${rec.difficulty}`}>{rec.difficulty}</span>
                            </div>
                        </div>
                        <p className="rec-description">{rec.description}</p>
                        {rec.expected_benefit && (
                            <div className="rec-benefit">
                                <strong>Expected Benefit:</strong> {rec.expected_benefit}
                            </div>
                        )}
                        {rec.timeline && (
                            <div className="rec-timeline">
                                <strong>Timeline:</strong> {rec.timeline}
                            </div>
                        )}
                        {rec.action_items && rec.action_items.length > 0 && (
                            <div className="rec-actions">
                                <strong>Action Items:</strong>
                                <ul>
                                    {rec.action_items.map((action, actionIdx) => (
                                        <li key={actionIdx}>{action}</li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
};

const RawResponseDisplay = ({ response }) => {
    const [showRaw, setShowRaw] = useState(false);

    return (
        <div className="raw-response-display">
            <div className="raw-response-header">
                <h5>📝 Raw AI Response</h5>
                <button
                    className="toggle-raw-btn"
                    onClick={() => setShowRaw(!showRaw)}
                >
                    {showRaw ? 'Hide' : 'Show'} Raw Response
                </button>
            </div>
            {showRaw && (
                <div className="raw-response-content">
                    <pre className="raw-text">{response}</pre>
                </div>
            )}
        </div>
    );
};

const ResultCard = ({ result }) => {
    const averageScores = result.averageScores || {};

    return (
        <div className="result-card">
            <div className="card-header">
                <h4>{formatTechniqueName(result.technique)}</h4>
                <div className="technique-badge">{result.technique}</div>
            </div>

            <div className="score-grid">
                <div className="score-item">
                    <span className="label">Effectiveness</span>
                    <span className="value">{Math.round(averageScores.effectiveness || 0)}</span>
                </div>
                <div className="score-item">
                    <span className="label">Readability</span>
                    <span className="value">{Math.round(averageScores.readability || 0)}</span>
                </div>
                <div className="score-item">
                    <span className="label">Actionability</span>
                    <span className="value">{Math.round(averageScores.actionability || 0)}</span>
                </div>
                <div className="score-item">
                    <span className="label">Reasoning</span>
                    <span className="value">{Math.round(averageScores.reasoning_depth || 0)}</span>
                </div>
                <div className="score-item">
                    <span className="label">Consistency</span>
                    <span className="value">{Math.round(result.consistency || 0)}</span>
                </div>
                <div className="score-item">
                    <span className="label">Reliability</span>
                    <span className="value">{Math.round(result.reliability || 0)}</span>
                </div>
            </div>

            <div className="performance-metrics">
                <div className="metric">
                    <span className="label">Avg Response Time:</span>
                    <span className="value">{safeRound(averageScores.response_time)}ms</span>
                </div>
                <div className="metric">
                    <span className="label">Avg Token Count:</span>
                    <span className="value">{safeRound(averageScores.token_count)}</span>
                </div>
                <div className="metric">
                    <span className="label">Success Rate:</span>
                    <span className="value">
                        {Math.round((result.iterations.filter(i => !i.error).length / result.iterations.length) * 100)}%
                    </span>
                </div>
            </div>

            <div className="iterations-summary">
                <span className="label">Iterations:</span>
                <span className="value">{result.iterations.length}</span>
                {result.iterations.some(i => i.error) && (
                    <span className="error-indicator">⚠️ {result.iterations.filter(i => i.error).length} errors</span>
                )}
            </div>
        </div>
    );
};

const ComparisonMatrix = ({ results }) => {
    const metrics = ['effectiveness', 'readability', 'actionability', 'reasoning_depth', 'consistency'];

    return (
        <div className="comparison-matrix">
            <table>
                <thead>
                    <tr>
                        <th>Technique</th>
                        {metrics.map(metric => (
                            <th key={metric}>{metric.replace(/_/g, ' ')}</th>
                        ))}
                        <th>Avg Response Time</th>
                        <th>Success Rate</th>
                    </tr>
                </thead>
                <tbody>
                    {results.map(result => (
                        <tr key={result.technique}>
                            <td className="technique-name">{result.technique}</td>
                            {metrics.map(metric => (
                                <td key={metric} className="score-cell">
                                    <div className="score-bar">
                                        <div
                                            className="score-fill"
                                            style={{
                                                width: `${(result.averageScores?.[metric] || 0)}%`,
                                                backgroundColor: getScoreColor(result.averageScores?.[metric] || 0)
                                            }}
                                        />
                                        <span className="score-text">
                                            {Math.round(result.averageScores?.[metric] || 0)}
                                        </span>
                                    </div>
                                </td>
                            ))}
                            <td>{Math.round(result.averageScores?.response_time || 0)}ms</td>
                            <td>
                                {Math.round((result.iterations.filter(i => !i.error).length / result.iterations.length) * 100)}%
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

const AnalyticsDashboard = ({ history, currentTest }) => (
    <div className="analytics-dashboard">
        <div className="analytics-grid">
            <div className="analytics-card">
                <h4>📊 Test Statistics</h4>
                <div className="stat-list">
                    <div className="stat-item">
                        <span className="label">Total Tests:</span>
                        <span className="value">{history.length}</span>
                    </div>
                    <div className="stat-item">
                        <span className="label">Successful Tests:</span>
                        <span className="value">{history.filter(t => t.status === 'completed').length}</span>
                    </div>
                    <div className="stat-item">
                        <span className="label">Average Success Rate:</span>
                        <span className="value">
                            {Math.round(history.reduce((sum, t) => sum + (t.summary?.successRate || 0), 0) / history.length)}%
                        </span>
                    </div>
                </div>
            </div>

            <div className="analytics-card">
                <h4>🏆 Best Performing Techniques</h4>
                <TopTechniquesChart history={history} />
            </div>

            <div className="analytics-card">
                <h4>⏱️ Performance Trends</h4>
                <PerformanceTrendsChart history={history} />
            </div>

            <div className="analytics-card">
                <h4>🔍 Technique Analysis</h4>
                <TechniqueAnalysisChart history={history} />
            </div>
        </div>
    </div>
);

// Helper functions
const getScoreColor = (score) => {
    if (score >= 80) return '#10b981'; // green
    if (score >= 60) return '#f59e0b'; // yellow
    if (score >= 40) return '#ef4444'; // red
    return '#6b7280'; // gray
};

const TopTechniquesChart = ({ history }) => {
    // This would be a real chart in production
    return (
        <div className="placeholder-chart">
            <p>📈 Chart showing top performing techniques over time</p>
        </div>
    );
};

const PerformanceTrendsChart = ({ history }) => {
    return (
        <div className="placeholder-chart">
            <p>📊 Chart showing performance trends across test runs</p>
        </div>
    );
};

const TechniqueAnalysisChart = ({ history }) => {
    return (
        <div className="placeholder-chart">
            <p>🔍 Detailed analysis of technique performance characteristics</p>
        </div>
    );
};

// Modal Component
const Modal = ({ type, data, onClose }) => {
    const [inputValue, setInputValue] = useState('');

    const handleSubmit = () => {
        if (type === 'input' && data.onConfirm) {
            data.onConfirm(inputValue);
        } else if (data.onConfirm) {
            data.onConfirm();
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter') {
            handleSubmit();
        } else if (e.key === 'Escape') {
            onClose();
        }
    };

    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3>{data.title || 'Confirm'}</h3>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>
                <div className="modal-body">
                    <p>{data.message}</p>
                    {type === 'input' && (
                        <input
                            type="text"
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onKeyDown={handleKeyDown}
                            placeholder="Enter value..."
                            autoFocus
                        />
                    )}
                </div>
                <div className="modal-actions">
                    <button onClick={onClose} className="btn-cancel">
                        Cancel
                    </button>
                    <button onClick={handleSubmit} className="btn-confirm">
                        {data.confirmText || 'Confirm'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default PromptTesting;
