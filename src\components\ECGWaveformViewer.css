.ecg-waveform-viewer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  color: var(--gb-fg);
}

.ecg-controls {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  padding: 1rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.lead-selector {
  flex: 1;
  min-width: 300px;
}

.lead-selector h4 {
  margin: 0 0 1rem 0;
  color: var(--gb-accent2);
  font-size: 0.9rem;
  font-weight: 600;
}

.lead-group {
  margin-bottom: 1rem;
}

.lead-group-name {
  display: block;
  font-size: 0.8rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.75);
  margin-bottom: 0.5rem;
}

.lead-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.lead-button {
  padding: 0.4rem 0.8rem;
  border: 2px solid;
  border-radius: 4px;
  background: transparent;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
}

.lead-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lead-button.active {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.display-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-width: 200px;
}

.time-controls,
.playback-controls,
.display-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.time-controls h4,
.playback-controls h4,
.display-options h4 {
  margin: 0;
  color: var(--gb-accent2);
  font-size: 0.9rem;
  font-weight: 600;
}

.time-controls label,
.playback-controls label,
.display-options label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.85);
}

.time-controls input,
.playback-controls select {
  padding: 0.3rem 0.5rem;
  border: 1px solid rgba(235, 219, 178, 0.25);
  border-radius: 4px;
  font-size: 0.8rem;
  width: 80px;
  background: rgba(235, 219, 178, 0.05);
  color: var(--gb-fg);
}

.play-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--gb-accent2);
  border-radius: 4px;
  background: transparent;
  color: var(--gb-accent2);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.play-button:hover {
  background: rgba(250, 189, 47, 0.1);
}

.play-button.playing {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.35);
  color: #ffb4ab;
}

.display-options input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.ecg-chart-container {
  height: 400px;
  width: 100%;
  position: relative;
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 4px;
  background: var(--gb-bg1);
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: rgba(235, 219, 178, 0.75);
  text-align: center;
}

.no-data p {
  margin: 0.5rem 0;
}

.ecg-metrics {
  background: rgba(235, 219, 178, 0.05);
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.ecg-metrics h4 {
  margin: 0 0 1rem 0;
  color: var(--gb-accent2);
  font-size: 0.9rem;
  font-weight: 600;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.7);
  font-weight: 500;
  margin-bottom: 0.2rem;
}

.metric-value {
  font-size: 1rem;
  color: var(--gb-fg);
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Responsive design */
@media (max-width: 768px) {
  .ecg-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .lead-buttons {
    justify-content: center;
  }

  .lead-button {
    min-width: 35px;
    padding: 0.3rem 0.6rem;
  }

  .display-controls {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
    min-width: unset;
  }

  .ecg-chart-container {
    height: 300px;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .ecg-waveform-viewer {
    padding: 0.5rem;
  }

  .ecg-controls {
    padding: 0.5rem;
  }

  .lead-selector {
    min-width: unset;
  }

  .lead-buttons {
    gap: 0.3rem;
  }

  .lead-button {
    min-width: 30px;
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
  }

  .ecg-chart-container {
    height: 250px;
  }
}
