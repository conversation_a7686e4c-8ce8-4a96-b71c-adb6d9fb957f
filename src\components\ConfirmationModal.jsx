import React from 'react';
import './ConfirmationModal.css';

const ConfirmationModal = ({ 
    isOpen, 
    title, 
    message, 
    confirmText = 'Confirm', 
    cancelText = 'Cancel',
    onConfirm, 
    onCancel,
    confirmButtonStyle = 'danger'
}) => {
    if (!isOpen) return null;

    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget) {
            onCancel();
        }
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
            onCancel();
        } else if (e.key === 'Enter') {
            onConfirm();
        }
    };

    React.useEffect(() => {
        if (isOpen) {
            document.addEventListener('keydown', handleKeyDown);
            // Focus trap - focus the modal
            const modal = document.querySelector('.confirmation-modal');
            if (modal) modal.focus();
            
            return () => {
                document.removeEventListener('keydown', handleKeyDown);
            };
        }
    }, [isOpen]);

    return (
        <div 
            className="confirmation-modal-overlay" 
            onClick={handleOverlayClick}
            role="dialog"
            aria-modal="true"
            aria-labelledby="confirmation-title"
            aria-describedby="confirmation-message"
        >
            <div 
                className="confirmation-modal" 
                tabIndex={-1}
            >
                <div className="confirmation-modal-header">
                    <h3 id="confirmation-title" className="confirmation-modal-title">
                        {title}
                    </h3>
                    <button 
                        className="confirmation-modal-close"
                        onClick={onCancel}
                        aria-label="Close modal"
                    >
                        ×
                    </button>
                </div>
                
                <div className="confirmation-modal-body">
                    <p id="confirmation-message" className="confirmation-modal-message">
                        {message}
                    </p>
                </div>
                
                <div className="confirmation-modal-actions">
                    <button 
                        className="confirmation-modal-button cancel-button"
                        onClick={onCancel}
                    >
                        {cancelText}
                    </button>
                    <button 
                        className={`confirmation-modal-button confirm-button ${confirmButtonStyle}`}
                        onClick={onConfirm}
                        autoFocus
                    >
                        {confirmText}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmationModal;