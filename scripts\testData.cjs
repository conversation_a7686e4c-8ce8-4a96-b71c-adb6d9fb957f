const neo4j = require('neo4j-driver');
const path = require('path');
const fs = require('fs');

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }
  }
}

loadEnvFile();

const driver = neo4j.driver(
  process.env.VITE_NEO4J_URI,
  neo4j.auth.basic(process.env.VITE_NEO4J_USERNAME, process.env.VITE_NEO4J_PASSWORD)
);

async function testData() {
  const session = driver.session();
  try {
    console.log('Testing populated data...');
    console.log('========================');

    // Count all nodes
    const countResult = await session.run('MATCH (n) RETURN count(n) as total');
    console.log('Total nodes:', countResult.records[0].get('total').toNumber());

    // Count different entity types
    const patientCount = await session.run('MATCH (p:Patient) RETURN count(p) as patients');
    console.log('Patients:', patientCount.records[0].get('patients').toNumber());

    const glucoseCount = await session.run('MATCH (g:GlucoseReading) RETURN count(g) as readings');
    console.log('Glucose readings:', glucoseCount.records[0].get('readings').toNumber());

    const doctorCount = await session.run('MATCH (d:Doctor) RETURN count(d) as doctors');
    console.log('Doctors:', doctorCount.records[0].get('doctors').toNumber());

    // List all patients
    console.log('\nPatient List:');
    console.log('=============');
    const patients = await session.run('MATCH (p:Patient) RETURN p.patientId, p.name, p.condition ORDER BY p.patientId');
    patients.records.forEach(record => {
      console.log(`${record.get('p.patientId')}: ${record.get('p.name')} (${record.get('p.condition')})`);
    });

    // Test AGP query for Patient_1
    console.log('\nSample glucose data for Patient_1:');
    console.log('==================================');
    const agpResult = await session.run(`
      MATCH (p:Patient {patientId: 'Patient_1'})-[:HAD_READING]->(g:GlucoseReading)
      RETURN g.timestamp, g.glucose, g.readingType
      ORDER BY g.timestamp DESC
      LIMIT 10
    `);

    agpResult.records.forEach(record => {
      const timestamp = new Date(record.get('g.timestamp')).toLocaleString();
      console.log(`- ${timestamp}: ${record.get('g.glucose')} mg/dL (${record.get('g.readingType')})`);
    });

    // Test glucose stats query
    console.log('\nGlucose statistics for Patient_1:');
    console.log('=================================');
    const statsResult = await session.run(`
      MATCH (p:Patient {patientId: 'Patient_1'})-[:HAD_READING]->(g:GlucoseReading)
      RETURN
        min(g.glucose) AS minGlucose,
        max(g.glucose) AS maxGlucose,
        avg(g.glucose) AS avgGlucose,
        count(g) AS numberOfReadings
    `);

    if (statsResult.records.length > 0) {
      const stats = statsResult.records[0];
      console.log(`- Min: ${Math.round(stats.get('minGlucose'))} mg/dL`);
      console.log(`- Max: ${Math.round(stats.get('maxGlucose'))} mg/dL`);
      console.log(`- Average: ${Math.round(stats.get('avgGlucose'))} mg/dL`);
      console.log(`- Total readings: ${stats.get('numberOfReadings').toNumber()}`);
    }

    console.log('\n✅ Database successfully populated and ready!');
    console.log('🎯 Perfect for AGP visualization and Query Runner!');

  } catch (error) {
    console.error('Error testing data:', error);
  } finally {
    await session.close();
    await driver.close();
  }
}

testData();
