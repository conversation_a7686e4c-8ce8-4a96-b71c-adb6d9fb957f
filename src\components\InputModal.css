/* Input Modal Styles */
.input-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.input-modal {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    min-width: 400px;
    max-width: 500px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: scale(1);
    transition: transform 0.2s ease-out;
}

.input-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.input-modal-title {
    margin: 0;
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.input-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.input-modal-close:hover {
    color: var(--text-color);
    background-color: var(--hover-bg);
}

.input-modal-body {
    padding: 1.5rem;
}

.input-modal-message {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    font-size: 1rem;
    line-height: 1.5;
}

.input-modal-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.input-modal-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(var(--accent-rgb), 0.1);
}

.input-modal-input::placeholder {
    color: var(--text-muted);
}

.input-modal-error {
    display: block;
    margin-top: 0.5rem;
    color: #dc3545;
    font-size: 0.875rem;
}

.input-modal-actions {
    display: flex;
    gap: 0.75rem;
    padding: 1rem 1.5rem 1.5rem 1.5rem;
    justify-content: flex-end;
    border-top: 1px solid var(--border-color);
}

.input-modal-button {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.cancel-button {
    background: var(--bg-color);
    color: var(--text-color);
}

.cancel-button:hover {
    background: var(--hover-bg);
    border-color: var(--text-muted);
}

.confirm-button.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.confirm-button.primary:hover {
    background: var(--accent-hover);
    border-color: var(--accent-hover);
}

.confirm-button.disabled {
    background: var(--border-color);
    color: var(--text-muted);
    border-color: var(--border-color);
    cursor: not-allowed;
}

/* Focus styles for accessibility */
.input-modal-button:focus,
.input-modal-close:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Mobile responsive */
@media (max-width: 640px) {
    .input-modal {
        min-width: 90vw;
        max-width: 90vw;
        margin: 0 1rem;
    }
    
    .input-modal-actions {
        flex-direction: column-reverse;
    }
    
    .input-modal-button {
        width: 100%;
        padding: 0.75rem 1rem;
    }
}