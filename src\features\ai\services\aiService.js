// Complete AI Service for Healthcare Recommendations using OpenAI
// Enhanced AGP Analysis and Clinical Decision Support

class AIRecommendationService {
  constructor() {
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY;
    this.model = import.meta.env.VITE_OPENAI_MODEL || 'gpt-4o-mini';
    this.maxTokens = parseInt(import.meta.env.VITE_OPENAI_MAX_TOKENS) || 2000;
    this.temperature = parseFloat(import.meta.env.VITE_OPENAI_TEMPERATURE) || 0.3;
    this.enabled = import.meta.env.VITE_AI_ENABLED === 'true';
    this.recommendationLevel = import.meta.env.VITE_AI_RECOMMENDATION_LEVEL || 'detailed';
    this.contextWindow = parseInt(import.meta.env.VITE_AI_CONTEXT_WINDOW) || 8192;
    this.clinicalMode = import.meta.env.VITE_AI_CLINICAL_MODE === 'true';
    this.patientPrivacy = import.meta.env.VITE_AI_PATIENT_PRIVACY || 'strict';

    this.baseURL = 'https://api.openai.com/v1/chat/completions';

    // Global GPT-5 override for all clients (env or localStorage)
    this.forceGPT5 = import.meta.env.VITE_ENABLE_GPT5_FOR_ALL === 'true';
    try {
      if (typeof window !== 'undefined') {
        const lsFlag = localStorage.getItem('ai_force_gpt5');
        if (lsFlag === 'true') this.forceGPT5 = true;
      }
    } catch { }
    if (this.forceGPT5) {
      this.model = 'gpt-5';
    }

    // Cache for AI responses to avoid repeated API calls
    this.responseCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes

    // AGP Analysis Templates
    this.agpAnalysisTemplates = {
      timeInRange: {
        excellent: "Outstanding glucose control achieved",
        good: "Good progress toward optimal glucose management",
        needsImprovement: "Significant opportunity for glucose control improvement"
      },
      variability: {
        stable: "Excellent glucose stability with minimal variability",
        moderate: "Moderate glucose variability requiring attention",
        high: "High glucose variability indicating unstable control"
      },
      hypoglycemia: {
        safe: "Minimal hypoglycemia risk with appropriate safety margins",
        caution: "Increased hypoglycemia risk requiring prevention strategies",
        critical: "Critical hypoglycemia risk requiring immediate intervention"
      }
    };
  }

  // Enhanced AGP Analysis with Clinical Decision Support
  async generateComprehensiveAGPAnalysis(glucoseData, patientContext = {}, agpSettings = {}) {
    if (!this.enabled || !this.apiKey || this.apiKey === 'your-openai-api-key-here') {
      console.log('🤖 AI Service: Using enhanced rule-based AGP analysis');
      return this.generateEnhancedAGPAnalysis(glucoseData, patientContext, agpSettings);
    }

    try {
      const agpMetrics = this.calculateAGPMetrics(glucoseData, agpSettings);
      const prompt = this.buildComprehensiveAGPPrompt(agpMetrics, patientContext, agpSettings);

      const response = await this.callOpenAI(prompt);
      const analysis = this.parseAIResponse(response, 'comprehensive_agp');

      return {
        ...analysis,
        agpMetrics,
        clinicalSummary: this.generateClinicalSummary(agpMetrics),
        riskAssessment: this.assessClinicalRisk(agpMetrics),
        actionablePlans: this.generateActionablePlans(agpMetrics, patientContext)
      };

    } catch (error) {
      console.error('❌ AI AGP Analysis error:', error);
      return this.generateEnhancedAGPAnalysis(glucoseData, patientContext, agpSettings);
    }
  }

  calculateAGPMetrics(glucoseData, settings = {}) {
    if (!glucoseData || glucoseData.length === 0) return null;

    const thresholds = {
      veryLow: 54,
      low: settings.hypoThreshold || 70,
      targetMin: settings.targetMin || 70,
      targetMax: settings.targetMax || 180,
      high: settings.hyperThreshold || 180,
      veryHigh: 250
    };

    const glucoseValues = glucoseData.map(reading =>
      typeof reading.glucose === 'number' ? reading.glucose : reading['g.glucose']
    ).filter(g => g && !isNaN(g));

    if (glucoseValues.length === 0) return null;

    const mean = glucoseValues.reduce((sum, g) => sum + g, 0) / glucoseValues.length;
    const variance = glucoseValues.reduce((sum, g) => sum + Math.pow(g - mean, 2), 0) / glucoseValues.length;
    const standardDeviation = Math.sqrt(variance);
    const cv = (standardDeviation / mean) * 100;

    // Time in Range calculations
    const veryLowCount = glucoseValues.filter(g => g < thresholds.veryLow).length;
    const lowCount = glucoseValues.filter(g => g >= thresholds.veryLow && g < thresholds.low).length;
    const targetCount = glucoseValues.filter(g => g >= thresholds.targetMin && g <= thresholds.targetMax).length;
    const highCount = glucoseValues.filter(g => g > thresholds.targetMax && g <= thresholds.veryHigh).length;
    const veryHighCount = glucoseValues.filter(g => g > thresholds.veryHigh).length;

    const total = glucoseValues.length;
    const timeInRange = {
      veryLow: { count: veryLowCount, percentage: (veryLowCount / total) * 100 },
      low: { count: lowCount, percentage: (lowCount / total) * 100 },
      target: { count: targetCount, percentage: (targetCount / total) * 100 },
      high: { count: highCount, percentage: (highCount / total) * 100 },
      veryHigh: { count: veryHighCount, percentage: (veryHighCount / total) * 100 }
    };

    // GMI calculation
    const gmi = 3.31 + (0.02392 * mean);

    // Risk scoring
    const hypoglycemiaRisk = this.calculateHypoglycemiaRisk(timeInRange, cv);
    const hyperglycemiaRisk = this.calculateHyperglycemiaRisk(timeInRange, mean);
    const variabilityRisk = this.calculateVariabilityRisk(cv, standardDeviation);

    return {
      summary: {
        mean: Math.round(mean * 10) / 10,
        median: this.calculateMedian(glucoseValues),
        standardDeviation: Math.round(standardDeviation * 10) / 10,
        coefficientOfVariation: Math.round(cv * 10) / 10,
        range: { min: Math.min(...glucoseValues), max: Math.max(...glucoseValues) },
        gmi: Math.round(gmi * 10) / 10,
        totalReadings: total
      },
      timeInRange,
      riskScores: {
        hypoglycemia: hypoglycemiaRisk,
        hyperglycemia: hyperglycemiaRisk,
        variability: variabilityRisk,
        overall: Math.max(hypoglycemiaRisk.score, hyperglycemiaRisk.score, variabilityRisk.score)
      },
      clinicalTargets: {
        tirTarget: 70,
        tirAchieved: timeInRange.target.percentage,
        tirGap: 70 - timeInRange.target.percentage,
        gmiTarget: 7.0,
        gmiGap: gmi - 7.0,
        cvTarget: 36,
        cvGap: cv - 36
      }
    };
  }

  calculateHypoglycemiaRisk(tir, cv) {
    const veryLowPercent = tir.veryLow.percentage;
    const lowPercent = tir.low.percentage;
    const totalLowPercent = veryLowPercent + lowPercent;

    let score = 0;
    let level = 'low';
    let description = 'Minimal hypoglycemia risk';

    if (veryLowPercent > 1) {
      score = 9;
      level = 'critical';
      description = 'Critical hypoglycemia risk - immediate intervention required';
    } else if (totalLowPercent > 4) {
      score = 7;
      level = 'high';
      description = 'High hypoglycemia risk - prevention strategies needed';
    } else if (totalLowPercent > 2 || cv > 50) {
      score = 4;
      level = 'moderate';
      description = 'Moderate hypoglycemia risk - monitor closely';
    } else if (totalLowPercent > 0.5) {
      score = 2;
      level = 'low';
      description = 'Low hypoglycemia risk - maintain awareness';
    }

    return { score, level, description, veryLowPercent, totalLowPercent };
  }

  calculateHyperglycemiaRisk(tir, meanGlucose) {
    const veryHighPercent = tir.veryHigh.percentage;
    const highPercent = tir.high.percentage;
    const totalHighPercent = veryHighPercent + highPercent;

    let score = 0;
    let level = 'low';
    let description = 'Minimal hyperglycemia risk';

    if (veryHighPercent > 5 || meanGlucose > 200) {
      score = 8;
      level = 'critical';
      description = 'Critical hyperglycemia risk - aggressive intervention needed';
    } else if (totalHighPercent > 25 || meanGlucose > 160) {
      score = 6;
      level = 'high';
      description = 'High hyperglycemia risk - treatment adjustment needed';
    } else if (totalHighPercent > 15 || meanGlucose > 140) {
      score = 4;
      level = 'moderate';
      description = 'Moderate hyperglycemia risk - lifestyle modifications recommended';
    } else if (totalHighPercent > 5) {
      score = 2;
      level = 'low';
      description = 'Low hyperglycemia risk - continue current management';
    }

    return { score, level, description, veryHighPercent, totalHighPercent, meanGlucose };
  }

  calculateVariabilityRisk(cv, standardDeviation) {
    let score = 0;
    let level = 'low';
    let description = 'Excellent glucose stability';

    if (cv > 50) {
      score = 7;
      level = 'high';
      description = 'High glucose variability - requires stabilization strategies';
    } else if (cv > 36) {
      score = 5;
      level = 'moderate';
      description = 'Moderate glucose variability - optimization recommended';
    } else if (cv > 25) {
      score = 3;
      level = 'low';
      description = 'Acceptable glucose variability - minor improvements possible';
    }

    return { score, level, description, cv, standardDeviation };
  }

  generateClinicalSummary(agpMetrics) {
    const { summary, timeInRange, riskScores, clinicalTargets } = agpMetrics;

    return {
      overallAssessment: this.getOverallAssessment(timeInRange.target.percentage, summary.gmi, summary.coefficientOfVariation),
      keyFindings: [
        `Time in Range: ${timeInRange.target.percentage.toFixed(1)}% (Target: ≥70%)`,
        `Estimated A1C (GMI): ${summary.gmi}% (Target: <7.0%)`,
        `Glucose Variability (CV): ${summary.coefficientOfVariation}% (Target: ≤36%)`,
        `Mean Glucose: ${summary.mean} mg/dL`,
        `Hypoglycemia Risk: ${riskScores.hypoglycemia.level.toUpperCase()}`
      ],
      priorityAreas: this.identifyPriorityAreas(agpMetrics),
      clinicalGoals: this.generateClinicalGoals(clinicalTargets)
    };
  }

  getOverallAssessment(tir, gmi, cv) {
    if (tir >= 70 && gmi <= 7.0 && cv <= 36) {
      return 'EXCELLENT - Meeting all clinical targets for optimal diabetes management';
    } else if (tir >= 50 && gmi <= 8.0 && cv <= 45) {
      return 'GOOD - On track with room for optimization in glucose management';
    } else if (tir >= 30 && gmi <= 9.0) {
      return 'NEEDS IMPROVEMENT - Significant opportunities for better glucose control';
    } else {
      return 'REQUIRES URGENT ATTENTION - Comprehensive diabetes management review needed';
    }
  }

  identifyPriorityAreas(agpMetrics) {
    const priorities = [];
    const { timeInRange, riskScores, clinicalTargets } = agpMetrics;

    if (riskScores.hypoglycemia.score >= 7) {
      priorities.push({
        area: 'Hypoglycemia Prevention',
        urgency: 'Critical',
        description: 'Immediate risk of severe hypoglycemia requires urgent intervention'
      });
    }

    if (clinicalTargets.tirGap > 20) {
      priorities.push({
        area: 'Time in Range Improvement',
        urgency: 'High',
        description: `TIR is ${clinicalTargets.tirGap.toFixed(1)}% below target - comprehensive approach needed`
      });
    }

    if (riskScores.variability.score >= 5) {
      priorities.push({
        area: 'Glucose Variability Reduction',
        urgency: 'Medium',
        description: 'High glucose variability indicates need for therapy optimization'
      });
    }

    if (priorities.length === 0) {
      priorities.push({
        area: 'Maintenance Optimization',
        urgency: 'Low',
        description: 'Continue current successful management with minor refinements'
      });
    }

    return priorities;
  }

  generateActionablePlans(agpMetrics, patientContext) {
    const { riskScores, clinicalTargets } = agpMetrics;
    const plans = [];

    // Hypoglycemia prevention plan
    if (riskScores.hypoglycemia.score >= 4) {
      plans.push({
        category: 'Hypoglycemia Prevention',
        priority: riskScores.hypoglycemia.score >= 7 ? 'Immediate' : 'High',
        actions: [
          'Review and adjust basal insulin or medication dosing',
          'Implement continuous glucose monitoring with predictive alerts',
          'Develop hypoglycemia action plan and rescue protocols',
          'Assess meal timing and carbohydrate consistency',
          'Consider medication adjustment or alternative therapies'
        ],
        timeline: '1-2 weeks',
        followUp: 'Weekly glucose pattern review until stabilized'
      });
    }

    // Time in range improvement plan
    if (clinicalTargets.tirGap > 10) {
      plans.push({
        category: 'Time in Range Optimization',
        priority: clinicalTargets.tirGap > 30 ? 'High' : 'Medium',
        actions: [
          'Comprehensive review of medication regimen',
          'Diabetes education focusing on carbohydrate counting',
          'Exercise planning and glucose monitoring strategies',
          'Stress management and sleep hygiene assessment',
          'Technology integration (CGM, insulin pumps) evaluation'
        ],
        timeline: '4-6 weeks',
        followUp: 'Bi-weekly progress assessment'
      });
    }

    return plans;
  }

  // ... rest of the existing AI service methods remain the same ...

  calculateMedian(values) {
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
  }

  // ... existing methods continue ...
}

export default AIRecommendationService;
