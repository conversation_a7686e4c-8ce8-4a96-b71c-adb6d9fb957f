import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

const driver = neo4j.driver(
  process.env.VITE_NEO4J_URI,
  neo4j.auth.basic(process.env.VITE_NEO4J_USERNAME, process.env.VITE_NEO4J_PASSWORD)
);

async function addECGData() {
  const session = driver.session();
  try {
    console.log('🔄 Adding ECG data...');

    // Create one ECG reading for patient 001
    await session.run(`
      MATCH (p:D1NAMOSubject)
      WHERE p.patientId = $patientId
      CREATE (e:ECGReading:D1NAMOReading {
        readingId: $readingId,
        timestamp: datetime($timestamp),
        duration: $duration,
        samplingRate: $samplingRate,
        sampleCount: $sampleCount,
        signalQuality: $signalQuality
      })
      CREATE (f:ECGFeatures {
        heartRate: $heartRate,
        hrv_rmssd: $hrv,
        meanAmplitude: $meanAmplitude,
        stdDevAmplitude: $stdDevAmplitude,
        qtc_interval: $qtc,
        peakCount: $peakCount,
        signalQuality: $signalQuality
      })
      CREATE (p)-[:HAD_ECG]->(e)
      CREATE (e)-[:HAS_FEATURES]->(f)
      RETURN e.readingId as createdId
    `, {
      patientId: 'D1NAMO_001',
      readingId: 'ECG_001_sample',
      timestamp: '2014-10-02T10:30:00Z',
      duration: 10.0,
      samplingRate: 250,
      sampleCount: 2500,
      signalQuality: 'Good',
      heartRate: 72,
      hrv: 25,
      meanAmplitude: 150.0,
      stdDevAmplitude: 30.0,
      qtc: 420,
      peakCount: 18
    });

    console.log('✅ Created ECG reading for D1NAMO_001');

    // Verify
    const result = await session.run(`
      MATCH (p:D1NAMOSubject)-[:HAD_ECG]->(e:ECGReading)-[:HAS_FEATURES]->(f:ECGFeatures)
      RETURN count(e) as ecgCount
    `);

    const count = result.records[0].get('ecgCount').toNumber();
    console.log(`📊 Total ECG readings: ${count}`);

  } finally {
    await session.close();
  }
}

addECGData()
  .then(() => console.log('🎉 Complete'))
  .catch(err => console.error('❌ Error:', err))
  .finally(() => driver.close());
