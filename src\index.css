/* Global Gruvbox Dark Two-Tone Theme */
:root {
  /* Typography */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Color scheme */
  color-scheme: dark;

  /* Gruvbox palette (softer accents) */
  --gb-bg0: #282828;
  --gb-bg1: #32302f;
  --gb-fg: #ebdbb2;
  --gb-accent: #c49a17;
  /* slightly darker, less saturated */
  --gb-accent2: #e0b74e;
  /* softer yellow */

  /* Softer surfaces and borders */
  --gb-surface-1: rgba(235, 219, 178, 0.03);
  --gb-surface-2: rgba(235, 219, 178, 0.05);
  --gb-border: rgba(235, 219, 178, 0.08);
  --gb-border-strong: rgba(235, 219, 178, 0.12);
  --gb-muted: rgba(235, 219, 178, 0.75);
  --gb-muted-strong: rgba(235, 219, 178, 0.85);

  /* Softer shadows */
  --gb-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
  --gb-shadow-2: 0 4px 12px rgba(0, 0, 0, 0.20);


  /* Ensure full-height layout */
  html,
  body,
  #root {
    min-height: 100%;
  }

  html,
  body {
    height: 100%;
    margin: 0;
    background: var(--gb-bg0);
    color: var(--gb-fg);
  }

  #root {
    min-height: 100vh;
    background: inherit;
    color: inherit;
  }

  /* Base elements */
  a {
    color: var(--gb-accent2);
    text-decoration: none;
  }

  a:hover {
    color: var(--gb-accent);
    text-decoration: underline;
  }

  /* Global button flexboxing: align icon + label nicely */
  button {
    display: inline-flex;
    align-items: center;
    /* vertical center for content */
    gap: 0.5rem;
    /* space between icon and label */
  }

  /* Ensure inline SVG/icons align well inside buttons */
  button>svg {
    width: 1em;
    height: 1em;
  }

  button,
  input,
  select,
  textarea {
    color: inherit;
  }

  ::selection {
    background: rgba(224, 183, 78, 0.25);
    color: #282828;
  }
}

/* =============================================================================
   GLOBAL FLEXBOX UTILITIES
   ============================================================================= */

/* Container utilities */
.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

/* Direction */
.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

/* Wrap */
.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

/* Justify content (main axis) */
.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

/* Align items (cross axis) */
.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

/* Align content */
.content-start {
  align-content: flex-start;
}

.content-end {
  align-content: flex-end;
}

.content-center {
  align-content: center;
}

.content-between {
  align-content: space-between;
}

.content-around {
  align-content: space-around;
}

.content-evenly {
  align-content: space-evenly;
}

/* Align self */
.self-auto {
  align-self: auto;
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-center {
  align-self: center;
}

.self-stretch {
  align-self: stretch;
}

.self-baseline {
  align-self: baseline;
}

/* Flex grow/shrink/basis */
.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.flex-initial {
  flex: 0 1 auto;
}

.flex-none {
  flex: none;
}

.flex-grow {
  flex-grow: 1;
}

.flex-grow-0 {
  flex-grow: 0;
}

.flex-shrink {
  flex-shrink: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* Gap utilities */
.gap-0 {
  gap: 0;
}

.gap-1 {
  gap: 0.25rem;
  /* 4px */
}

.gap-2 {
  gap: 0.5rem;
  /* 8px */
}

.gap-3 {
  gap: 0.75rem;
  /* 12px */
}

.gap-4 {
  gap: 1rem;
  /* 16px */
}

.gap-5 {
  gap: 1.25rem;
  /* 20px */
}

.gap-6 {
  gap: 1.5rem;
  /* 24px */
}

.gap-8 {
  gap: 2rem;
  /* 32px */
}

.gap-12 {
  gap: 3rem;
  /* 48px */
}

/* Row gap */
.gap-x-0 {
  column-gap: 0;
}

.gap-x-1 {
  column-gap: 0.25rem;
}

.gap-x-2 {
  column-gap: 0.5rem;
}

.gap-x-3 {
  column-gap: 0.75rem;
}

.gap-x-4 {
  column-gap: 1rem;
}

.gap-x-6 {
  column-gap: 1.5rem;
}

.gap-x-8 {
  column-gap: 2rem;
}

/* Column gap */
.gap-y-0 {
  row-gap: 0;
}

.gap-y-1 {
  row-gap: 0.25rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-3 {
  row-gap: 0.75rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

/* =============================================================================
   COMMON LAYOUT PATTERNS
   ============================================================================= */

/* Centered container */
.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Full height flex column */
.full-height {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Card layout with flexbox */
.card-flex {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Button groups */
.btn-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

/* Toolbar/header layout */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Sidebar layout */
.sidebar-layout {
  display: flex;
  min-height: 100vh;
  gap: 0;
}

.sidebar-layout>.sidebar {
  flex-shrink: 0;
  width: 250px;
}

.sidebar-layout>.main {
  flex: 1;
  min-width: 0;
  /* Prevents flex item from overflowing */
}

/* Grid-like flexbox layouts */
.flex-grid-2 {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.flex-grid-2>* {
  flex: 1 1 calc(50% - 0.5rem);
  min-width: 250px;
}

.flex-grid-3 {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.flex-grid-3>* {
  flex: 1 1 calc(33.333% - 0.67rem);
  min-width: 200px;
}

.flex-grid-4 {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.flex-grid-4>* {
  flex: 1 1 calc(25% - 0.75rem);
  min-width: 150px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .md\:flex-col {
    flex-direction: column;
  }

  .md\:items-stretch {
    align-items: stretch;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .flex-grid-2>*,
  .flex-grid-3>*,
  .flex-grid-4>* {
    flex: 1 1 100%;
    min-width: unset;
  }
}

@media (max-width: 640px) {
  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-stretch {
    align-items: stretch;
  }

  .sm\:gap-1 {
    gap: 0.25rem;
  }
}

/* =============================================================================
   HIGH-DENSITY LAYOUT SYSTEM
   ============================================================================= */

/* Compact spacing utilities for information density */
.space-tight {
  --space: 0.125rem;
  /* 2px */
}

.space-compact {
  --space: 0.25rem;
  /* 4px */
}

.space-dense {
  --space: 0.5rem;
  /* 8px */
}

.space-normal {
  --space: 1rem;
  /* 16px */
}

/* Multi-column grid layouts */
.grid-dense {
  display: grid;
  gap: var(--space, 1rem);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-dense-sm {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.grid-dense-lg {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

/* Three-panel layout for maximum content */
.layout-triple {
  display: grid;
  grid-template-columns: 250px 1fr 300px;
  grid-template-areas: "sidebar main panel";
  gap: 1rem;
  min-height: 100vh;
}

.layout-triple>.sidebar {
  grid-area: sidebar;
  overflow-y: auto;
}

.layout-triple>.main {
  grid-area: main;
  overflow-y: auto;
}

.layout-triple>.panel {
  grid-area: panel;
  overflow-y: auto;
}

/* Dashboard grid system */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: auto;
  gap: 0.75rem;
  padding: 0.75rem;
}

.grid-span-1 {
  grid-column: span 1;
}

.grid-span-2 {
  grid-column: span 2;
}

.grid-span-3 {
  grid-column: span 3;
}

.grid-span-4 {
  grid-column: span 4;
}

.grid-span-5 {
  grid-column: span 5;
}

.grid-span-6 {
  grid-column: span 6;
}

.grid-span-7 {
  grid-column: span 7;
}

.grid-span-8 {
  grid-column: span 8;
}

.grid-span-9 {
  grid-column: span 9;
}

.grid-span-10 {
  grid-column: span 10;
}

.grid-span-11 {
  grid-column: span 11;
}

.grid-span-12 {
  grid-column: span 12;
}

/* Information cards with high density */
.info-card {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  box-shadow: var(--gb-shadow-1);
}

.info-card-compact {
  padding: 0.5rem;
  gap: 0.375rem;
}

.info-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--gb-accent2);
}

.info-card-body {
  flex: 1;
  font-size: 0.8125rem;
  line-height: 1.4;
}

.info-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  opacity: 0.8;
  margin-top: 0.375rem;
}

/* Collapsible sections */
.collapsible {
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.collapsible-header {
  background: var(--gb-bg1);
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.collapsible-header:hover {
  background: var(--gb-surface-2);
}

.collapsible-content {
  padding: 0.75rem;
  border-top: 1px solid var(--gb-border);
  display: none;
}

.collapsible.expanded .collapsible-content {
  display: block;
}

.collapsible-icon {
  transition: transform 0.2s ease;
}

.collapsible.expanded .collapsible-icon {
  transform: rotate(180deg);
}

/* Accordions for layered information */
.accordion {
  border: 1px solid var(--gb-border);
  border-radius: 6px;
}

.accordion-item {
  border-bottom: 1px solid var(--gb-border);
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-button {
  width: 100%;
  padding: 0.625rem 0.875rem;
  background: var(--gb-bg1);
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: inherit;
  transition: background-color 0.2s ease;
}

.accordion-button:hover {
  background: rgba(235, 219, 178, 0.05);
}

.accordion-content {
  padding: 0.75rem 0.875rem;
  font-size: 0.8125rem;
  line-height: 1.5;
}

/* Tab system for information layers */
.tabs {
  border-bottom: 2px solid rgba(235, 219, 178, 0.1);
  margin-bottom: 1rem;
}

.tabs-nav {
  display: flex;
  gap: 0;
  margin-bottom: -2px;
}

.tab-button {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.7);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--gb-fg);
  background: var(--gb-surface-2);
}

.tab-button.active {
  color: var(--gb-accent2);
  border-bottom-color: var(--gb-accent2);
  background: rgba(250, 189, 47, 0.1);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Data summary boxes */
.summary-box {
  background: linear-gradient(135deg, var(--gb-bg1) 0%, rgba(196, 154, 23, 0.08) 100%);
  border-left: 4px solid var(--gb-accent);
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 0 4px 4px 0;
}

.summary-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--gb-accent2);
  margin-bottom: 0.375rem;
}

.summary-content {
  font-size: 0.8125rem;
  line-height: 1.4;
}

.summary-stats {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-weight: 700;
  font-size: 1.125rem;
  color: var(--gb-accent2);
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* Quick info panels */
.quick-info {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  padding: 0.5rem;
  margin: 0.25rem 0;
}

.quick-info-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--gb-accent2);
  margin-bottom: 0.25rem;
}

.quick-info-value {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Responsive adjustments for density */
@media (max-width: 1200px) {
  .layout-triple {
    grid-template-columns: 200px 1fr 250px;
    gap: 0.75rem;
  }

  .dashboard-grid {
    gap: 0.5rem;
    padding: 0.5rem;
  }
}

@media (max-width: 768px) {
  .layout-triple {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  .layout-triple>.sidebar,
  .layout-triple>.panel {
    display: none;
  }

  .grid-dense {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .grid-span-1,
  .grid-span-2,
  .grid-span-3,
  .grid-span-4,
  .grid-span-5,
  .grid-span-6,
  .grid-span-7,
  .grid-span-8,
  .grid-span-9,
  .grid-span-10,
  .grid-span-11,
  .grid-span-12 {
    grid-column: span 1;
  }
}

/* =============================================================================
   VISUAL HIERARCHY & TYPOGRAPHY SYSTEM
   ============================================================================= */

/* Typography scale for information density */
.text-micro {
  font-size: 0.6875rem;
  /* 11px */
  line-height: 1.3;
}

.text-tiny {
  font-size: 0.75rem;
  /* 12px */
  line-height: 1.4;
}

.text-small {
  font-size: 0.8125rem;
  /* 13px */
  line-height: 1.4;
}

.text-base {
  font-size: 0.875rem;
  /* 14px */
  line-height: 1.5;
}

.text-large {
  font-size: 1rem;
  /* 16px */
  line-height: 1.5;
}

.text-xlarge {
  font-size: 1.125rem;
  /* 18px */
  line-height: 1.4;
}

/* Font weights */
.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Color hierarchy */
.text-primary {
  color: var(--gb-fg);
}

.text-secondary {
  color: rgba(235, 219, 178, 0.8);
}

.text-muted {
  color: rgba(235, 219, 178, 0.6);
}

.text-accent {
  color: var(--gb-accent2);
}

.text-warning {
  color: #f59e0b;
}

.text-success {
  color: #10b981;
}

.text-error {
  color: #ef4444;
}

/* Dense heading system */
.heading-primary {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gb-accent2);
  margin: 0 0 0.75rem 0;
  line-height: 1.2;
}

.heading-secondary {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--gb-fg);
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.heading-tertiary {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gb-accent);
  margin: 0 0 0.375rem 0;
  line-height: 1.3;
}

.heading-quaternary {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(235, 219, 178, 0.9);
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Compact spacing system */
.space-0 {
  margin: 0;
  padding: 0;
}

.space-1 {
  margin: 0.125rem;
}

.space-2 {
  margin: 0.25rem;
}

.space-3 {
  margin: 0.375rem;
}

.space-4 {
  margin: 0.5rem;
}

.space-6 {
  margin: 0.75rem;
}

.space-8 {
  margin: 1rem;
}

.p-0 {
  padding: 0;
}

.p-1 {
  padding: 0.125rem;
}

.p-2 {
  padding: 0.25rem;
}

.p-3 {
  padding: 0.375rem;
}

.p-4 {
  padding: 0.5rem;
}

.p-6 {
  padding: 0.75rem;
}

.p-8 {
  padding: 1rem;
}

.m-0 {
  margin: 0;
}

.m-1 {
  margin: 0.125rem;
}

.m-2 {
  margin: 0.25rem;
}

.m-3 {
  margin: 0.375rem;
}

.m-4 {
  margin: 0.5rem;
}

.m-6 {
  margin: 0.75rem;
}

.m-8 {
  margin: 1rem;
}

/* Icon and symbol utilities */
.icon {
  width: 1em;
  height: 1em;
  display: inline-block;
  vertical-align: middle;
}

.icon-small {
  width: 0.875em;
  height: 0.875em;
}

.icon-large {
  width: 1.25em;
  height: 1.25em;
}

.icon-text::before {
  content: attr(data-icon);
  margin-right: 0.375em;
  font-weight: normal;
}

/* Status indicators */
.status-dot {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-right: 0.375rem;
}

.status-online {
  background-color: #10b981;
}

.status-warning {
  background-color: #f59e0b;
}

.status-error {
  background-color: #ef4444;
}

.status-offline {
  background-color: #6b7280;
}

/* Data visualization containers */
.chart-container {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 0.75rem;
  margin: 0.5rem 0;
  position: relative;
  box-shadow: var(--gb-shadow-1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(235, 219, 178, 0.1);
}

.chart-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gb-accent2);
  margin: 0;
}

.chart-subtitle {
  font-size: 0.75rem;
  color: rgba(235, 219, 178, 0.7);
  margin: 0;
}

.chart-actions {
  display: flex;
  gap: 0.25rem;
}

.chart-body {
  position: relative;
  min-height: 200px;
}

/* Metric cards for key statistics */
.metric-card {
  background: linear-gradient(135deg, var(--gb-bg1) 0%, rgba(196, 154, 23, 0.04) 100%);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 0.75rem;
  text-align: center;
  transition: all 0.2s ease;
}

.metric-card:hover {
  border-color: var(--gb-accent);
  transform: translateY(-1px);
  box-shadow: var(--gb-shadow-2);
}

.metric-value {
  display: block;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--gb-accent2);
  line-height: 1.1;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 0.125rem;
}

.metric-change {
  font-size: 0.6875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}

.metric-change.neutral {
  color: rgba(235, 219, 178, 0.6);
}

/* Information tables with high density */
.dense-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8125rem;
  background: var(--gb-bg1);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: var(--gb-shadow-1);
}

.dense-table th {
  background: rgba(215, 153, 33, 0.1);
  padding: 0.5rem 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.75rem;
  color: var(--gb-accent2);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border-bottom: 1px solid rgba(235, 219, 178, 0.1);
}

.dense-table td {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid rgba(235, 219, 178, 0.05);
  vertical-align: top;
}

.dense-table tr:hover {
  background: rgba(235, 219, 178, 0.03);
}

.dense-table .numeric {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

/* List styles for scannable content */
.list-dense {
  margin: 0;
  padding: 0;
  list-style: none;
}

.list-dense li {
  padding: 0.375rem 0;
  border-bottom: 1px solid rgba(235, 219, 178, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8125rem;
}

.list-dense li:last-child {
  border-bottom: none;
}

.list-item-primary {
  font-weight: 500;
  color: var(--gb-fg);
}

.list-item-secondary {
  font-size: 0.75rem;
  color: rgba(235, 219, 178, 0.7);
}

/* Badge system for categories and status */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  font-size: 0.6875rem;
  font-weight: 600;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  line-height: 1;
}

.badge-primary {
  background: rgba(250, 189, 47, 0.2);
  color: var(--gb-accent2);
  border: 1px solid rgba(250, 189, 47, 0.3);
}

.badge-secondary {
  background: rgba(235, 219, 178, 0.1);
  color: rgba(235, 219, 178, 0.8);
  border: 1px solid rgba(235, 219, 178, 0.2);
}

.badge-success {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.badge-error {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* =============================================================================
   INTERACTIVE COMPONENTS & PERFORMANCE
   ============================================================================= */

/* Interactive data table styles */
.interactive-data-table {
  background: var(--gb-bg1);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--gb-surface-2);
  border-bottom: 1px solid var(--gb-border);
  flex-wrap: wrap;
  gap: 0.75rem;
}

.search-container {
  flex: 1;
  min-width: 200px;
}

.filter-container {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-select {
  background: var(--gb-surface-2);
  border: 1px solid var(--gb-border);
  border-radius: 4px;
  padding: 0.375rem 0.5rem;
  font-size: 0.8125rem;
  color: var(--gb-fg);
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(250, 189, 47, 0.1);
}

.table-info {
  font-size: 0.8125rem;
  color: rgba(235, 219, 178, 0.8);
}

.table-container {
  overflow-x: auto;
  max-height: 400px;
  scrollbar-width: thin;
  scrollbar-color: var(--gb-accent) transparent;
}

.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(235, 219, 178, 0.1);
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--gb-accent);
  border-radius: 3px;
}

.interactive-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.interactive-table th.sortable:hover {
  background: rgba(250, 189, 47, 0.15);
}

.th-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.sort-indicator {
  opacity: 0.5;
  font-size: 0.75rem;
  transition: opacity 0.2s ease;
}

.interactive-table th.sortable:hover .sort-indicator {
  opacity: 1;
}

.interactive-row {
  transition: all 0.2s ease;
}

.interactive-row.selected {
  background: rgba(250, 189, 47, 0.1) !important;
}

.select-column,
.actions-column {
  width: 50px;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
}

.action-buttons button {
  padding: 0.25rem;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-buttons button:hover {
  background: rgba(235, 219, 178, 0.1);
  border-color: rgba(235, 219, 178, 0.2);
}

/* Pagination styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--gb-surface-2);
  border-top: 1px solid var(--gb-border);
}

.pagination-controls {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.pagination-btn {
  padding: 0.375rem 0.75rem;
  border: 1px solid rgba(235, 219, 178, 0.2);
  background: transparent;
  color: var(--gb-fg);
  font-size: 0.8125rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(235, 219, 178, 0.1);
  border-color: var(--gb-accent);
}

.pagination-btn.active {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border-color: var(--gb-accent);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Progressive disclosure styles */
.progressive-disclosure {
  margin: 0.5rem 0;
}

.disclosure-summary {
  margin-bottom: 0.25rem;
}

.disclosure-toggle {
  background: none;
  border: none;
  padding: 0;
  color: var(--gb-accent2);
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.disclosure-toggle:hover {
  color: var(--gb-accent);
  text-decoration: none;
}

.disclosure-details {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(235, 219, 178, 0.05);
  border-left: 3px solid var(--gb-accent);
  border-radius: 0 4px 4px 0;
  font-size: 0.8125rem;
  line-height: 1.5;
}

/* Enhanced tooltips */
.tooltip-top::after {
  bottom: 100%;
  margin-bottom: 0.25rem;
}

.tooltip-bottom::after {
  top: 100%;
  bottom: auto;
  margin-top: 0.25rem;
  margin-bottom: 0;
}

.tooltip-bottom::before {
  top: 100%;
  bottom: auto;
  border-top-color: transparent;
  border-bottom-color: rgba(40, 40, 40, 0.95);
}

.tooltip-left::after {
  right: 100%;
  top: 50%;
  bottom: auto;
  left: auto;
  transform: translateY(-50%);
  margin-right: 0.25rem;
  margin-bottom: 0;
}

.tooltip-right::after {
  left: 100%;
  top: 50%;
  bottom: auto;
  right: auto;
  transform: translateY(-50%);
  margin-left: 0.25rem;
  margin-bottom: 0;
}

/* Card grid filters */
.filter-controls {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 6px;
}

.filter-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: end;
  margin-bottom: 0.5rem;
}

.filter-group,
.sort-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 150px;
}

.results-info {
  text-align: right;
}

/* Performance optimizations */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-load.loaded {
  opacity: 1;
}

/* Virtual scrolling container */
.virtual-scroll-container {
  height: 400px;
  overflow-y: auto;
  position: relative;
}

.virtual-scroll-spacer {
  pointer-events: none;
}

/* Skeleton loading for better perceived performance */
.skeleton {
  background: linear-gradient(90deg,
      rgba(235, 219, 178, 0.1) 25%,
      rgba(235, 219, 178, 0.2) 50%,
      rgba(235, 219, 178, 0.1) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 2s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

.skeleton-text {
  height: 1rem;
  margin: 0.25rem 0;
}

.skeleton-title {
  height: 1.5rem;
  width: 60%;
  margin: 0.5rem 0;
}

.skeleton-button {
  height: 2rem;
  width: 100px;
  margin: 0.25rem;
}

/* Accessibility enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus indicators */
button:focus-visible,
input:focus-visible,
select:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--gb-accent2);
  outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {

  .info-card,
  .metric-card,
  .chart-container {
    border-width: 2px;
    border-color: var(--gb-fg);
  }

  .badge {
    border-width: 2px;
  }
}

/* Print optimizations */
@media print {
  .interactive-data-table {
    box-shadow: none;
    border: 1px solid #000;
  }

  .table-controls,
  .pagination-container {
    display: none;
  }

  .action-buttons {
    display: none;
  }

  .select-column {
    display: none;
  }
}
