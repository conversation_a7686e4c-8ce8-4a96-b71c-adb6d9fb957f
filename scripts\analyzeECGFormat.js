import fs from 'fs';

console.log('🔍 Examining ECG file format...');

const ecgFile = './data/d1namo/diabetes_subset_ecg_data/001/sensor_data/2014_10_02-10_56_44/2014_10_02-10_56_44_ECG.csv';
const content = fs.readFileSync(ecgFile, 'utf8');
const lines = content.split('\n').slice(0, 10);

console.log('First 10 lines of ECG file:');
lines.forEach((line, i) => {
  console.log(`${i + 1}: ${line}`);
});

console.log('\n🔍 Analyzing date format...');
const dataLine = lines[1]; // First data line after header
const parts = dataLine.split(',');
console.log('Time part:', parts[0]);
console.log('ECG value part:', parts[1]);

// Test date parsing
const timeStr = parts[0];
console.log('Testing date parsing for:', timeStr);

try {
  const date1 = new Date(timeStr);
  console.log('Date() constructor:', date1.toString());
  console.log('Is valid:', !isNaN(date1.getTime()));
} catch (e) {
  console.log('Date() error:', e.message);
}

// Try different parsing approaches
try {
  // European date format: DD/MM/YYYY HH:mm:ss.SSS
  const parts = timeStr.split(' ');
  const datePart = parts[0];
  const timePart = parts[1];

  const [day, month, year] = datePart.split('/');
  const [hours, minutes, seconds] = timePart.split(':');
  const [sec, ms] = seconds.split('.');

  const date2 = new Date(year, month - 1, day, hours, minutes, sec, ms);
  console.log('Manual parsing:', date2.toString());
  console.log('Is valid:', !isNaN(date2.getTime()));
  console.log('ISO string:', date2.toISOString());
} catch (e) {
  console.log('Manual parsing error:', e.message);
}
