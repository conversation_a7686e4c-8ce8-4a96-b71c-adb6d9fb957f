import { useEffect, useState } from 'react';
import styles from '../../features/neo4j/components/QueryRunner.module.css';
import AIRecommendationService from '../../services/aiService';
import Neo4jService from '../../services/neo4jService';
import { calculateAGPData } from '../../utils/agpCalculation';
import { exportAGPReportCSV, exportAGPReportJSON, generateAGPReport } from '../../utils/agpReporting';
import AGPChart from '../AGPChart';
import AGPStatistics from '../AGPStatistics';
import AIControlPanel from '../AIControlPanel';

function QueryRunner() {
    const [neo4jService] = useState(() => new Neo4jService());
    const [aiService] = useState(() => new AIRecommendationService());
    const [connectionStatus, setConnectionStatus] = useState('disconnected');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // Query state
    const [selectedQueryKey, setSelectedQueryKey] = useState('');
    const [customQuery, setCustomQuery] = useState('');
    const [queryParameters, setQueryParameters] = useState({ patientId: 'Patient_1' });

    // Results state
    const [queryResults, setQueryResults] = useState([]);
    const [agpData, setAgpData] = useState(null);
    const [showAGPChart, setShowAGPChart] = useState(false);
    const [queryStats, setQueryStats] = useState(null);

    // UI state
    const [activeTab, setActiveTab] = useState('predefined'); // 'predefined', 'custom', or 'ai-control'
    const [showPerformanceStats, setShowPerformanceStats] = useState(false);
    const [agpSettings, setAgpSettings] = useState({
        hypoThreshold: 70,
        hyperThreshold: 180,
        targetMin: 70,
        targetMax: 180
    });

    // AI state
    const [aiSettings, setAiSettings] = useState({});
    const [aiRecommendations, setAiRecommendations] = useState(null);
    const [aiInsights, setAiInsights] = useState(null);
    const [aiLoading, setAiLoading] = useState(false);
    const [showAIRecommendations, setShowAIRecommendations] = useState(true);

    const commonQueries = Neo4jService.getCommonQueries();

    useEffect(() => {
        // Auto-connect on component mount
        handleConnect();
    }, []);

    const handleConnect = async () => {
        setLoading(true);
        setError(null);

        try {
            await neo4jService.connect();
            setConnectionStatus('connected');
        } catch (err) {
            setError(err.message);
            setConnectionStatus('error');
        } finally {
            setLoading(false);
        }
    };

    const handleQuerySelect = (queryKey) => {
        setSelectedQueryKey(queryKey);
        const query = commonQueries[queryKey];
        if (query) {
            setCustomQuery(query.query);
            // Auto-execute predefined queries for better UX
            if (connectionStatus === 'connected') {
                executeQuery(query.query, queryParameters);
            }
        }
    };

    const executeQuery = async (query, parameters) => {
        setLoading(true);
        setError(null);
        setQueryResults([]);
        setAgpData(null);
        setShowAGPChart(false);

        try {
            // Enforce read-only safety: block write or administrative Cypher
            const forbidden = /\b(CREATE|MERGE|DELETE|DETACH\s+DELETE|SET\s+|REMOVE\s+|CALL\s+db\.|USE\s+|LOAD\s+CSV|APOC\.\w+\.(?:(?!(toJSON|convert|map|coll|text|math|date|temporal)).)*)\b/i;
            if (forbidden.test(query)) {
                throw new Error('Write or administrative queries are not allowed in this app. This interface is read-only and intended for data exploration and visualization.');
            }

            // Ensure required parameters have sensible defaults
            const safeParameters = {
                ...parameters,
                days: parameters?.days || 30,
                limit: parameters?.limit || 1000
            };

            const startTime = performance.now();
            const results = await neo4jService.executeQuery(query, safeParameters);
            const endTime = performance.now();

            // Normalize results to an array of rows
            const rows = Array.isArray(results)
                ? results
                : (results && Array.isArray(results.records) ? results.records : []);

            setQueryResults(rows);
            setQueryStats({
                executionTime: Math.round(endTime - startTime),
                recordCount: rows.length,
                timestamp: new Date().toLocaleString()
            });

            // Check if this is a glucose over time query for AGP generation
            if (selectedQueryKey === 'glucose_over_time' ||
                query.toLowerCase().includes('glucose') &&
                query.toLowerCase().includes('timestamp')) {

                const calculatedAGP = calculateAGPData(rows);
                if (calculatedAGP) {
                    setAgpData(calculatedAGP);
                    setShowAGPChart(true);

                    // Generate AI recommendations for AGP data
                    generateAIRecommendations(calculatedAGP, safeParameters);
                }
            } else {
                // Generate AI insights for other query types
                generateAIInsights(rows, selectedQueryKey, safeParameters);
            }

        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const handleExecuteQuery = async () => {
        if (connectionStatus !== 'connected') {
            setError('Please connect to Neo4j first');
            return;
        }

        if (!customQuery.trim()) {
            setError('Please enter a Cypher query');
            return;
        }

        await executeQuery(customQuery, queryParameters);
    };

    const handleParameterChange = (key, value) => {
        setQueryParameters(prev => ({
            ...prev,
            [key]: value
        }));
    };

    // Export functionality
    const exportResults = (format) => {
        if (!queryResults.length) return;

        let content;
        let filename;
        let mimeType;

        if (format === 'json') {
            content = JSON.stringify(queryResults, null, 2);
            filename = `neo4j_results_${new Date().toISOString().split('T')[0]}.json`;
            mimeType = 'application/json';
        } else if (format === 'csv') {
            // Convert results to CSV
            if (queryResults.length === 0) return;

            // Get all unique keys from all results
            const allKeys = new Set();
            queryResults.forEach(result => {
                Object.keys(result).forEach(key => allKeys.add(key));
            });

            const headers = Array.from(allKeys);
            const csvContent = [
                headers.join(','),
                ...queryResults.map(result =>
                    headers.map(header => {
                        let value = result[header];
                        if (value === null || value === undefined) return '';
                        if (typeof value === 'object') value = JSON.stringify(value);
                        // Escape quotes and wrap in quotes if contains comma
                        value = String(value).replace(/"/g, '""');
                        return value.includes(',') ? `"${value}"` : value;
                    }).join(',')
                )
            ].join('\n');

            content = csvContent;
            filename = `neo4j_results_${new Date().toISOString().split('T')[0]}.csv`;
            mimeType = 'text/csv';
        }

        // Create and trigger download
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    // AI Functions
    const generateAIRecommendations = async (agpData, patientContext = {}) => {
        if (!aiSettings.enabled || !showAIRecommendations) return;

        setAiLoading(true);
        try {
            console.log('🤖 Generating AI recommendations for AGP data...');
            const recommendations = await aiService.generateAGPRecommendations(agpData, {
                patientId: patientContext.patientId || queryParameters.patientId,
                ...patientContext
            });
            setAiRecommendations(recommendations);
            console.log('✅ AI recommendations generated:', recommendations);
        } catch (error) {
            console.error('❌ Error generating AI recommendations:', error);
            setAiRecommendations({
                error: true,
                message: 'Failed to generate AI recommendations',
                fallback: true
            });
        } finally {
            setAiLoading(false);
        }
    };

    const generateAIInsights = async (queryResults, queryType, patientContext = {}) => {
        if (!aiSettings.enabled || !showAIRecommendations) return;

        setAiLoading(true);
        try {
            console.log('🤖 Generating AI insights for query results...');
            const insights = await aiService.generateClinicalInsights(queryResults, queryType, {
                patientId: patientContext.patientId || queryParameters.patientId,
                queryType: queryType,
                ...patientContext
            });
            setAiInsights(insights);
            console.log('✅ AI insights generated:', insights);
        } catch (error) {
            console.error('❌ Error generating AI insights:', error);
            setAiInsights({
                error: true,
                message: 'Failed to generate AI insights',
                fallback: true
            });
        } finally {
            setAiLoading(false);
        }
    };

    const handleAISettingsChange = (newSettings) => {
        setAiSettings(newSettings);
        console.log('🔧 AI settings updated:', newSettings);
    };

    const toggleAIRecommendations = () => {
        setShowAIRecommendations(!showAIRecommendations);
    };

    const refreshAIRecommendations = () => {
        if (agpData) {
            generateAIRecommendations(agpData, queryParameters);
        } else if (queryResults.length > 0) {
            generateAIInsights(queryResults, selectedQueryKey, queryParameters);
        }
    };

    return (
        <div className={styles['query-runner']}>
            <div className={styles['query-runner-header']}>
                <h2>Neo4j Query Runner</h2>
                <div className={styles['header-controls']}>
                    <div className={`${styles['connection-status']} ${styles[connectionStatus]}`}>
                        <span className={styles['status-indicator']}></span>
                        {connectionStatus === 'connected' ? 'Connected' :
                            connectionStatus === 'error' ? 'Error' : 'Disconnected'}
                    </div>
                    <button
                        onClick={() => setShowPerformanceStats(!showPerformanceStats)}
                        className={styles['stats-button']}
                        title="View Performance Statistics"
                    >
                        📊 Stats
                    </button>
                    <button
                        onClick={handleConnect}
                        disabled={loading || connectionStatus === 'connected'}
                        className={styles['connect-button']}
                    >
                        {loading ? 'Connecting...' : 'Connect'}
                    </button>
                </div>
            </div>

            {error && (
                <div className={styles['error-message']}>
                    <strong>Error:</strong> {error}
                </div>
            )}

            {showPerformanceStats && (
                <div className="performance-stats">
                    <h3>Performance Statistics</h3>
                    <div className="stats-grid">
                        {(() => {
                            const stats = neo4jService.getPerformanceStats();
                            return (
                                <>
                                    <div className="stat-card">
                                        <h4>Total Queries</h4>
                                        <p>{stats.totalQueries}</p>
                                    </div>
                                    <div className="stat-card">
                                        <h4>Avg Execution Time</h4>
                                        <p>{stats.avgExecutionTime.toFixed(2)}ms</p>
                                    </div>
                                    <div className="stat-card">
                                        <h4>Cache Hit Rate</h4>
                                        <p>{stats.cacheHitRate}</p>
                                    </div>
                                    <div className="stat-card">
                                        <h4>Cache Size</h4>
                                        <p>{stats.cacheSize} entries</p>
                                    </div>
                                    {stats.slowQueries.length > 0 && (
                                        <div className="stat-card full-width">
                                            <h4>Recent Slow Queries (&gt;1s)</h4>
                                            <div className="slow-queries">
                                                {stats.slowQueries.slice(-3).map((query, index) => (
                                                    <div key={index} className="slow-query">
                                                        <code>{query.query}...</code>
                                                        <span className="execution-time">{query.executionTime}ms</span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </>
                            );
                        })()}
                    </div>
                    <div className="stats-actions">
                        <button
                            onClick={() => neo4jService.clearPerformanceStats()}
                            className="clear-stats-button"
                        >
                            Clear Statistics
                        </button>
                    </div>
                </div>
            )}

            <div className={styles['query-tabs']}>
                <button
                    className={`${styles['tab-button']} ${activeTab === 'predefined' ? styles['active'] : ''}`}
                    onClick={() => setActiveTab('predefined')}
                >
                    Predefined Queries
                </button>
                <button
                    className={`${styles['tab-button']} ${activeTab === 'custom' ? styles['active'] : ''}`}
                    onClick={() => setActiveTab('custom')}
                >
                    Custom Query
                </button>
                <button
                    className={`${styles['tab-button']} ${activeTab === 'agp-settings' ? styles['active'] : ''}`}
                    onClick={() => setActiveTab('agp-settings')}
                >
                    AGP Settings
                </button>
                <button
                    className={`${styles['tab-button']} ${activeTab === 'ai-control' ? styles['active'] : ''}`}
                    onClick={() => setActiveTab('ai-control')}
                >
                    🤖 AI Control
                </button>
            </div>

            <div className={styles['query-content']}>
                {activeTab === 'predefined' && (
                    <div className={styles['predefined-queries']}>
                        <h3>Healthcare & Research Queries</h3>
                        <div className={styles['query-categories']}>
                            {(() => {
                                // Group queries by category
                                const categories = {};
                                Object.entries(commonQueries).forEach(([key, queryInfo]) => {
                                    const category = queryInfo.category || 'General';
                                    if (!categories[category]) {
                                        categories[category] = [];
                                    }
                                    categories[category].push([key, queryInfo]);
                                });

                                return Object.entries(categories).map(([category, queries]) => (
                                    <div key={category} className={styles['query-category']}>
                                        <h4 className={styles['category-header']}>{category}</h4>
                                        <div className={styles['query-grid']}>
                                            {queries.map(([key, queryInfo]) => (
                                                <div
                                                    key={key}
                                                    className={`${styles['query-card']} ${selectedQueryKey === key ? styles['selected'] : ''}`}
                                                >
                                                    <div className={styles['query-card-content']} onClick={() => handleQuerySelect(key)}>
                                                        <h5>{queryInfo.name}</h5>
                                                        <p>{queryInfo.description}</p>
                                                        {key.includes('glucose') && (
                                                            <span className={styles['agp-badge']}>AGP Enabled</span>
                                                        )}
                                                        {queryInfo.parameters && queryInfo.parameters.length > 0 && (
                                                            <div className={styles['parameters-info']}>
                                                                <small>Parameters: {queryInfo.parameters.join(', ')}</small>
                                                            </div>
                                                        )}
                                                    </div>
                                                    <div className={`${styles['query-card-actions']} ${styles['compact-actions']}`}>
                                                        <button
                                                            className={styles['execute-button']}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                if (connectionStatus === 'connected') {
                                                                    setSelectedQueryKey(key);
                                                                    setCustomQuery(queryInfo.query);
                                                                    executeQuery(queryInfo.query, queryParameters);
                                                                } else {
                                                                    setError('Please connect to Neo4j first');
                                                                }
                                                            }}
                                                            disabled={connectionStatus !== 'connected' || loading}
                                                        >
                                                            {loading && selectedQueryKey === key ? '⏳' : '▶️'} Execute
                                                        </button><button
                                                            className={styles['view-button']}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleQuerySelect(key);
                                                                setActiveTab('custom');
                                                            }}
                                                        >
                                                            👁️ View
                                                        </button>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ));
                            })()}
                        </div>
                    </div>
                )}

                {activeTab === 'agp-settings' && (
                    <div className="agp-settings-panel">
                        <h3>AGP Configuration</h3>
                        <div className="agp-settings-grid">
                            <div className="setting-group">
                                <h4>Glucose Thresholds</h4>
                                <div className="threshold-inputs">
                                    <div className="threshold-input">
                                        <label>Hypoglycemia Threshold:</label>
                                        <input
                                            type="number"
                                            value={agpSettings.hypoThreshold}
                                            onChange={(e) => setAgpSettings(prev => ({
                                                ...prev,
                                                hypoThreshold: parseInt(e.target.value) || 70
                                            }))}
                                            min="50"
                                            max="100"
                                        />
                                        <span className="unit">mg/dL</span>
                                    </div>
                                    <div className="threshold-input">
                                        <label>Hyperglycemia Threshold:</label>
                                        <input
                                            type="number"
                                            value={agpSettings.hyperThreshold}
                                            onChange={(e) => setAgpSettings(prev => ({
                                                ...prev,
                                                hyperThreshold: parseInt(e.target.value) || 180
                                            }))}
                                            min="150"
                                            max="250"
                                        />
                                        <span className="unit">mg/dL</span>
                                    </div>
                                </div>
                            </div>
                            <div className="setting-group">
                                <h4>Target Range</h4>
                                <div className="threshold-inputs">
                                    <div className="threshold-input">
                                        <label>Target Range Min:</label>
                                        <input
                                            type="number"
                                            value={agpSettings.targetMin}
                                            onChange={(e) => setAgpSettings(prev => ({
                                                ...prev,
                                                targetMin: parseInt(e.target.value) || 70
                                            }))}
                                            min="50"
                                            max="120"
                                        />
                                        <span className="unit">mg/dL</span>
                                    </div>
                                    <div className="threshold-input">
                                        <label>Target Range Max:</label>
                                        <input
                                            type="number"
                                            value={agpSettings.targetMax}
                                            onChange={(e) => setAgpSettings(prev => ({
                                                ...prev,
                                                targetMax: parseInt(e.target.value) || 180
                                            }))}
                                            min="150"
                                            max="250"
                                        />
                                        <span className="unit">mg/dL</span>
                                    </div>
                                </div>
                            </div>
                            <div className="setting-group">
                                <h4>Preset Configurations</h4>
                                <div className="preset-buttons">
                                    <button
                                        onClick={() => setAgpSettings({
                                            hypoThreshold: 70,
                                            hyperThreshold: 180,
                                            targetMin: 70,
                                            targetMax: 180
                                        })}
                                        className="preset-button"
                                    >
                                        Standard Adult (70-180)
                                    </button>
                                    <button
                                        onClick={() => setAgpSettings({
                                            hypoThreshold: 70,
                                            hyperThreshold: 250,
                                            targetMin: 70,
                                            targetMax: 250
                                        })}
                                        className="preset-button"
                                    >
                                        Elderly/High Risk (70-250)
                                    </button>
                                    <button
                                        onClick={() => setAgpSettings({
                                            hypoThreshold: 63,
                                            hyperThreshold: 140,
                                            targetMin: 63,
                                            targetMax: 140
                                        })}
                                        className="preset-button"
                                    >
                                        Pediatric (63-140)
                                    </button>
                                    <button
                                        onClick={() => setAgpSettings({
                                            hypoThreshold: 63,
                                            hyperThreshold: 160,
                                            targetMin: 63,
                                            targetMax: 160
                                        })}
                                        className="preset-button"
                                    >
                                        Pregnancy (63-160)
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div className="agp-settings-info">
                            <h4>Clinical Guidelines</h4>
                            <ul>
                                <li><strong>Time in Range Goal:</strong> ≥70% in target range</li>
                                <li><strong>Time Below Range:</strong> &lt;4% below 70 mg/dL, &lt;1% below 54 mg/dL</li>
                                <li><strong>Coefficient of Variation:</strong> ≤36% for stable glucose</li>
                                <li><strong>GMI Target:</strong> &lt;7% for most adults with diabetes</li>
                            </ul>
                        </div>
                    </div>
                )}

                {activeTab === 'ai-control' && (
                    <AIControlPanel
                        onSettingsChange={handleAISettingsChange}
                        currentSettings={aiSettings}
                    />
                )}

                <div className={styles['query-editor']}>
                    <h3>Query Editor</h3>
                    <div className={styles['parameters-section']}>
                        <h4>Query Parameters</h4>
                        <div className={styles['parameter-inputs']}>
                            <div className={styles['parameter-input']}>
                                <label>Patient ID:</label>
                                <select
                                    value={queryParameters.patientId}
                                    onChange={(e) => handleParameterChange('patientId', e.target.value)}
                                >
                                    <option value="Patient_1">Patient_1 - John Anderson (Type 2 Diabetes)</option>
                                    <option value="Patient_2">Patient_2 - Maria Garcia (Type 1 Diabetes)</option>
                                    <option value="Patient_3">Patient_3 - Robert Chen (Prediabetes)</option>
                                    <option value="Patient_4">Patient_4 - Sarah Johnson (Gestational Diabetes)</option>
                                    <option value="Patient_5">Patient_5 - David Williams (Type 2 Diabetes)</option>
                                    <option value="Patient_6">Patient_6 - Emily Davis (Type 1 Diabetes)</option>
                                    <option value="Patient_7">Patient_7 - Michael Brown (Type 2 Diabetes)</option>
                                </select>
                            </div>
                            <div className="parameter-input">
                                <label>Date Range (days):</label>
                                <input
                                    type="number"
                                    value={queryParameters.days || 30}
                                    onChange={(e) => handleParameterChange('days', parseInt(e.target.value))}
                                    placeholder="30"
                                    min="1"
                                    max="365"
                                />
                            </div>
                            <div className="parameter-input">
                                <label>Limit Results:</label>
                                <input
                                    type="number"
                                    value={queryParameters.limit || 100}
                                    onChange={(e) => handleParameterChange('limit', parseInt(e.target.value))}
                                    placeholder="100"
                                    min="1"
                                    max="1000"
                                />
                            </div>
                        </div>
                    </div>

                    <div className={styles['query-input-section']}>
                        <label htmlFor="cypher-query">Cypher Query:</label>
                        <textarea
                            id="cypher-query"
                            value={customQuery}
                            onChange={(e) => setCustomQuery(e.target.value)}
                            placeholder="Enter your Cypher query here..."
                            rows={8}
                            className={styles['query-textarea']}
                        />
                    </div>

                    <div className={styles['query-actions']}>
                        <button
                            onClick={handleExecuteQuery}
                            disabled={loading || connectionStatus !== 'connected' || !customQuery.trim()}
                            className={styles['execute-button']}
                        >
                            {loading ? 'Executing...' : 'Execute Query'}
                        </button>

                        {queryResults.length > 0 && (
                            <div className={styles['export-buttons']}>
                                <button onClick={() => exportResults('json')} className={styles['export-button']}>
                                    Export JSON
                                </button>
                                <button onClick={() => exportResults('csv')} className={styles['export-button']}>
                                    Export CSV
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* Results Section */}
            <div className={styles['results-section']}>
                {queryStats && (
                    <div className={styles['query-stats']}>
                        <h3>Query Results</h3>
                        <div className={styles['stats-grid']}>
                            <div className={styles['stat-item']}>
                                <span className="stat-label">Records:</span>
                                <span className="stat-value">{queryStats.recordCount}</span>
                            </div>
                            <div className={styles['stat-item']}>
                                <span className="stat-label">Execution Time:</span>
                                <span className="stat-value">{queryStats.executionTime}ms</span>
                            </div>
                            <div className={styles['stat-item']}>
                                <span className="stat-label">Executed:</span>
                                <span className="stat-value">{queryStats.timestamp}</span>
                            </div>
                        </div>
                    </div>
                )}

                {/* AGP Chart Section */}
                {showAGPChart && agpData && (
                    <div className="agp-section">
                        <div className="agp-section-header">
                            <h3>Ambulatory Glucose Profile (AGP)</h3>
                            <div className="agp-export-buttons">
                                <button
                                    onClick={() => {
                                        const reportData = generateAGPReport(
                                            queryResults,
                                            { patientId: queryParameters.patientId },
                                            agpSettings
                                        );
                                        if (reportData) exportAGPReportJSON(reportData);
                                    }}
                                    className="agp-export-button"
                                    title="Export comprehensive AGP report as JSON"
                                >
                                    📊 Export AGP Report (JSON)
                                </button>
                                <button
                                    onClick={() => {
                                        const reportData = generateAGPReport(
                                            queryResults,
                                            { patientId: queryParameters.patientId },
                                            agpSettings
                                        );
                                        if (reportData) exportAGPReportCSV(reportData);
                                    }}
                                    className="agp-export-button"
                                    title="Export AGP summary as CSV"
                                >
                                    📈 Export AGP Summary (CSV)
                                </button>
                            </div>
                        </div>

                        <AGPChart
                            agpData={agpData}
                            glucoseData={queryResults}
                            hypoThreshold={agpSettings.hypoThreshold}
                            hyperThreshold={agpSettings.hyperThreshold}
                            className="agp-chart"
                        />

                        {/* AGP Statistics - Complete Feature Set */}
                        <AGPStatistics
                            glucoseData={queryResults}
                            customRanges={{
                                veryLow: { min: 0, max: 54, label: 'Very Low (<54)', color: '#dc2626' },
                                low: { min: 54, max: agpSettings.hypoThreshold, label: `Low (54-${agpSettings.hypoThreshold})`, color: '#f59e0b' },
                                target: { min: agpSettings.targetMin, max: agpSettings.targetMax, label: `Target Range (${agpSettings.targetMin}-${agpSettings.targetMax})`, color: '#10b981' },
                                high: { min: agpSettings.hyperThreshold, max: 250, label: `High (${agpSettings.hyperThreshold}-250)`, color: '#f59e0b' },
                                veryHigh: { min: 250, max: 400, label: 'Very High (>250)', color: '#dc2626' }
                            }}
                            className="agp-statistics"
                        />

                        {/* AI Recommendations Section */}
                        {showAIRecommendations && (aiRecommendations || aiInsights) && (
                            <div className="ai-recommendations-section">
                                <div className="ai-section-header">
                                    <h3>🤖 AI Clinical Recommendations</h3>
                                    <div className="ai-controls">
                                        <button
                                            className="refresh-ai-btn"
                                            onClick={refreshAIRecommendations}
                                            disabled={aiLoading}
                                        >
                                            {aiLoading ? '🔄 Generating...' : '🔄 Refresh AI'}
                                        </button>
                                        <button
                                            className="toggle-ai-btn"
                                            onClick={toggleAIRecommendations}
                                        >
                                            {showAIRecommendations ? '👁️ Hide AI' : '👁️ Show AI'}
                                        </button>
                                    </div>
                                </div>

                                {aiLoading && (
                                    <div className="ai-loading">
                                        <div className="loading-spinner"></div>
                                        <p>Generating AI recommendations...</p>
                                    </div>
                                )}

                                {aiRecommendations && !aiLoading && (
                                    <div className="ai-agp-recommendations">
                                        <h4>📊 AGP Analysis Recommendations</h4>
                                        {aiRecommendations.error ? (
                                            <div className="ai-error">
                                                <p>❌ {aiRecommendations.message}</p>
                                                {aiRecommendations.fallback && (
                                                    <p>✅ Fallback mode enabled - Enhanced rule-based recommendations available.</p>
                                                )}
                                            </div>
                                        ) : (
                                            <div className="recommendations-grid">
                                                {aiRecommendations.recommendations?.map((rec, index) => (
                                                    <div key={index} className={`recommendation-card ${rec.level} ${rec.priority}-priority`}>
                                                        <div className="recommendation-header">
                                                            <span className="recommendation-category">{rec.category}</span>
                                                            <span className={`recommendation-level ${rec.level}`}>
                                                                {rec.level === 'critical' && '🚨'}
                                                                {rec.level === 'high' && '⚠️'}
                                                                {rec.level === 'medium' && '📋'}
                                                                {rec.level === 'excellent' && '✅'}
                                                                {rec.level === 'good' && '👍'}
                                                                {rec.level}
                                                            </span>
                                                        </div>
                                                        <p className="recommendation-message">{rec.message}</p>
                                                        <p className="recommendation-action">{rec.recommendation}</p>
                                                        <div className="recommendation-meta">
                                                            <span className="evidence-level">Evidence: {rec.evidence_level}</span>
                                                            <span className="ai-source">Source: {rec.source}</span>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}

                                        {aiRecommendations.model_used && (
                                            <div className="ai-meta-info">
                                                <p><strong>AI Model:</strong> {aiRecommendations.model_used}</p>
                                                <p><strong>Analysis Type:</strong> {aiRecommendations.analysis_type}</p>
                                                <p><strong>Generated:</strong> {new Date(aiRecommendations.generated_at).toLocaleString()}</p>
                                                {aiRecommendations.ai_confidence && (
                                                    <p><strong>Confidence:</strong> {aiRecommendations.ai_confidence}</p>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}

                                {aiInsights && !aiLoading && !aiRecommendations && (
                                    <div className="ai-clinical-insights">
                                        <h4>🔍 Clinical Insights</h4>
                                        {aiInsights.error ? (
                                            <div className="ai-error">
                                                <p>❌ {aiInsights.message}</p>
                                            </div>
                                        ) : (
                                            <div className="insights-grid">
                                                {aiInsights.insights?.map((insight, index) => (
                                                    <div key={index} className={`insight-card ${insight.level}`}>
                                                        <div className="insight-header">
                                                            <span className="insight-category">{insight.category}</span>
                                                            <span className={`insight-level ${insight.level}`}>{insight.level}</span>
                                                        </div>
                                                        <p className="insight-message">{insight.message}</p>
                                                        {insight.recommendation && (
                                                            <p className="insight-recommendation">{insight.recommendation}</p>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                )}

                                <div className="ai-disclaimer">
                                    <p><strong>⚠️ Medical Disclaimer:</strong> AI recommendations are for informational purposes only and should not replace professional medical advice. Always consult with healthcare providers for treatment decisions.</p>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Raw Results */}
                {queryResults.length > 0 && (
                    <div className={styles['raw-results']}>
                        <h3>Raw Query Results</h3>
                        <div className={styles['results-table-container']}>
                            <table className={styles['results-table']}>
                                <thead>
                                    <tr>
                                        {Object.keys(queryResults[0] || {}).map(key => (
                                            <th key={key}>{key}</th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody>
                                    {queryResults.slice(0, 100).map((row, index) => (
                                        <tr key={index}>
                                            {Object.values(row).map((value, cellIndex) => (
                                                <td key={cellIndex}>
                                                    {typeof value === 'object' ?
                                                        JSON.stringify(value, null, 2) :
                                                        String(value)
                                                    }
                                                </td>
                                            ))}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                            {queryResults.length > 100 && (
                                <div className={styles['results-truncated']}>
                                    Showing first 100 of {queryResults.length} results
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {queryResults.length === 0 && queryStats && (
                    <div className={styles['no-results']}>
                        No results returned from the query.
                    </div>
                )}
            </div>
        </div>
    );
}

export default QueryRunner;
