import { useEffect, useState } from 'react';
import AIRecommendationService from '../services/aiService';
import './PromptComparisonTesting.css';

const PromptComparisonTesting = ({ patientData, onResultsUpdate }) => {
    const [isRunning, setIsRunning] = useState(false);
    const [comparisonResults, setComparisonResults] = useState(null);
    const [selectedTechniques, setSelectedTechniques] = useState([
        'structured', 'conversational', 'motivational', 'clinical',
        'zero_shot', 'one_shot', 'few_shot', 'chain_of_thought',
        'tree_of_thought', 'self_consistency', 'react', 'role_playing'
    ]);
    const [testHistory, setTestHistory] = useState([]);
    const [currentTest, setCurrentTest] = useState(null);
    const [analysisMode, setAnalysisMode] = useState('comparison'); // 'comparison' or 'single'
    const [selectedCategory, setSelectedCategory] = useState('all'); // Filter by technique category

    const aiService = new AIRecommendationService();

    const promptTechniques = [
        // Traditional techniques
        {
            id: 'structured',
            name: 'Structured Clinical',
            description: 'Organized, systematic approach with clear categories and action items',
            color: '#007bff',
            icon: '📋',
            category: 'traditional',
            complexity: 3
        },
        {
            id: 'conversational',
            name: 'Conversational',
            description: 'Friendly, supportive tone like talking to a health coach',
            color: '#28a745',
            icon: '💬',
            category: 'traditional',
            complexity: 2
        },
        {
            id: 'motivational',
            name: 'Motivational',
            description: 'Inspiring, empowering language focused on transformation',
            color: '#ffc107',
            icon: '🚀',
            category: 'traditional',
            complexity: 2
        },
        {
            id: 'clinical',
            name: 'Evidence-Based Clinical',
            description: 'Medical terminology, clinical guidelines, and evidence levels',
            color: '#6c757d',
            icon: '🏥',
            category: 'traditional',
            complexity: 4
        },
        // Few-shot learning techniques
        {
            id: 'zero_shot',
            name: 'Zero-Shot',
            description: 'Direct task execution without examples or prior context',
            color: '#e83e8c',
            icon: '🎯',
            category: 'few_shot',
            complexity: 1
        },
        {
            id: 'one_shot',
            name: 'One-Shot',
            description: 'Learning from a single example before task execution',
            color: '#fd7e14',
            icon: '1️⃣',
            category: 'few_shot',
            complexity: 2
        },
        {
            id: 'few_shot',
            name: 'Few-Shot',
            description: 'Learning from multiple examples to establish patterns',
            color: '#20c997',
            icon: '📚',
            category: 'few_shot',
            complexity: 3
        },
        // Advanced reasoning techniques
        {
            id: 'chain_of_thought',
            name: 'Chain-of-Thought',
            description: 'Step-by-step reasoning process with explicit thinking',
            color: '#6610f2',
            icon: '🔗',
            category: 'reasoning',
            complexity: 4
        },
        {
            id: 'tree_of_thought',
            name: 'Tree-of-Thought',
            description: 'Multiple reasoning paths explored simultaneously',
            color: '#6f42c1',
            icon: '🌳',
            category: 'reasoning',
            complexity: 5
        },
        {
            id: 'self_consistency',
            name: 'Self-Consistency',
            description: 'Multiple perspectives synthesized for consistent output',
            color: '#495057',
            icon: '🔄',
            category: 'reasoning',
            complexity: 5
        },
        // Interactive techniques
        {
            id: 'react',
            name: 'ReAct (Reasoning + Acting)',
            description: 'Iterative reasoning and action-taking approach',
            color: '#17a2b8',
            icon: '⚡',
            category: 'interactive',
            complexity: 4
        },
        {
            id: 'role_playing',
            name: 'Role-Playing',
            description: 'Immersive persona-driven expert consultation',
            color: '#dc3545',
            icon: '🎭',
            category: 'interactive',
            complexity: 3
        }
    ];

    const techniqueCategories = [
        { value: 'all', label: 'All Techniques', count: promptTechniques.length },
        { value: 'traditional', label: 'Traditional Approaches', count: promptTechniques.filter(t => t.category === 'traditional').length },
        { value: 'few_shot', label: 'Few-Shot Learning', count: promptTechniques.filter(t => t.category === 'few_shot').length },
        { value: 'reasoning', label: 'Advanced Reasoning', count: promptTechniques.filter(t => t.category === 'reasoning').length },
        { value: 'interactive', label: 'Interactive Methods', count: promptTechniques.filter(t => t.category === 'interactive').length }
    ];

    useEffect(() => {
        loadTestHistory();
    }, []);

    const loadTestHistory = () => {
        const history = localStorage.getItem('promptComparisonHistory');
        if (history) {
            setTestHistory(JSON.parse(history));
        }
    };

    const saveTestToHistory = (results) => {
        const testRecord = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            patientProfile: results.patient_profile,
            bestTechnique: results.best_technique,
            techniqueResults: Object.keys(results.comparison_results).map(technique => ({
                technique,
                effectiveness: results.comparison_results[technique].effectiveness_score,
                readability: results.comparison_results[technique].readability_score,
                actionability: results.comparison_results[technique].actionability_score
            }))
        };

        const updatedHistory = [testRecord, ...testHistory.slice(0, 19)]; // Keep last 20 tests
        setTestHistory(updatedHistory);
        localStorage.setItem('promptComparisonHistory', JSON.stringify(updatedHistory));
    };

    const runComparison = async () => {
        if (!patientData) {
            alert('Please provide patient data to run the comparison.');
            return;
        }

        setIsRunning(true);
        setCurrentTest({
            startTime: new Date(),
            status: 'initializing',
            progress: 0
        });

        try {
            const results = await aiService.comparePromptingTechniques(patientData);
            setComparisonResults(results);
            saveTestToHistory(results);

            if (onResultsUpdate) {
                onResultsUpdate(results);
            }

            setCurrentTest({
                ...currentTest,
                status: 'completed',
                progress: 100
            });

        } catch (error) {
            console.error('Comparison failed:', error);
            setCurrentTest({
                ...currentTest,
                status: 'failed',
                error: error.message
            });
        } finally {
            setIsRunning(false);
        }
    };

    const runSingleTechnique = async (technique) => {
        if (!patientData) {
            alert('Please provide patient data to test the technique.');
            return;
        }

        setIsRunning(true);
        try {
            const results = await aiService.generatePatientHealthRecommendations(patientData, {
                promptingTechnique: technique,
                useComparison: false
            });

            setComparisonResults({
                comparison_results: { [technique]: results },
                single_technique_test: true,
                technique: technique
            });

        } catch (error) {
            console.error('Single technique test failed:', error);
        } finally {
            setIsRunning(false);
        }
    };

    const runCategoryComparison = async () => {
        if (!patientData) {
            alert('Please provide patient data to run the comparison.');
            return;
        }

        const categoryTechniques = getFilteredTechniques().map(t => t.id);
        setIsRunning(true);

        try {
            // Run comparison with only techniques in selected category
            const results = await aiService.comparePromptingTechniques(patientData);

            // Filter results to only include selected category
            const filteredResults = {};
            categoryTechniques.forEach(technique => {
                if (results.comparison_results[technique]) {
                    filteredResults[technique] = results.comparison_results[technique];
                }
            });

            setComparisonResults({
                ...results,
                comparison_results: filteredResults,
                category_comparison: true,
                selected_category: selectedCategory
            });

        } catch (error) {
            console.error('Category comparison failed:', error);
        } finally {
            setIsRunning(false);
        }
    };

    const getFilteredTechniques = () => {
        if (selectedCategory === 'all') return promptTechniques;
        return promptTechniques.filter(t => t.category === selectedCategory);
    };

    const getTechniqueInfo = (techniqueId) => {
        return promptTechniques.find(t => t.id === techniqueId);
    };

    const getScoreColor = (score) => {
        if (score >= 80) return '#28a745';
        if (score >= 60) return '#ffc107';
        if (score >= 40) return '#fd7e14';
        return '#dc3545';
    };

    const getComplexityColor = (complexity) => {
        if (complexity <= 2) return '#28a745';
        if (complexity <= 3) return '#ffc107';
        if (complexity <= 4) return '#fd7e14';
        return '#dc3545';
    };

    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleString();
    };

    if (!patientData) {
        return (
            <div className="prompt-comparison-empty">
                <div className="empty-icon">🧪</div>
                <h3>Prompt Comparison Testing</h3>
                <p>Patient data is required to run prompting technique comparisons.</p>
                <p>Please select a patient or provide patient data to begin testing.</p>
            </div>
        );
    }

    return (
        <div className="prompt-comparison-testing">
            <div className="testing-header">
                <h2>🧪 AI Prompting Technique Comparison</h2>
                <p>Test and compare different prompting strategies for patient health recommendations</p>

                <div className="testing-controls">
                    <div className="analysis-mode-selector">
                        <label>Analysis Mode:</label>
                        <select value={analysisMode} onChange={(e) => setAnalysisMode(e.target.value)}>
                            <option value="comparison">Full Comparison</option>
                            <option value="single">Single Technique</option>
                            <option value="category">By Category</option>
                        </select>
                    </div>

                    <div className="category-selector">
                        <label>Technique Category:</label>
                        <select value={selectedCategory} onChange={(e) => setSelectedCategory(e.target.value)}>
                            {techniqueCategories.map(cat => (
                                <option key={cat.value} value={cat.value}>
                                    {cat.label} ({cat.count})
                                </option>
                            ))}
                        </select>
                    </div>

                    {analysisMode === 'comparison' ? (
                        <button
                            className="run-comparison-btn"
                            onClick={runComparison}
                            disabled={isRunning}
                        >
                            {isRunning ? '🔄 Running Analysis...' : '🚀 Run Full Analysis'}
                        </button>
                    ) : analysisMode === 'category' ? (
                        <button
                            className="run-category-btn"
                            onClick={runCategoryComparison}
                            disabled={isRunning}
                        >
                            {isRunning ? '🔄 Analyzing...' : `📊 Test ${techniqueCategories.find(c => c.value === selectedCategory)?.label}`}
                        </button>
                    ) : (
                        <div className="single-technique-buttons">
                            {getFilteredTechniques().map(technique => (
                                <button
                                    key={technique.id}
                                    className="single-technique-btn"
                                    onClick={() => runSingleTechnique(technique.id)}
                                    disabled={isRunning}
                                    style={{ borderColor: technique.color }}
                                >
                                    {technique.icon} {technique.name}
                                    <span className="complexity-badge">C{technique.complexity}</span>
                                </button>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {currentTest && currentTest.status !== 'completed' && (
                <div className="test-progress">
                    <div className="progress-header">
                        <span>Test Status: {currentTest.status}</span>
                        <span>{formatTimestamp(currentTest.startTime)}</span>
                    </div>
                    {currentTest.status === 'failed' && (
                        <div className="error-message">
                            ❌ Test Failed: {currentTest.error}
                        </div>
                    )}
                </div>
            )}

            {comparisonResults && (
                <div className="comparison-results">
                    <div className="results-header">
                        <h3>📊 Comparison Results</h3>
                        {comparisonResults.best_technique && (
                            <div className="best-technique-badge">
                                🏆 Best Technique: {comparisonResults.best_technique.technique}
                                <span className="score">
                                    ({Math.round(comparisonResults.best_technique.composite_score)}%)
                                </span>
                            </div>
                        )}
                    </div>

                    <div className="techniques-grid">
                        {Object.entries(comparisonResults.comparison_results).map(([technique, results]) => {
                            const techniqueInfo = getTechniqueInfo(technique);
                            const isBest = comparisonResults.best_technique?.technique === technique;

                            return (
                                <div key={technique} className={`technique-result-card ${isBest ? 'best' : ''}`}>
                                    <div className="technique-header">
                                        <div className="technique-info">
                                            <span className="technique-icon">{techniqueInfo?.icon}</span>
                                            <div>
                                                <h4>{techniqueInfo?.name || technique}</h4>
                                                <p>{techniqueInfo?.description}</p>
                                            </div>
                                        </div>
                                        {isBest && <div className="best-badge">🏆 Best</div>}
                                    </div>

                                    {results.error ? (
                                        <div className="technique-error">
                                            ❌ Error: {results.error}
                                        </div>
                                    ) : (
                                        <div className="technique-scores">
                                            <div className="technique-meta">
                                                <span className="category-badge" style={{ backgroundColor: techniqueInfo?.color + '20', color: techniqueInfo?.color }}>
                                                    {techniqueInfo?.category}
                                                </span>
                                                <span className="complexity-indicator" style={{ color: getComplexityColor(techniqueInfo?.complexity) }}>
                                                    Complexity: {techniqueInfo?.complexity}/5
                                                </span>
                                            </div>

                                            <div className="score-item">
                                                <span className="score-label">Effectiveness</span>
                                                <div className="score-bar">
                                                    <div
                                                        className="score-fill"
                                                        style={{
                                                            width: `${results.effectiveness_score}%`,
                                                            backgroundColor: getScoreColor(results.effectiveness_score)
                                                        }}
                                                    ></div>
                                                    <span className="score-value">{Math.round(results.effectiveness_score)}%</span>
                                                </div>
                                            </div>

                                            <div className="score-item">
                                                <span className="score-label">Readability</span>
                                                <div className="score-bar">
                                                    <div
                                                        className="score-fill"
                                                        style={{
                                                            width: `${results.readability_score}%`,
                                                            backgroundColor: getScoreColor(results.readability_score)
                                                        }}
                                                    ></div>
                                                    <span className="score-value">{Math.round(results.readability_score)}%</span>
                                                </div>
                                            </div>

                                            <div className="score-item">
                                                <span className="score-label">Actionability</span>
                                                <div className="score-bar">
                                                    <div
                                                        className="score-fill"
                                                        style={{
                                                            width: `${results.actionability_score}%`,
                                                            backgroundColor: getScoreColor(results.actionability_score)
                                                        }}
                                                    ></div>
                                                    <span className="score-value">{Math.round(results.actionability_score)}%</span>
                                                </div>
                                            </div>

                                            {results.reasoning_depth && (
                                                <div className="score-item">
                                                    <span className="score-label">Reasoning Depth</span>
                                                    <div className="score-bar">
                                                        <div
                                                            className="score-fill"
                                                            style={{
                                                                width: `${results.reasoning_depth}%`,
                                                                backgroundColor: getScoreColor(results.reasoning_depth)
                                                            }}
                                                        ></div>
                                                        <span className="score-value">{Math.round(results.reasoning_depth)}%</span>
                                                    </div>
                                                </div>
                                            )}

                                            <div className="recommendation-count">
                                                📝 {results.recommendations?.length || 0} recommendations generated
                                                {results.technical_complexity && (
                                                    <span className="tech-complexity">
                                                        • Tech Complexity: {results.technical_complexity}/5
                                                    </span>
                                                )}
                                            </div>

                                            {results.recommendations && results.recommendations.length > 0 && (
                                                <div className="sample-recommendations">
                                                    <h5>Sample Recommendations:</h5>
                                                    {results.recommendations.slice(0, 2).map((rec, idx) => (
                                                        <div key={idx} className="sample-recommendation">
                                                            <strong>{rec.title}</strong>
                                                            <p>{rec.description.slice(0, 100)}...</p>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}

            {testHistory.length > 0 && (
                <div className="test-history">
                    <h3>📈 Test History</h3>
                    <div className="history-list">
                        {testHistory.slice(0, 5).map(test => (
                            <div key={test.id} className="history-item">
                                <div className="history-header">
                                    <span className="history-date">{formatTimestamp(test.timestamp)}</span>
                                    <span className="history-best">
                                        🏆 {test.bestTechnique?.technique}
                                        ({Math.round(test.bestTechnique?.composite_score)}%)
                                    </span>
                                </div>
                                <div className="history-techniques">
                                    {test.techniqueResults.map(result => (
                                        <span
                                            key={result.technique}
                                            className="history-technique-score"
                                            title={`${result.technique}: ${Math.round(result.effectiveness)}% effectiveness`}
                                        >
                                            {getTechniqueInfo(result.technique)?.icon} {Math.round(result.effectiveness)}%
                                        </span>
                                    ))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default PromptComparisonTesting;
