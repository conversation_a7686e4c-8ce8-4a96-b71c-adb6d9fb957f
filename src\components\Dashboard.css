/* Dashboard Main Container */
.dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #282828 0%, #3c3836 100%);
  color: #ebdbb2;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: rgba(235, 219, 178, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(235, 219, 178, 0.2);
}

.user-info h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.user-info p {
  margin: 0;
  opacity: 0.8;
  font-size: 0.9rem;
}

.logout-button {
  padding: 0.75rem 1.5rem;
  background: rgba(235, 219, 178, 0.2);
  color: #ebdbb2;
  border: 1px solid rgba(235, 219, 178, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.logout-button:hover {
  background: rgba(235, 219, 178, 0.3);
  transform: translateY(-2px);
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 0;
}

/* Dashboard Navigation */
.dashboard-nav {
  flex-shrink: 0;
  width: 100%;
  background: rgba(235, 219, 178, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(235, 219, 178, 0.2);
  padding: 1rem 2rem;
}

.dashboard-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: row;
  gap: 1rem;
  justify-content: flex-start;
  align-items: center;
}

.dashboard-nav li {
  padding: 0;
}

.dashboard-nav button {
  padding: 0.75rem 1.5rem;
  background: transparent;
  color: #ebdbb2;
  border: none;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.dashboard-nav button:hover {
  background: rgba(235, 219, 178, 0.1);
  transform: translateY(-2px);
}

.dashboard-nav button.active {
  background: rgba(235, 219, 178, 0.2);
  border-bottom: 3px solid #d79921;
  font-weight: 600;
}

/* Dashboard Main Content */
.dashboard-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.dashboard-main.no-scroll {
  overflow: hidden;
}

/* Research Section */
.research-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.research-section h3 {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  font-weight: 600;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Tools Grid */
.tools-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: flex-start;
}

.tool-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  min-width: 280px;
  max-width: 320px;
  padding: 2rem;
  background: rgba(235, 219, 178, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  border: 1px solid rgba(235, 219, 178, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tool-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.2);
  background: rgba(235, 219, 178, 0.2);
}

.tool-card h4 {
  margin: 0 0 1rem 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #d79921;
}

.tool-card p {
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  opacity: 0.9;
  flex-grow: 1;
}

.tool-button {
  align-self: flex-start;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(45deg, #d79921, #fabd2f);
  color: #282828;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.tool-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(215, 153, 33, 0.4);
  background: linear-gradient(45deg, #fabd2f, #d79921);
}

/* Settings Section */
.settings-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 600px;
}

.settings-section h3 {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  font-weight: 600;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  background: rgba(235, 219, 178, 0.15);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  border: 1px solid rgba(235, 219, 178, 0.2);
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-group label {
  font-weight: 600;
  font-size: 1.1rem;
  color: #d79921;
}

.setting-group select {
  padding: 0.75rem 1rem;
  border: 2px solid rgba(235, 219, 178, 0.3);
  border-radius: 8px;
  background: rgba(235, 219, 178, 0.1);
  color: #ebdbb2;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.setting-group select:focus {
  outline: none;
  border-color: #d79921;
  box-shadow: 0 0 0 3px rgba(215, 153, 33, 0.2);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 400;
  font-size: 1rem;
  color: #ebdbb2;
  cursor: pointer;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #d79921;
}

.save-button {
  align-self: flex-start;
  padding: 1rem 2rem;
  background: linear-gradient(45deg, #d79921, #fabd2f);
  color: #282828;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(215, 153, 33, 0.4);
  background: linear-gradient(45deg, #fabd2f, #d79921);
}

/* Loading State */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem;
  font-size: 1.2rem;
  color: #d79921;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-nav {
    padding: 1rem;
  }

  .dashboard-nav ul {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  .dashboard-nav li {
    flex: 1;
    min-width: 120px;
  }

  .dashboard-nav button {
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .dashboard-main {
    padding: 1rem;
  }

  .tools-grid {
    justify-content: center;
  }

  .tool-card {
    min-width: 100%;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dashboard-nav {
    padding: 0.75rem;
  }

  .dashboard-nav ul {
    flex-direction: column;
    gap: 0.25rem;
  }

  .dashboard-nav li {
    min-width: auto;
    flex: none;
  }

  .dashboard-nav button {
    width: 100%;
  }

  .tools-grid {
    gap: 1rem;
  }

  .tool-card {
    padding: 1.5rem;
  }
}
