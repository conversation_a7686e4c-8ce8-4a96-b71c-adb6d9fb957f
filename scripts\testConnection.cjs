const neo4j = require('neo4j-driver');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    }
  }
}

loadEnvFile();

const uri = process.env.VITE_NEO4J_URI;
const username = process.env.VITE_NEO4J_USERNAME;
const password = process.env.VITE_NEO4J_PASSWORD;

console.log('Neo4j Connection Test');
console.log('====================');
console.log('URI:', uri);
console.log('Username:', username);

const driver = neo4j.driver(uri, neo4j.auth.basic(username, password));

async function test() {
  const session = driver.session();
  try {
    console.log('\nTesting connection...');
    const result = await session.run('RETURN "Hello Neo4j!" as message, datetime() as now');
    const record = result.records[0];
    console.log('✅ Connection successful!');
    console.log('Message:', record.get('message'));
    console.log('Server time:', record.get('now').toString());
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    process.exit(1);
  } finally {
    await session.close();
    await driver.close();
  }
}

test();
