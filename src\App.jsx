import { useEffect, useState } from "react"
import "./App.css"
import Dashboard from "./components/Dashboard"
import Login from "./components/Login"

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [user, setUser] = useState(null)

  const handleLogin = (userData) => {
    setUser(userData)
    setIsLoggedIn(true)
    try {
      localStorage.setItem("rp_user", JSON.stringify(userData))
      localStorage.setItem("rp_logged_in", "true")
    } catch (error) {
      console.error("Failed to save user data to localStorage:", error)
    }
  }

  const handleLogout = () => {
    setUser(null)
    setIsLoggedIn(false)
    try {
      localStorage.removeItem("rp_user")
      localStorage.removeItem("rp_logged_in")
    } catch (error) {
      console.error("Failed to clear user data from localStorage:", error)
    }
  }

  // Restore session on mount
  useEffect(() => {
    try {
      const logged = localStorage.getItem("rp_logged_in") === "true"
      const storedUser = localStorage.getItem("rp_user")
      if (logged && storedUser) {
        setUser(JSON.parse(storedUser))
        setIsLoggedIn(true)
      }
    } catch (error) {
      console.error("Failed to restore user session:", error)
    }
  }, [])

  return (
    <div className="app">
      <header className="app-header">
        <h1>NLG Research Platform</h1>
        <p>Healthcare Research Application</p>
      </header>

      <main className="app-main">
        {!isLoggedIn ? (
          <Login onLogin={handleLogin} />
        ) : (
          <Dashboard user={user} onLogout={handleLogout} />
        )}
      </main>
    </div>
  )
}

export default App
