import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLazyLoading } from '../utils/performanceHooks';

// Virtual scrolling component for large datasets
export const VirtualScrollList = ({
    items,
    itemHeight = 50,
    containerHeight = 400,
    renderItem,
    buffer = 5
}) => {
    const [scrollTop, setScrollTop] = useState(0);

    const visibleRange = useMemo(() => {
        const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer);
        const endIndex = Math.min(
            items.length,
            Math.ceil((scrollTop + containerHeight) / itemHeight) + buffer
        );
        return { startIndex, endIndex };
    }, [scrollTop, itemHeight, containerHeight, items.length, buffer]);

    const visibleItems = useMemo(() => {
        return items.slice(visibleRange.startIndex, visibleRange.endIndex);
    }, [items, visibleRange]);

    const totalHeight = items.length * itemHeight;
    const offsetY = visibleRange.startIndex * itemHeight;

    const handleScroll = useCallback((e) => {
        setScrollTop(e.target.scrollTop);
    }, []);

    return (
        <div
            className="virtual-scroll-container"
            style={{ height: containerHeight }}
            onScroll={handleScroll}
        >
            <div style={{ height: totalHeight, position: 'relative' }}>
                <div style={{ transform: `translateY(${offsetY}px)` }}>
                    {visibleItems.map((item, index) => (
                        <div key={visibleRange.startIndex + index} style={{ height: itemHeight }}>
                            {renderItem(item, visibleRange.startIndex + index)}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

// Loading skeleton component
export const Skeleton = ({
    type = 'text',
    width = '100%',
    height = '1rem',
    className = ''
}) => {
    const getSkeletonClass = () => {
        switch (type) {
            case 'title': return 'skeleton skeleton-title';
            case 'button': return 'skeleton skeleton-button';
            case 'text':
            default: return 'skeleton skeleton-text';
        }
    };

    return (
        <div
            className={`${getSkeletonClass()} ${className}`}
            style={{ width, height }}
            aria-label="Loading content..."
        />
    );
};

// Lazy image component
export const LazyImage = ({ src, alt, className = '', ...props }) => {
    const [imageSrc, setImageSrc] = useState('');
    const [imageRef, isVisible] = useLazyLoading();

    useEffect(() => {
        if (isVisible && src) {
            const img = new Image();
            img.onload = () => setImageSrc(src);
            img.src = src;
        }
    }, [isVisible, src]);

    return (
        <img
            ref={imageRef}
            src={imageSrc}
            alt={alt}
            className={`lazy-image ${imageSrc ? 'loaded' : ''} ${className}`}
            {...props}
        />
    );
};

// Performance monitoring component
export const PerformanceMonitor = ({ children }) => {
    const [metrics, setMetrics] = useState({
        loadTime: 0,
        renderTime: 0,
        memoryUsage: 0
    });

    useEffect(() => {
        const startTime = performance.now();

        // Measure render time
        const measureRenderTime = () => {
            const endTime = performance.now();
            setMetrics(prev => ({
                ...prev,
                renderTime: endTime - startTime
            }));
        };

        // Measure memory usage (if available)
        if (performance.memory) {
            setMetrics(prev => ({
                ...prev,
                memoryUsage: performance.memory.usedJSHeapSize / (1024 * 1024) // MB
            }));
        }

        // Measure page load time
        if (performance.navigation) {
            setMetrics(prev => ({
                ...prev,
                loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart
            }));
        }

        requestAnimationFrame(measureRenderTime);
    }, []);

    // Development mode only - show performance metrics
    const isDevelopment = import.meta.env.MODE === 'development';
    if (isDevelopment) {
        return (
            <>
                {children}
                <div className="performance-monitor" style={{
                    position: 'fixed',
                    bottom: 10,
                    right: 10,
                    background: 'rgba(0,0,0,0.8)',
                    color: 'white',
                    padding: '0.5rem',
                    borderRadius: '4px',
                    fontSize: '0.75rem',
                    zIndex: 9999
                }}>
                    <div>Render: {metrics.renderTime.toFixed(2)}ms</div>
                    <div>Load: {metrics.loadTime}ms</div>
                    {metrics.memoryUsage > 0 && (
                        <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>
                    )}
                </div>
            </>
        );
    }

    return children;
};

// Performance monitoring component - React component only

// Error boundary for better error handling
export class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Error caught by boundary:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="error-boundary" role="alert">
                    <div className="info-card">
                        <div className="info-card-header">
                            <span>⚠️ Something went wrong</span>
                        </div>
                        <div className="info-card-body">
                            <p className="text-small">We encountered an unexpected error. Please try refreshing the page.</p>
                            <button
                                onClick={() => this.setState({ hasError: false, error: null })}
                                className="text-small"
                            >
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

// Accessible focus trap for modals
export const FocusTrap = ({ children, active = true }) => {
    const trapRef = useRef(null);

    useEffect(() => {
        if (!active || !trapRef.current) return;

        const focusableElements = trapRef.current.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        const firstFocusable = focusableElements[0];
        const lastFocusable = focusableElements[focusableElements.length - 1];

        const handleKeyDown = (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusable) {
                        e.preventDefault();
                        lastFocusable.focus();
                    }
                } else {
                    if (document.activeElement === lastFocusable) {
                        e.preventDefault();
                        firstFocusable.focus();
                    }
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        firstFocusable?.focus();

        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [active]);

    return <div ref={trapRef}>{children}</div>;
};
