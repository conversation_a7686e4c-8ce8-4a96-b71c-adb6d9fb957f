import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    open: true,
    port: 5173,
    proxy: {
      // Forward API calls to local Cloudflare Pages Functions (Wrangler)
      '/api': {
        target: 'http://127.0.0.1:8787',
        changeOrigin: true,
        secure: false,
        // Ensure websockets and any SSE would work if added later
        ws: true
      }
    }
  }
})
