/**
 * Generate Glucose Readings for D1NAMO Patients
 * Creates realistic glucose data that correlates with ECG timestamps
 */

import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

// Neo4j connection
const driver = neo4j.driver(
  process.env.VITE_NEO4J_URI,
  neo4j.auth.basic(process.env.VITE_NEO4J_USERNAME, process.env.VITE_NEO4J_PASSWORD)
);

/**
 * Generate realistic glucose values based on patient profile and time
 */
function generateGlucoseValue(patientType, hour, baseVariability = 1.0) {
  let baseGlucose = 110; // Starting point

  // Adjust base glucose by diabetes type
  if (patientType.includes('Type 2')) {
    baseGlucose = 140; // Higher baseline for Type 2
  } else if (patientType.includes('Type 1')) {
    baseGlucose = 120; // Moderate baseline for Type 1
  }

  // Time-of-day patterns
  if (hour >= 6 && hour <= 9) {
    baseGlucose += 30; // Dawn phenomenon
  } else if (hour >= 11 && hour <= 14) {
    baseGlucose += 45; // Post-lunch spike
  } else if (hour >= 17 && hour <= 20) {
    baseGlucose += 35; // Post-dinner spike
  } else if (hour >= 22 || hour <= 5) {
    baseGlucose -= 20; // Overnight lower levels
  }

  // Add individual variability
  const randomVariation = (Math.random() - 0.5) * 60 * baseVariability;
  const glucoseValue = baseGlucose + randomVariation;

  // Keep within realistic bounds
  return Math.max(60, Math.min(300, Math.round(glucoseValue)));
}

/**
 * Generate glucose readings around ECG timestamps
 */
async function generateGlucoseData() {
  console.log('🍯 Starting glucose data generation...');

  const session = driver.session();
  let totalGenerated = 0;

  try {
    // Get all ECG readings with patient info
    const result = await session.run(`
      MATCH (p:Patient:D1NAMOSubject)-[:HAD_ECG]->(e:ECGReading)
      RETURN p.patientId as patientId,
             p.condition as condition,
             e.timestamp as ecgTimestamp,
             p
      ORDER BY p.patientId, e.timestamp
    `);

    console.log(`Found ${result.records.length} ECG readings to correlate with glucose`);

    const groupedByPatient = {};

    // Group ECG readings by patient
    result.records.forEach(record => {
      const patientId = record.get('patientId');
      const condition = record.get('condition');
      const ecgTimestamp = new Date(record.get('ecgTimestamp'));

      if (!groupedByPatient[patientId]) {
        groupedByPatient[patientId] = {
          condition,
          ecgTimestamps: []
        };
      }

      groupedByPatient[patientId].ecgTimestamps.push(ecgTimestamp);
    });

    // Generate glucose data for each patient
    for (const [patientId, data] of Object.entries(groupedByPatient)) {
      console.log(`\n👤 Generating glucose data for Patient ${patientId} (${data.condition})`);

      let patientGlucoseCount = 0;
      const usedTimestamps = new Set();

      // Create 3-5 glucose readings per day from ECG data
      const dailyReadings = Math.floor(Math.random() * 3) + 3; // 3-5 readings

      // Sample ECG timestamps for glucose correlation
      const sampledECGs = data.ecgTimestamps
        .filter((_, index) => index % Math.ceil(data.ecgTimestamps.length / (dailyReadings * 3)) === 0)
        .slice(0, dailyReadings * 3);

      for (const ecgTimestamp of sampledECGs) {
        // Create glucose reading within ±30 minutes of ECG
        const offsetMinutes = (Math.random() - 0.5) * 60; // ±30 minutes
        const glucoseTimestamp = new Date(ecgTimestamp.getTime() + offsetMinutes * 60 * 1000);

        // Avoid duplicate timestamps
        const timestampKey = glucoseTimestamp.toISOString();
        if (usedTimestamps.has(timestampKey)) continue;
        usedTimestamps.add(timestampKey);

        const hour = glucoseTimestamp.getHours();
        const glucoseValue = generateGlucoseValue(data.condition, hour);

        // Determine reading type based on time
        let readingType = 'CGM';
        if (hour >= 6 && hour <= 9) readingType = 'Fasting';
        else if (hour >= 11 && hour <= 14) readingType = 'Post-Lunch';
        else if (hour >= 17 && hour <= 20) readingType = 'Post-Dinner';
        else if (hour >= 21 || hour <= 5) readingType = 'Bedtime';

        try {
          await session.run(`
            MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
            CREATE (g:GlucoseReading:D1NAMOReading {
              readingId: $readingId,
              timestamp: datetime($timestamp),
              value: $value,
              unit: 'mg/dL',
              readingType: $readingType,
              trend: $trend,
              source: 'Generated'
            })
            CREATE (p)-[:HAD_READING]->(g)
          `, {
            patientId,
            readingId: `GLU_${patientId}_${glucoseTimestamp.getTime()}`,
            timestamp: glucoseTimestamp.toISOString(),
            value: glucoseValue,
            readingType,
            trend: glucoseValue > 140 ? 'rising' : glucoseValue < 80 ? 'falling' : 'stable'
          });

          patientGlucoseCount++;
          totalGenerated++;

        } catch (error) {
          console.warn(`Warning: Could not create glucose reading: ${error.message}`);
        }
      }

      console.log(`   ✅ Generated ${patientGlucoseCount} glucose readings for patient ${patientId}`);
    }

    console.log(`\n🎉 Glucose generation complete! Total readings: ${totalGenerated}`);
    return totalGenerated;

  } finally {
    await session.close();
  }
}

/**
 * Verify data import
 */
async function verifyDataIntegrity() {
  console.log('\n🔍 Verifying data integrity...');

  const session = driver.session();

  try {
    // Check patients with both ECG and glucose data
    const result = await session.run(`
      MATCH (p:Patient:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(e:ECGReading)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
      RETURN p.patientId as patientId,
             p.name as name,
             p.condition as condition,
             count(DISTINCT e) as ecgCount,
             count(DISTINCT g) as glucoseCount,
             count(DISTINCT f) as featuresCount
      ORDER BY p.patientId
    `);

    console.log('\n📊 Data Summary:');
    console.log('┌─────────┬──────────────────────┬─────────────────┬─────────┬─────────┬──────────┐');
    console.log('│ Patient │ Name                 │ Condition       │ ECG     │ Glucose │ Features │');
    console.log('├─────────┼──────────────────────┼─────────────────┼─────────┼─────────┼──────────┤');

    let totalECG = 0;
    let totalGlucose = 0;
    let totalFeatures = 0;

    result.records.forEach(record => {
      const patientId = record.get('patientId');
      const name = record.get('name').substring(0, 20);
      const condition = record.get('condition').substring(0, 15);
      const ecgCount = record.get('ecgCount').toNumber();
      const glucoseCount = record.get('glucoseCount').toNumber();
      const featuresCount = record.get('featuresCount').toNumber();

      totalECG += ecgCount;
      totalGlucose += glucoseCount;
      totalFeatures += featuresCount;

      console.log(`│ ${patientId.padEnd(7)} │ ${name.padEnd(20)} │ ${condition.padEnd(15)} │ ${ecgCount.toString().padStart(7)} │ ${glucoseCount.toString().padStart(7)} │ ${featuresCount.toString().padStart(8)} │`);
    });

    console.log('├─────────┼──────────────────────┼─────────────────┼─────────┼─────────┼──────────┤');
    console.log(`│ TOTAL   │                      │                 │ ${totalECG.toString().padStart(7)} │ ${totalGlucose.toString().padStart(7)} │ ${totalFeatures.toString().padStart(8)} │`);
    console.log('└─────────┴──────────────────────┴─────────────────┴─────────┴─────────┴──────────┘');

    // Check date ranges
    const dateResult = await session.run(`
      MATCH (e:ECGReading:D1NAMOReading)
      OPTIONAL MATCH (g:GlucoseReading:D1NAMOReading)
      RETURN min(e.timestamp) as earliestECG,
             max(e.timestamp) as latestECG,
             min(g.timestamp) as earliestGlucose,
             max(g.timestamp) as latestGlucose
    `);

    if (dateResult.records.length > 0) {
      const record = dateResult.records[0];
      console.log('\n📅 Date Ranges:');
      console.log(`   ECG Data: ${new Date(record.get('earliestECG')).toLocaleDateString()} to ${new Date(record.get('latestECG')).toLocaleDateString()}`);
      console.log(`   Glucose Data: ${new Date(record.get('earliestGlucose')).toLocaleDateString()} to ${new Date(record.get('latestGlucose')).toLocaleDateString()}`);
    }

    return {
      totalECG,
      totalGlucose,
      totalFeatures,
      patientCount: result.records.length
    };

  } finally {
    await session.close();
  }
}

// Main execution
async function main() {
  try {
    console.log('🚀 D1NAMO Glucose Data Generation Pipeline');

    // Generate glucose data
    const glucoseCount = await generateGlucoseData();

    // Verify data integrity
    const summary = await verifyDataIntegrity();

    console.log('\n✅ Data import and generation completed successfully!');
    console.log(`📊 Final Summary:`);
    console.log(`   - Patients: ${summary.patientCount}`);
    console.log(`   - ECG Readings: ${summary.totalECG}`);
    console.log(`   - Glucose Readings: ${summary.totalGlucose}`);
    console.log(`   - ECG Features: ${summary.totalFeatures}`);

    return {
      success: true,
      glucoseReadings: glucoseCount,
      ...summary
    };

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1].replace(/\\/g, '/')}`) {
  main()
    .then((result) => {
      console.log('🎉 Process completed:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Process failed:', error);
      process.exit(1);
    })
    .finally(() => {
      driver.close();
    });
}

export { generateGlucoseData, verifyDataIntegrity };
