# Build, Push, and Deploy Script for HealthHub Research Platform
# Usage: .\deploy.ps1

Write-Host "🚀 Starting build, push, and deploy process..." -ForegroundColor Green

try {
    # Build the project
    Write-Host "🔧 Building project..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }

    # Check git status
    Write-Host "📋 Checking git status..." -ForegroundColor Yellow
    $gitStatus = git status --porcelain

    if ($gitStatus) {
        # Add all changes
        Write-Host "📦 Adding changes to git..." -ForegroundColor Yellow
        git add .

        # Commit with timestamp
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Write-Host "💾 Committing changes..." -ForegroundColor Yellow
        git commit -m "feat: automated build and deployment - $timestamp"

        if ($LASTEXITCODE -ne 0) {
            throw "Commit failed"
        }

        # Push to remote
        Write-Host "⬆️  Pushing to GitHub..." -ForegroundColor Yellow
        git push

        if ($LASTEXITCODE -ne 0) {
            throw "Push failed"
        }
    } else {
        Write-Host "ℹ️  No changes to commit" -ForegroundColor Cyan
    }

    # Deploy with Wrangler
    Write-Host "☁️  Deploying to Cloudflare Pages..." -ForegroundColor Yellow
    wrangler pages deploy dist --project-name=healthhub-research-platform

    if ($LASTEXITCODE -ne 0) {
        throw "Deployment failed"
    }

    # Test the deployment
    Write-Host "🧪 Testing deployment..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri "https://healthhub-research-platform.pages.dev/api/health" -Method GET

    Write-Host "✅ Deployment successful!" -ForegroundColor Green
    Write-Host "🌐 Production URL: https://healthhub-research-platform.pages.dev" -ForegroundColor Cyan
    Write-Host "📊 API Status: $($response.status)" -ForegroundColor Cyan
    Write-Host "🕐 Deployment time: $($response.timestamp)" -ForegroundColor Cyan

} catch {
    Write-Host "❌ Error during deployment: $_" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 Build, push, and deploy completed successfully!" -ForegroundColor Green
