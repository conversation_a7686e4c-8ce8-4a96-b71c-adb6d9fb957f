import { useRef, useState } from 'react';
import './DataExportImport.css';

const DataExportImport = ({ agpData, patientData, recommendations }) => {
    const [exportFormat, setExportFormat] = useState('csv');
    const [exportType, setExportType] = useState('agp'); // agp, patient, full, custom
    const [isExporting, setIsExporting] = useState(false);
    const [importStatus, setImportStatus] = useState(null);
    const [customFields, setCustomFields] = useState({
        includeRawData: true,
        includeStatistics: true,
        includeRecommendations: true,
        includeTrendAnalysis: true,
        includeMetadata: true,
        dateRange: 'all',
        anonymizeData: false
    });

    const fileInputRef = useRef(null);

    // Export Functions
    const handleExport = async () => {
        if (!agpData && !patientData) {
            alert('No data available for export');
            return;
        }

        setIsExporting(true);

        try {
            let exportData;
            let filename;

            switch (exportType) {
                case 'agp':
                    exportData = await generateAGPExportData();
                    filename = `agp-report-${patientData?.name || 'patient'}-${new Date().toISOString().split('T')[0]}`;
                    break;

                case 'patient':
                    exportData = await generatePatientExportData();
                    filename = `patient-data-${patientData?.name || 'patient'}-${new Date().toISOString().split('T')[0]}`;
                    break;

                case 'full':
                    exportData = await generateFullExportData();
                    filename = `full-export-${patientData?.name || 'patient'}-${new Date().toISOString().split('T')[0]}`;
                    break;

                case 'custom':
                    exportData = await generateCustomExportData();
                    filename = `custom-export-${patientData?.name || 'patient'}-${new Date().toISOString().split('T')[0]}`;
                    break;

                default:
                    throw new Error('Invalid export type');
            }

            // Export based on format
            switch (exportFormat) {
                case 'csv':
                    await exportAsCSV(exportData, filename);
                    break;

                case 'json':
                    await exportAsJSON(exportData, filename);
                    break;

                case 'pdf':
                    await exportAsPDF(exportData, filename);
                    break;

                case 'xlsx':
                    await exportAsExcel(exportData, filename);
                    break;

                default:
                    throw new Error('Invalid export format');
            }

        } catch (error) {
            console.error('Export failed:', error);
            alert('Export failed: ' + error.message);
        } finally {
            setIsExporting(false);
        }
    };

    const generateAGPExportData = async () => {
        if (!agpData) throw new Error('No AGP data available');

        const data = {
            exportType: 'agp',
            exportDate: new Date().toISOString(),
            patient: customFields.anonymizeData ? {
                id: 'ANONYMOUS',
                name: 'Anonymous Patient',
                age: 'N/A',
                condition: patientData?.condition || 'Unknown'
            } : {
                id: patientData?.patientId,
                name: patientData?.name,
                age: patientData?.age,
                condition: patientData?.condition,
                gender: patientData?.gender
            },

            agpSummary: {
                timeInRange: agpData.timeInRange,
                summaryStatistics: agpData.summaryStatistics,
                totalReadings: agpData.totalReadings,
                dateRange: agpData.dateRange
            }
        };

        if (customFields.includeRawData && agpData.rawData) {
            data.rawGlucoseData = customFields.anonymizeData
                ? agpData.rawData.map(reading => ({
                    timestamp: reading.timestamp,
                    glucose: reading.glucose,
                    readingType: reading.readingType
                }))
                : agpData.rawData;
        }

        if (customFields.includeRecommendations && recommendations) {
            data.aiRecommendations = recommendations;
        }

        return data;
    };

    const generatePatientExportData = async () => {
        if (!patientData) throw new Error('No patient data available');

        const data = {
            exportType: 'patient',
            exportDate: new Date().toISOString(),
            patient: customFields.anonymizeData ? {
                id: 'ANONYMOUS',
                name: 'Anonymous Patient',
                age: 'N/A',
                condition: patientData.condition
            } : patientData,

            contactInfo: customFields.anonymizeData ? null : {
                phoneNumber: patientData.phoneNumber,
                email: patientData.email,
                emergencyContact: patientData.emergencyContact
            },

            medicalInfo: {
                conditions: patientData.conditions,
                medications: patientData.medications,
                primaryCondition: patientData.primaryCondition
            },

            glucoseStatistics: patientData.totalReadings ? {
                totalReadings: patientData.totalReadings,
                avgGlucose: patientData.avgGlucose,
                minGlucose: patientData.minGlucose,
                maxGlucose: patientData.maxGlucose
            } : null
        };

        return data;
    };

    const generateFullExportData = async () => {
        const agpExport = await generateAGPExportData();
        const patientExport = await generatePatientExportData();

        return {
            exportType: 'full',
            exportDate: new Date().toISOString(),
            ...agpExport,
            ...patientExport,
            metadata: {
                applicationVersion: '1.0.0',
                exportFormat: exportFormat,
                customSettings: customFields
            }
        };
    };

    const generateCustomExportData = async () => {
        const data = {
            exportType: 'custom',
            exportDate: new Date().toISOString(),
            customFields: customFields
        };

        if (customFields.includeRawData && agpData?.rawData) {
            data.glucoseReadings = agpData.rawData;
        }

        if (customFields.includeStatistics && agpData) {
            data.agpStatistics = {
                timeInRange: agpData.timeInRange,
                summaryStatistics: agpData.summaryStatistics
            };
        }

        if (customFields.includeRecommendations && recommendations) {
            data.aiRecommendations = recommendations;
        }

        if (customFields.includeMetadata) {
            data.metadata = {
                totalReadings: agpData?.totalReadings,
                dateRange: agpData?.dateRange,
                patientInfo: customFields.anonymizeData ? null : patientData
            };
        }

        return data;
    };

    // Export format handlers
    const exportAsCSV = async (data, filename) => {
        let csvContent = '';

        if (data.exportType === 'agp' || data.exportType === 'full') {
            // AGP Summary CSV
            csvContent += 'AGP Summary\n';
            csvContent += 'Metric,Value\n';
            csvContent += `Time in Range,%${data.agpSummary?.timeInRange?.targetRangePercentage?.toFixed(1) || 'N/A'}\n`;
            csvContent += `Very Low (<54),%${data.agpSummary?.timeInRange?.ranges?.veryLow?.percentage?.toFixed(1) || '0'}\n`;
            csvContent += `Low (54-69),%${data.agpSummary?.timeInRange?.ranges?.low?.percentage?.toFixed(1) || '0'}\n`;
            csvContent += `Target (70-180),%${data.agpSummary?.timeInRange?.targetRangePercentage?.toFixed(1) || 'N/A'}\n`;
            csvContent += `High (181-250),%${data.agpSummary?.timeInRange?.ranges?.high?.percentage?.toFixed(1) || '0'}\n`;
            csvContent += `Very High (>250),%${data.agpSummary?.timeInRange?.ranges?.veryHigh?.percentage?.toFixed(1) || '0'}\n`;
            csvContent += `Average Glucose,${data.agpSummary?.summaryStatistics?.mean?.toFixed(1) || 'N/A'} mg/dL\n`;
            csvContent += `Standard Deviation,${data.agpSummary?.summaryStatistics?.standardDeviation?.toFixed(1) || 'N/A'}\n`;
            csvContent += `Coefficient of Variation,%${data.agpSummary?.summaryStatistics?.coefficientOfVariation?.toFixed(1) || 'N/A'}\n`;
            csvContent += `Total Readings,${data.agpSummary?.totalReadings || 'N/A'}\n\n`;

            // Raw data if included
            if (data.rawGlucoseData && customFields.includeRawData) {
                csvContent += 'Glucose Readings\n';
                csvContent += 'Timestamp,Glucose (mg/dL),Reading Type\n';
                data.rawGlucoseData.forEach(reading => {
                    csvContent += `${reading.timestamp},${reading.glucose},${reading.readingType}\n`;
                });
            }
        }

        const blob = new Blob([csvContent], { type: 'text/csv' });
        downloadFile(blob, `${filename}.csv`);
    };

    const exportAsJSON = async (data, filename) => {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        downloadFile(blob, `${filename}.json`);
    };

    const exportAsPDF = async (data, filename) => {
        // Create a simple PDF report using HTML and print
        const reportHTML = generatePDFHTML(data);

        const printWindow = window.open('', '_blank');
        printWindow.document.write(reportHTML);
        printWindow.document.close();
        printWindow.focus();

        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 1000);
    };

    const exportAsExcel = async (data, filename) => {
        // Simple Excel-compatible format (tab-separated values)
        let content = '';

        if (data.agpSummary) {
            content += 'AGP Summary\n';
            content += 'Metric\tValue\n';
            content += `Time in Range\t${data.agpSummary?.timeInRange?.targetRangePercentage?.toFixed(1) || 'N/A'}%\n`;
            content += `Average Glucose\t${data.agpSummary?.summaryStatistics?.mean?.toFixed(1) || 'N/A'} mg/dL\n`;
            content += `Standard Deviation\t${data.agpSummary?.summaryStatistics?.standardDeviation?.toFixed(1) || 'N/A'}\n`;
            content += `Total Readings\t${data.agpSummary?.totalReadings || 'N/A'}\n\n`;
        }

        if (data.rawGlucoseData && customFields.includeRawData) {
            content += 'Glucose Readings\n';
            content += 'Timestamp\tGlucose (mg/dL)\tReading Type\n';
            data.rawGlucoseData.forEach(reading => {
                content += `${reading.timestamp}\t${reading.glucose}\t${reading.readingType}\n`;
            });
        }

        const blob = new Blob([content], { type: 'application/vnd.ms-excel' });
        downloadFile(blob, `${filename}.xls`);
    };

    const generatePDFHTML = (data) => {
        return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>AGP Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1, h2 { color: #d79921; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f4f4f4; }
            .summary { background-color: #f9f9f9; padding: 20px; margin: 20px 0; }
            .patient-info { margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <h1>AGP Clinical Report</h1>

          <div class="patient-info">
            <h2>Patient Information</h2>
            <p><strong>Name:</strong> ${data.patient?.name || 'N/A'}</p>
            <p><strong>Age:</strong> ${data.patient?.age || 'N/A'}</p>
            <p><strong>Condition:</strong> ${data.patient?.condition || 'N/A'}</p>
            <p><strong>Report Date:</strong> ${new Date(data.exportDate).toLocaleDateString()}</p>
          </div>

          ${data.agpSummary ? `
          <div class="summary">
            <h2>AGP Summary</h2>
            <table>
              <tr><th>Metric</th><th>Value</th></tr>
              <tr><td>Time in Range (70-180 mg/dL)</td><td>${data.agpSummary?.timeInRange?.targetRangePercentage?.toFixed(1) || 'N/A'}%</td></tr>
              <tr><td>Average Glucose</td><td>${data.agpSummary?.summaryStatistics?.mean?.toFixed(1) || 'N/A'} mg/dL</td></tr>
              <tr><td>Glucose Variability (CV)</td><td>${data.agpSummary?.summaryStatistics?.coefficientOfVariation?.toFixed(1) || 'N/A'}%</td></tr>
              <tr><td>Total Readings</td><td>${data.agpSummary?.totalReadings || 'N/A'}</td></tr>
            </table>
          </div>
          ` : ''}

          ${data.aiRecommendations ? `
          <div>
            <h2>Clinical Recommendations</h2>
            ${data.aiRecommendations.recommendations?.map(rec => `
              <div style="margin: 10px 0; padding: 10px; border-left: 4px solid #d79921;">
                <strong>${rec.category}:</strong> ${rec.recommendation}
              </div>
            `).join('') || ''}
          </div>
          ` : ''}

          <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p><small>Generated by HealthHub Research Platform on ${new Date().toLocaleDateString()}</small></p>
          </footer>
        </body>
      </html>
    `;
    };

    const downloadFile = (blob, filename) => {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    };

    // Import Functions
    const handleImport = async (event) => {
        const file = event.target.files[0];
        if (!file) return;

        setImportStatus({ type: 'loading', message: 'Processing file...' });

        try {
            const fileContent = await readFile(file);
            const importedData = await processImportedFile(file, fileContent);

            setImportStatus({
                type: 'success',
                message: `Successfully imported ${importedData.recordCount} records`,
                data: importedData
            });

            // Here you would typically call a callback to update the parent component
            // onDataImported(importedData);

        } catch (error) {
            console.error('Import failed:', error);
            setImportStatus({
                type: 'error',
                message: 'Import failed: ' + error.message
            });
        }

        // Reset file input
        event.target.value = '';
    };

    const readFile = (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;

            if (file.type === 'application/json' || file.name.endsWith('.json')) {
                reader.readAsText(file);
            } else if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
                reader.readAsText(file);
            } else {
                reader.readAsText(file);
            }
        });
    };

    const processImportedFile = async (file, content) => {
        if (file.type === 'application/json' || file.name.endsWith('.json')) {
            return processJSONImport(content);
        } else if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
            return processCSVImport(content);
        } else {
            throw new Error('Unsupported file format');
        }
    };

    const processJSONImport = (content) => {
        try {
            const data = JSON.parse(content);

            // Validate import data structure
            if (!data.exportType) {
                throw new Error('Invalid file format: missing export type');
            }

            let recordCount = 0;
            const importedData = {
                type: data.exportType,
                patient: data.patient,
                importDate: new Date().toISOString()
            };

            if (data.rawGlucoseData) {
                importedData.glucoseReadings = data.rawGlucoseData;
                recordCount += data.rawGlucoseData.length;
            }

            if (data.agpSummary) {
                importedData.agpData = data.agpSummary;
                recordCount += 1;
            }

            if (data.aiRecommendations) {
                importedData.recommendations = data.aiRecommendations;
            }

            return { ...importedData, recordCount };

        } catch (error) {
            throw new Error('Invalid JSON format: ' + error.message);
        }
    };

    const processCSVImport = (content) => {
        const lines = content.split('\n').filter(line => line.trim());
        const glucoseReadings = [];

        let isReadingData = false;
        let headers = [];

        for (let line of lines) {
            if (line.includes('Glucose Readings')) {
                isReadingData = false;
                continue;
            }

            if (line.includes('Timestamp,Glucose')) {
                isReadingData = true;
                headers = line.split(',').map(h => h.trim());
                continue;
            }

            if (isReadingData && line.trim()) {
                const values = line.split(',').map(v => v.trim());
                if (values.length >= 2) {
                    glucoseReadings.push({
                        timestamp: values[0],
                        glucose: parseFloat(values[1]),
                        readingType: values[2] || 'Imported'
                    });
                }
            }
        }

        return {
            type: 'csv_import',
            glucoseReadings,
            recordCount: glucoseReadings.length,
            importDate: new Date().toISOString()
        };
    };

    const renderCustomFieldsSelector = () => (
        <div className="custom-fields-selector">
            <h4>Custom Export Options</h4>

            <div className="fields-grid">
                <label className="field-checkbox">
                    <input
                        type="checkbox"
                        checked={customFields.includeRawData}
                        onChange={(e) => setCustomFields(prev => ({
                            ...prev,
                            includeRawData: e.target.checked
                        }))}
                    />
                    Include Raw Glucose Data
                </label>

                <label className="field-checkbox">
                    <input
                        type="checkbox"
                        checked={customFields.includeStatistics}
                        onChange={(e) => setCustomFields(prev => ({
                            ...prev,
                            includeStatistics: e.target.checked
                        }))}
                    />
                    Include AGP Statistics
                </label>

                <label className="field-checkbox">
                    <input
                        type="checkbox"
                        checked={customFields.includeRecommendations}
                        onChange={(e) => setCustomFields(prev => ({
                            ...prev,
                            includeRecommendations: e.target.checked
                        }))}
                    />
                    Include AI Recommendations
                </label>

                <label className="field-checkbox">
                    <input
                        type="checkbox"
                        checked={customFields.includeMetadata}
                        onChange={(e) => setCustomFields(prev => ({
                            ...prev,
                            includeMetadata: e.target.checked
                        }))}
                    />
                    Include Metadata
                </label>

                <label className="field-checkbox">
                    <input
                        type="checkbox"
                        checked={customFields.anonymizeData}
                        onChange={(e) => setCustomFields(prev => ({
                            ...prev,
                            anonymizeData: e.target.checked
                        }))}
                    />
                    Anonymize Patient Data
                </label>
            </div>
        </div>
    );

    return (
        <div className="data-export-import">
            <div className="export-import-header">
                <h2>📊 Data Export & Import</h2>
                <p>Export patient data and analysis results, or import data from external sources</p>
            </div>

            <div className="export-import-tabs">
                {/* Export Section */}
                <div className="export-section">
                    <h3>📤 Export Data</h3>

                    <div className="export-controls">
                        <div className="control-group">
                            <label>Export Type:</label>
                            <select
                                value={exportType}
                                onChange={(e) => setExportType(e.target.value)}
                            >
                                <option value="agp">AGP Report Only</option>
                                <option value="patient">Patient Data Only</option>
                                <option value="full">Complete Export</option>
                                <option value="custom">Custom Selection</option>
                            </select>
                        </div>

                        <div className="control-group">
                            <label>Format:</label>
                            <select
                                value={exportFormat}
                                onChange={(e) => setExportFormat(e.target.value)}
                            >
                                <option value="csv">CSV (Spreadsheet)</option>
                                <option value="json">JSON (Data)</option>
                                <option value="pdf">PDF (Report)</option>
                                <option value="xlsx">Excel (Workbook)</option>
                            </select>
                        </div>
                    </div>

                    {exportType === 'custom' && renderCustomFieldsSelector()}

                    <div className="export-actions">
                        <button
                            className="export-btn primary"
                            onClick={handleExport}
                            disabled={isExporting}
                        >
                            {isExporting ? '⏳ Exporting...' : '📥 Export Data'}
                        </button>

                        <div className="export-info">
                            <p>Export will include data for: <strong>{patientData?.name || 'Current Patient'}</strong></p>
                            {agpData && (
                                <p>AGP data covers {agpData.totalReadings} readings over {agpData.dateRange}</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Import Section */}
                <div className="import-section">
                    <h3>📥 Import Data</h3>

                    <div className="import-controls">
                        <div className="import-area">
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept=".json,.csv,.txt"
                                onChange={handleImport}
                                className="file-input"
                                id="data-import"
                            />
                            <label htmlFor="data-import" className="file-input-label">
                                <span className="upload-icon">📁</span>
                                <span>Choose File to Import</span>
                                <span className="file-types">Supports: JSON, CSV</span>
                            </label>
                        </div>

                        {importStatus && (
                            <div className={`import-status ${importStatus.type}`}>
                                {importStatus.type === 'loading' && <span className="status-icon">⏳</span>}
                                {importStatus.type === 'success' && <span className="status-icon">✅</span>}
                                {importStatus.type === 'error' && <span className="status-icon">❌</span>}
                                <span>{importStatus.message}</span>

                                {importStatus.type === 'success' && importStatus.data && (
                                    <div className="import-summary">
                                        <p>Import successful!</p>
                                        <ul>
                                            <li>Type: {importStatus.data.type}</li>
                                            <li>Records: {importStatus.data.recordCount}</li>
                                            {importStatus.data.patient && (
                                                <li>Patient: {importStatus.data.patient.name}</li>
                                            )}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="import-instructions">
                        <h4>Import Instructions</h4>
                        <ul>
                            <li><strong>JSON:</strong> Import previously exported data files</li>
                            <li><strong>CSV:</strong> Import glucose readings with columns: Timestamp, Glucose, Reading Type</li>
                            <li>Maximum file size: 10MB</li>
                            <li>Data will be validated before import</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DataExportImport;
