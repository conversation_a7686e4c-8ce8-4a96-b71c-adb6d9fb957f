# Global headers for all routes
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: geolocation=(), microphone=(), camera=()

# Cache static assets for 1 year
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# API endpoints - no cache
/api/*
  Cache-Control: no-cache, no-store, must-revalidate

# Service worker
/sw.js
  Cache-Control: no-cache

# Fonts
/*.woff2
  Cache-Control: public, max-age=31536000

# Images
/*.png
  Cache-Control: public, max-age=2592000
/*.jpg
  Cache-Control: public, max-age=2592000
/*.svg
  Cache-Control: public, max-age=2592000
