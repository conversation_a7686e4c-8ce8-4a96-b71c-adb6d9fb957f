import { useState } from 'react';
import './AdvancedAGPAnalytics.css';
import AG<PERSON><PERSON> from './AGPChart';
import AGPStatistics from './AGPStatistics';

const AdvancedAGPAnalytics = ({
    primaryData,
    primaryPatientInfo,
    comparisonData = [],
    comparisonPatients = []
}) => {
    const [analysisMode, setAnalysisMode] = useState('single'); // single, comparison, trends, population
    const [timeframe, setTimeframe] = useState('30'); // days
    const [viewType, setViewType] = useState('overlay'); // overlay, sidebyside, combined
    const [selectedMetrics] = useState([
        'timeInRange',
        'glucoseVariability',
        'hypoglycemiaRisk',
        'hyperglycemiaRisk',
        'gmi'
    ]);
    // Future implementation for trend analysis and population comparison
    // const [trendAnalysis, setTrendAnalysis] = useState(null);
    // const [populationComparison, setPopulationComparison] = useState(null);

    // Advanced metrics calculations
    const calculateAdvancedMetrics = (agpData) => {
        if (!agpData || !agpData.summaryStatistics) return null;

        const stats = agpData.summaryStatistics;
        const tir = agpData.timeInRange;

        return {
            // Glycemic Risk Assessment
            hypoglycemiaRisk: {
                level: tir?.ranges?.veryLow?.percentage > 1 ? 'high' :
                    tir?.ranges?.low?.percentage > 4 ? 'moderate' : 'low',
                score: (tir?.ranges?.veryLow?.percentage || 0) * 10 +
                    (tir?.ranges?.low?.percentage || 0) * 2.5,
                episodes: Math.round(((tir?.ranges?.veryLow?.percentage || 0) +
                    (tir?.ranges?.low?.percentage || 0)) / 100 *
                    (agpData.totalReadings || 0))
            },

            hyperglycemiaRisk: {
                level: tir?.ranges?.veryHigh?.percentage > 5 ? 'high' :
                    tir?.ranges?.high?.percentage > 25 ? 'moderate' : 'low',
                score: (tir?.ranges?.veryHigh?.percentage || 0) * 5 +
                    (tir?.ranges?.high?.percentage || 0) * 1.5,
                episodes: Math.round(((tir?.ranges?.veryHigh?.percentage || 0) +
                    (tir?.ranges?.high?.percentage || 0)) / 100 *
                    (agpData.totalReadings || 0))
            },

            // Glycemic Variability
            variabilityProfile: {
                cv: stats.coefficientOfVariation,
                level: stats.coefficientOfVariation <= 36 ? 'optimal' :
                    stats.coefficientOfVariation <= 50 ? 'acceptable' : 'high',
                mard: Math.abs(stats.mean - 140) / 140 * 100, // Mean Absolute Relative Difference from target
                j_index: 0.001 * (stats.mean + stats.standardDeviation) ** 2 // J-index for variability
            },

            // Time in Range Detailed
            tirDetailed: {
                optimal: tir?.targetRangePercentage >= 70,
                grade: tir?.targetRangePercentage >= 70 ? 'A' :
                    tir?.targetRangePercentage >= 50 ? 'B' :
                        tir?.targetRangePercentage >= 30 ? 'C' : 'D',
                improvement: {
                    needed: Math.max(0, 70 - (tir?.targetRangePercentage || 0)),
                    achievable: Math.min(20, Math.max(0, 70 - (tir?.targetRangePercentage || 0)))
                }
            },

            // Clinical Insights
            clinicalProfile: {
                diabetesType: primaryPatientInfo?.condition?.includes('Type 1') ? 'T1D' : 'T2D',
                riskScore: this.calculateOverallRisk(agpData),
                treatmentResponse: this.assessTreatmentResponse(agpData),
                recommendations: this.generateClinicalRecommendations(agpData)
            }
        };
    };

    const calculateOverallRisk = (agpData) => {
        if (!agpData?.timeInRange) return 0;

        const tir = agpData.timeInRange;
        const cv = agpData.summaryStatistics?.coefficientOfVariation || 0;

        let riskScore = 0;

        // Time below range risk
        riskScore += (tir.ranges?.veryLow?.percentage || 0) * 20;
        riskScore += (tir.ranges?.low?.percentage || 0) * 5;

        // Time above range risk
        riskScore += (tir.ranges?.veryHigh?.percentage || 0) * 10;
        riskScore += (tir.ranges?.high?.percentage || 0) * 2;

        // Variability risk
        if (cv > 50) riskScore += 15;
        else if (cv > 36) riskScore += 8;

        return Math.min(100, Math.round(riskScore));
    };

    const assessTreatmentResponse = (agpData) => {
        // This would normally compare with historical data
        // For now, provide static assessment based on current metrics
        const tir = agpData?.timeInRange?.targetRangePercentage || 0;
        const cv = agpData?.summaryStatistics?.coefficientOfVariation || 0;

        if (tir >= 70 && cv <= 36) return 'excellent';
        if (tir >= 50 && cv <= 50) return 'good';
        if (tir >= 30) return 'fair';
        return 'needs_improvement';
    };

    const generateClinicalRecommendations = (agpData) => {
        const recommendations = [];
        const tir = agpData?.timeInRange;
        const cv = agpData?.summaryStatistics?.coefficientOfVariation || 0;

        if ((tir?.ranges?.veryLow?.percentage || 0) > 1) {
            recommendations.push({
                priority: 'high',
                category: 'hypoglycemia',
                message: 'Severe hypoglycemia risk requires immediate attention',
                action: 'Review basal insulin, consider CGM with alarms'
            });
        }

        if (cv > 36) {
            recommendations.push({
                priority: 'medium',
                category: 'variability',
                message: 'High glucose variability indicates unstable control',
                action: 'Consider continuous monitoring and structured meal planning'
            });
        }

        if ((tir?.targetRangePercentage || 0) < 50) {
            recommendations.push({
                priority: 'high',
                category: 'control',
                message: 'Time in range below target requires intervention',
                action: 'Comprehensive therapy review and diabetes education'
            });
        }

        return recommendations;
    };

    const renderAdvancedMetrics = (metrics) => {
        if (!metrics) return null;

        return (
            <div className="advanced-metrics">
                <h3>Advanced Clinical Metrics</h3>

                <div className="metrics-grid">
                    {/* Risk Assessment */}
                    <div className="metric-card risk-assessment">
                        <h4>🚨 Risk Assessment</h4>
                        <div className="risk-indicators">
                            <div className={`risk-indicator ${metrics.hypoglycemiaRisk.level}`}>
                                <span className="risk-label">Hypoglycemia Risk</span>
                                <span className="risk-value">{metrics.hypoglycemiaRisk.level}</span>
                                <span className="risk-score">{metrics.hypoglycemiaRisk.score.toFixed(1)}</span>
                            </div>
                            <div className={`risk-indicator ${metrics.hyperglycemiaRisk.level}`}>
                                <span className="risk-label">Hyperglycemia Risk</span>
                                <span className="risk-value">{metrics.hyperglycemiaRisk.level}</span>
                                <span className="risk-score">{metrics.hyperglycemiaRisk.score.toFixed(1)}</span>
                            </div>
                        </div>
                    </div>

                    {/* Variability Profile */}
                    <div className="metric-card variability-profile">
                        <h4>📊 Glycemic Variability</h4>
                        <div className="variability-metrics">
                            <div className="variability-item">
                                <span>Coefficient of Variation</span>
                                <span className={`cv-value ${metrics.variabilityProfile.level}`}>
                                    {metrics.variabilityProfile.cv?.toFixed(1)}%
                                </span>
                            </div>
                            <div className="variability-item">
                                <span>J-Index</span>
                                <span>{metrics.variabilityProfile.j_index?.toFixed(2)}</span>
                            </div>
                            <div className="variability-item">
                                <span>MARD from Target</span>
                                <span>{metrics.variabilityProfile.mard?.toFixed(1)}%</span>
                            </div>
                        </div>
                    </div>

                    {/* TIR Detailed */}
                    <div className="metric-card tir-detailed">
                        <h4>🎯 Time in Range Analysis</h4>
                        <div className="tir-grade-container">
                            <div className={`tir-grade grade-${metrics.tirDetailed.grade.toLowerCase()}`}>
                                Grade: {metrics.tirDetailed.grade}
                            </div>
                            <div className="tir-improvement">
                                {metrics.tirDetailed.improvement.needed > 0 ? (
                                    <div>
                                        <span>Improvement needed: {metrics.tirDetailed.improvement.needed.toFixed(1)}%</span>
                                        <span>Achievable goal: +{metrics.tirDetailed.improvement.achievable.toFixed(1)}%</span>
                                    </div>
                                ) : (
                                    <span className="optimal-indicator">✅ Optimal Control Achieved</span>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Clinical Profile */}
                    <div className="metric-card clinical-profile">
                        <h4>🏥 Clinical Profile</h4>
                        <div className="clinical-metrics">
                            <div className="clinical-item">
                                <span>Diabetes Type</span>
                                <span className="diabetes-type">{metrics.clinicalProfile.diabetesType}</span>
                            </div>
                            <div className="clinical-item">
                                <span>Overall Risk Score</span>
                                <span className={`risk-score-value ${metrics.clinicalProfile.riskScore < 20 ? 'low' :
                                    metrics.clinicalProfile.riskScore < 50 ? 'moderate' : 'high'
                                    }`}>
                                    {metrics.clinicalProfile.riskScore}/100
                                </span>
                            </div>
                            <div className="clinical-item">
                                <span>Treatment Response</span>
                                <span className={`treatment-response ${metrics.clinicalProfile.treatmentResponse}`}>
                                    {metrics.clinicalProfile.treatmentResponse.replace('_', ' ')}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Clinical Recommendations */}
                {metrics.clinicalProfile.recommendations.length > 0 && (
                    <div className="clinical-recommendations">
                        <h4>💡 Clinical Recommendations</h4>
                        <div className="recommendations-list">
                            {metrics.clinicalProfile.recommendations.map((rec, index) => (
                                <div key={index} className={`recommendation-item priority-${rec.priority}`}>
                                    <div className="recommendation-header">
                                        <span className={`priority-badge ${rec.priority}`}>
                                            {rec.priority}
                                        </span>
                                        <span className="category-badge">{rec.category}</span>
                                    </div>
                                    <div className="recommendation-content">
                                        <p className="recommendation-message">{rec.message}</p>
                                        <p className="recommendation-action">👉 {rec.action}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderComparisonView = () => {
        if (comparisonData.length === 0) return null;

        return (
            <div className="comparison-view">
                <h3>Multi-Patient Comparison</h3>

                <div className="comparison-controls">
                    <label>
                        View Type:
                        <select value={viewType} onChange={(e) => setViewType(e.target.value)}>
                            <option value="overlay">Overlay Charts</option>
                            <option value="sidebyside">Side by Side</option>
                            <option value="combined">Combined Analysis</option>
                        </select>
                    </label>
                </div>

                <div className={`comparison-charts ${viewType}`}>
                    {viewType === 'overlay' && (
                        <div className="overlay-chart">
                            <AGPChart
                                data={primaryData}
                                comparisonData={comparisonData}
                                showComparison={true}
                            />
                        </div>
                    )}

                    {viewType === 'sidebyside' && (
                        <div className="sidebyside-charts">
                            <div className="chart-container primary">
                                <h4>{primaryPatientInfo?.name || 'Primary Patient'}</h4>
                                <AGPChart data={primaryData} />
                            </div>
                            {comparisonData.map((data, index) => (
                                <div key={index} className="chart-container comparison">
                                    <h4>{comparisonPatients[index]?.name || `Patient ${index + 1}`}</h4>
                                    <AGPChart data={data} />
                                </div>
                            ))}
                        </div>
                    )}

                    {viewType === 'combined' && (
                        <div className="combined-analysis">
                            <div className="combined-metrics-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Patient</th>
                                            <th>TIR (%)</th>
                                            <th>CV (%)</th>
                                            <th>Mean (mg/dL)</th>
                                            <th>Hypo Risk</th>
                                            <th>Hyper Risk</th>
                                            <th>Overall Grade</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr className="primary-row">
                                            <td>{primaryPatientInfo?.name || 'Primary'}</td>
                                            <td>{primaryData?.timeInRange?.targetRangePercentage?.toFixed(1) || 'N/A'}</td>
                                            <td>{primaryData?.summaryStatistics?.coefficientOfVariation?.toFixed(1) || 'N/A'}</td>
                                            <td>{primaryData?.summaryStatistics?.mean?.toFixed(0) || 'N/A'}</td>
                                            <td className="risk-cell">
                                                {((primaryData?.timeInRange?.ranges?.veryLow?.percentage || 0) +
                                                    (primaryData?.timeInRange?.ranges?.low?.percentage || 0)).toFixed(1)}%
                                            </td>
                                            <td className="risk-cell">
                                                {((primaryData?.timeInRange?.ranges?.veryHigh?.percentage || 0) +
                                                    (primaryData?.timeInRange?.ranges?.high?.percentage || 0)).toFixed(1)}%
                                            </td>
                                            <td className="grade-cell">
                                                {primaryData?.timeInRange?.targetRangePercentage >= 70 ? 'A' :
                                                    primaryData?.timeInRange?.targetRangePercentage >= 50 ? 'B' :
                                                        primaryData?.timeInRange?.targetRangePercentage >= 30 ? 'C' : 'D'}
                                            </td>
                                        </tr>
                                        {comparisonData.map((data, index) => (
                                            <tr key={index} className="comparison-row">
                                                <td>{comparisonPatients[index]?.name || `Patient ${index + 1}`}</td>
                                                <td>{data?.timeInRange?.targetRangePercentage?.toFixed(1) || 'N/A'}</td>
                                                <td>{data?.summaryStatistics?.coefficientOfVariation?.toFixed(1) || 'N/A'}</td>
                                                <td>{data?.summaryStatistics?.mean?.toFixed(0) || 'N/A'}</td>
                                                <td className="risk-cell">
                                                    {((data?.timeInRange?.ranges?.veryLow?.percentage || 0) +
                                                        (data?.timeInRange?.ranges?.low?.percentage || 0)).toFixed(1)}%
                                                </td>
                                                <td className="risk-cell">
                                                    {((data?.timeInRange?.ranges?.veryHigh?.percentage || 0) +
                                                        (data?.timeInRange?.ranges?.high?.percentage || 0)).toFixed(1)}%
                                                </td>
                                                <td className="grade-cell">
                                                    {data?.timeInRange?.targetRangePercentage >= 70 ? 'A' :
                                                        data?.timeInRange?.targetRangePercentage >= 50 ? 'B' :
                                                            data?.timeInRange?.targetRangePercentage >= 30 ? 'C' : 'D'}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        );
    };

    // Calculate advanced metrics for primary data
    const primaryMetrics = primaryData ? calculateAdvancedMetrics(primaryData) : null;

    return (
        <div className="advanced-agp-analytics">
            <div className="analytics-header">
                <h2>🔬 Advanced AGP Analytics</h2>

                <div className="analytics-controls">
                    <div className="mode-selector">
                        <label>Analysis Mode:</label>
                        <select value={analysisMode} onChange={(e) => setAnalysisMode(e.target.value)}>
                            <option value="single">Single Patient</option>
                            <option value="comparison">Multi-Patient Comparison</option>
                            <option value="trends">Trend Analysis</option>
                            <option value="population">Population Analysis</option>
                        </select>
                    </div>

                    <div className="timeframe-selector">
                        <label>Timeframe:</label>
                        <select value={timeframe} onChange={(e) => setTimeframe(e.target.value)}>
                            <option value="7">7 Days</option>
                            <option value="14">14 Days</option>
                            <option value="30">30 Days</option>
                            <option value="90">90 Days</option>
                            <option value="180">6 Months</option>
                        </select>
                    </div>
                </div>
            </div>

            <div className="analytics-content">
                {analysisMode === 'single' && primaryData && (
                    <div className="single-patient-analysis">
                        <div className="primary-charts">
                            <AGPChart data={primaryData} />
                            <AGPStatistics data={primaryData} />
                        </div>
                        {renderAdvancedMetrics(primaryMetrics)}
                    </div>
                )}

                {analysisMode === 'comparison' && (
                    renderComparisonView()
                )}

                {analysisMode === 'trends' && (
                    <div className="trends-analysis">
                        <h3>📈 Trend Analysis</h3>
                        <p>Longitudinal trend analysis feature coming soon...</p>
                    </div>
                )}

                {analysisMode === 'population' && (
                    <div className="population-analysis">
                        <h3>👥 Population Analysis</h3>
                        <p>Population health analytics feature coming soon...</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AdvancedAGPAnalytics;
