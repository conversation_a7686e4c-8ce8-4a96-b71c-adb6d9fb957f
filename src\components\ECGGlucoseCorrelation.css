.ecg-glucose-correlation {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  max-width: 100%;
  overflow: hidden;
  color: var(--gb-fg);
}

.correlation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 1rem;
}

.patient-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--gb-accent2);
  font-size: 1.25rem;
  font-weight: 600;
}

.patient-name,
.patient-condition {
  margin: 0.25rem 0;
  color: rgba(235, 219, 178, 0.75);
  font-size: 0.9rem;
}

.view-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.view-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab-button {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 6px 6px 0 0;
  background: rgba(235, 219, 178, 0.05);
  color: var(--gb-fg);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background: rgba(235, 219, 178, 0.08);
  border-color: rgba(235, 219, 178, 0.35);
}

.tab-button.active {
  background: var(--gb-bg1);
  color: var(--gb-accent2);
  border-color: var(--gb-accent2) var(--gb-accent2) transparent var(--gb-accent2);
  border-bottom: 2px solid var(--gb-bg1);
  margin-bottom: -2px;
}

.metric-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metric-selector label {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.85);
}

.metric-selector select {
  padding: 0.4rem 0.8rem;
  border: 1px solid rgba(235, 219, 178, 0.25);
  border-radius: 4px;
  font-size: 0.9rem;
  background: rgba(235, 219, 178, 0.05);
  color: var(--gb-fg);
  min-width: 200px;
}

.correlation-stats {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1rem;
}

.stat-card {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(235, 219, 178, 0.12);
  text-align: center;
}

.correlation-card {
  text-align: left;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.correlation-badge {
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gb-fg);
  font-family: 'Courier New', monospace;
  margin-bottom: 0.25rem;
}

.correlation-value {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-description {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.75);
  font-style: italic;
}

.chart-container {
  height: 500px;
  width: 100%;
  max-width: 100%;
  position: relative;
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 6px;
  background: var(--gb-bg1);
  padding: 1rem;
  overflow: hidden;
  display: block;
  /* avoid flex sizing side-effects for Chart.js */
  min-width: 0;
  /* prevent overflow from flex parents */
}

/* Ensure Chart.js canvas respects container sizing */
.chart-container>canvas,
.chart-container canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
}

.chart-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
  color: rgba(235, 219, 178, 0.75);
  width: 100%;
  height: 100%;
}

.empty-chart-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.chart-empty-state h4 {
  margin: 0 0 1rem 0;
  color: var(--gb-accent2);
  font-size: 1.25rem;
  font-weight: 600;
}

.chart-empty-state p {
  margin: 0.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
  max-width: 400px;
}

.empty-chart-suggestion {
  font-style: italic;
  color: rgba(235, 219, 178, 0.7);
}

.correlation-insights {
  margin-top: 1rem;
}

.correlation-insights h4 {
  margin: 0 0 1rem 0;
  color: var(--gb-fg);
  font-size: 1.1rem;
  font-weight: 600;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.insight-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(235, 219, 178, 0.05);
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 6px;
  border-left: 4px solid var(--gb-accent2);
}

.insight-card.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
  border-left-color: #ffc107;
}

.insight-card.info {
  background: rgba(23, 162, 184, 0.1);
  border-color: rgba(23, 162, 184, 0.2);
  border-left-color: #17a2b8;
}

.insight-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.insight-content h5 {
  margin: 0 0 0.5rem 0;
  color: var(--gb-fg);
  font-size: 0.9rem;
  font-weight: 600;
}

.insight-content p {
  margin: 0;
  color: rgba(235, 219, 178, 0.85);
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .correlation-stats {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .correlation-card {
    grid-column: 1 / -1;
  }

  .chart-container {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .ecg-glucose-correlation {
    padding: 1rem;
    gap: 1rem;
    max-width: 100vw;
  }

  .correlation-header {
    flex-direction: column;
    align-items: stretch;
  }

  .view-controls {
    align-items: stretch;
  }

  .view-tabs {
    justify-content: center;
  }

  .metric-selector {
    justify-content: center;
  }

  .correlation-stats {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 350px;
    padding: 0.5rem;
    width: calc(100vw - 4rem);
    max-width: 100%;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .ecg-glucose-correlation {
    padding: 0.5rem;
  }

  .tab-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .metric-selector select {
    min-width: 150px;
    font-size: 0.8rem;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .correlation-value {
    font-size: 1.5rem;
  }

  .chart-container {
    height: 300px;
    padding: 0.25rem;
  }

  .insight-card {
    padding: 0.75rem;
    gap: 0.75rem;
  }

  .insight-icon {
    font-size: 1.25rem;
  }
}
