@echo off
setlocal ENABLEDELAYEDEXPANSION

:: HealthHub Research Platform - Local Development Runner (Windows .bat)
echo ==============================================
echo   HealthHub Research Platform - Local Dev
echo ==============================================
echo.

:: Verify Node and npm are available
where node >nul 2>nul || (echo ERROR: Node.js is not in PATH. Install Node and reopen your terminal. & exit /b 1)
where npm  >nul 2>nul || (echo ERROR: npm is not in PATH. Install Node and reopen your terminal. & exit /b 1)

:: Optional: install dependencies if node_modules missing
if not exist "node_modules" (
  echo Installing dependencies (first run)...
  call npm install || (echo ERROR: npm install failed. & exit /b 1)
)

:: Warn if Wrangler local secrets file is missing
if not exist ".dev.vars" (
  echo WARNING: .dev.vars not found. API secrets (NEO4J_*, OPENAI_API_KEY) will be unavailable.
  echo          Copy .dev.vars.example to .dev.vars to enable full API features locally.
  echo.
)

:: Start Cloudflare Pages Functions (Wrangler) on port 8787
set API_TITLE=API (Wrangler 8787)
echo Starting %%API_TITLE%% ...
start "%%API_TITLE%%" cmd /c "npx wrangler pages dev public --port=8787"

:: Small delay to let Wrangler bind the port
ping 127.0.0.1 -n 2 >nul

:: Start Vite dev server on port 5173
set WEB_TITLE=Web (Vite 5173)
echo Starting %%WEB_TITLE%% ...
start "%%WEB_TITLE%%" cmd /c "npm run dev:web"

:: Optional: open browser after servers start
ping 127.0.0.1 -n 2 >nul
start "" http://localhost:5173/

echo.
echo Both servers started.
echo  - Vite:   http://localhost:5173
if "%1"=="/noapi" (
  echo  - API:    (not started by this script)
) else (
  echo  - API:    http://127.0.0.1:8787
  echo  - Health: http://127.0.0.1:8787/api/health
)
echo.
echo Close the spawned windows to stop each process.
exit /b 0
