import { useMemo, useState } from 'react';

const InteractiveDataTable = ({
    data = [],
    columns = [],
    searchable = true,
    filterable = true,
    sortable = true,
    pagination = true,
    pageSize = 10
}) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
    const [filters, setFilters] = useState({});
    const [currentPage, setCurrentPage] = useState(1);
    const [selectedRows, setSelectedRows] = useState(new Set());

    // Filter and search data
    const filteredData = useMemo(() => {
        let result = data;

        // Apply search
        if (searchTerm) {
            result = result.filter(item =>
                Object.values(item).some(value =>
                    String(value).toLowerCase().includes(searchTerm.toLowerCase())
                )
            );
        }

        // Apply filters
        Object.entries(filters).forEach(([key, value]) => {
            if (value) {
                result = result.filter(item => String(item[key]) === String(value));
            }
        });

        // Apply sorting
        if (sortConfig.key) {
            result.sort((a, b) => {
                const aVal = a[sortConfig.key];
                const bVal = b[sortConfig.key];

                if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
                if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
                return 0;
            });
        }

        return result;
    }, [data, searchTerm, filters, sortConfig]);

    // Paginate data
    const paginatedData = useMemo(() => {
        if (!pagination) return filteredData;

        const startIndex = (currentPage - 1) * pageSize;
        return filteredData.slice(startIndex, startIndex + pageSize);
    }, [filteredData, currentPage, pageSize, pagination]);

    const totalPages = Math.ceil(filteredData.length / pageSize);

    const handleSort = (key) => {
        setSortConfig(prev => ({
            key,
            direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
        }));
    };

    const handleFilter = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value === 'all' ? '' : value
        }));
        setCurrentPage(1);
    };

    const handleRowSelect = (rowId) => {
        const newSelected = new Set(selectedRows);
        if (newSelected.has(rowId)) {
            newSelected.delete(rowId);
        } else {
            newSelected.add(rowId);
        }
        setSelectedRows(newSelected);
    };

    const getUniqueValues = (key) => {
        return [...new Set(data.map(item => item[key]))].filter(Boolean);
    };

    return (
        <div className="interactive-data-table">
            {/* Table Header with Search and Filters */}
            <div className="table-controls space-compact">
                {searchable && (
                    <div className="search-container">
                        <input
                            type="text"
                            placeholder="Search across all fields..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input"
                        />
                    </div>
                )}

                {filterable && (
                    <div className="filter-container">
                        <div className="filter-group">
                            {columns.filter(col => col.filterable).map(column => (
                                <select
                                    key={column.key}
                                    value={filters[column.key] || ''}
                                    onChange={(e) => handleFilter(column.key, e.target.value)}
                                    className="filter-select"
                                >
                                    <option value="">All {column.label}</option>
                                    {getUniqueValues(column.key).map(value => (
                                        <option key={value} value={value}>{value}</option>
                                    ))}
                                </select>
                            ))}
                        </div>
                    </div>
                )}

                <div className="table-info">
                    <span className="text-small text-muted">
                        Showing {paginatedData.length} of {filteredData.length} entries
                        {selectedRows.size > 0 && ` (${selectedRows.size} selected)`}
                    </span>
                </div>
            </div>

            {/* Data Table */}
            <div className="table-container">
                <table className="dense-table interactive-table">
                    <thead>
                        <tr>
                            <th className="select-column">
                                <input
                                    type="checkbox"
                                    onChange={(e) => {
                                        if (e.target.checked) {
                                            setSelectedRows(new Set(paginatedData.map((_, idx) => idx)));
                                        } else {
                                            setSelectedRows(new Set());
                                        }
                                    }}
                                    checked={selectedRows.size === paginatedData.length && paginatedData.length > 0}
                                />
                            </th>
                            {columns.map(column => (
                                <th
                                    key={column.key}
                                    className={`${column.align || 'left'} ${sortable && column.sortable !== false ? 'sortable' : ''}`}
                                    onClick={() => sortable && column.sortable !== false && handleSort(column.key)}
                                >
                                    <div className="th-content">
                                        <span>{column.label}</span>
                                        {sortable && column.sortable !== false && (
                                            <span className="sort-indicator">
                                                {sortConfig.key === column.key
                                                    ? (sortConfig.direction === 'asc' ? '▲' : '▼')
                                                    : '⇅'
                                                }
                                            </span>
                                        )}
                                    </div>
                                </th>
                            ))}
                            <th className="actions-column">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {paginatedData.map((row, index) => (
                            <tr
                                key={index}
                                className={`${selectedRows.has(index) ? 'selected' : ''} interactive-row`}
                                onClick={() => handleRowSelect(index)}
                            >
                                <td className="select-column">
                                    <input
                                        type="checkbox"
                                        checked={selectedRows.has(index)}
                                        onChange={() => handleRowSelect(index)}
                                        onClick={(e) => e.stopPropagation()}
                                    />
                                </td>
                                {columns.map(column => (
                                    <td key={column.key} className={column.align || 'left'}>
                                        {column.render
                                            ? column.render(row[column.key], row, index)
                                            : row[column.key]
                                        }
                                    </td>
                                ))}
                                <td className="actions-column">
                                    <div className="action-buttons">
                                        <button className="text-tiny" title="View Details">👁️</button>
                                        <button className="text-tiny" title="Edit">✏️</button>
                                        <button className="text-tiny" title="Delete">🗑️</button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            {pagination && totalPages > 1 && (
                <div className="pagination-container">
                    <div className="pagination-info">
                        <span className="text-small text-muted">
                            Page {currentPage} of {totalPages}
                        </span>
                    </div>
                    <div className="pagination-controls">
                        <button
                            disabled={currentPage === 1}
                            onClick={() => setCurrentPage(1)}
                            className="pagination-btn"
                        >
                            ⏮️
                        </button>
                        <button
                            disabled={currentPage === 1}
                            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                            className="pagination-btn"
                        >
                            ◀️
                        </button>

                        {/* Page numbers */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            const pageNum = Math.max(1, currentPage - 2) + i;
                            if (pageNum <= totalPages) {
                                return (
                                    <button
                                        key={pageNum}
                                        onClick={() => setCurrentPage(pageNum)}
                                        className={`pagination-btn ${currentPage === pageNum ? 'active' : ''}`}
                                    >
                                        {pageNum}
                                    </button>
                                );
                            }
                            return null;
                        })}

                        <button
                            disabled={currentPage === totalPages}
                            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                            className="pagination-btn"
                        >
                            ▶️
                        </button>
                        <button
                            disabled={currentPage === totalPages}
                            onClick={() => setCurrentPage(totalPages)}
                            className="pagination-btn"
                        >
                            ⏭️
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

const ExpandableSection = ({ title, children, defaultExpanded = false, icon = null }) => {
    const [isExpanded, setIsExpanded] = useState(defaultExpanded);

    return (
        <div className={`collapsible ${isExpanded ? 'expanded' : ''}`}>
            <div
                className="collapsible-header"
                onClick={() => setIsExpanded(!isExpanded)}
            >
                <span className="collapsible-title">
                    {icon && <span className="collapsible-icon-prefix">{icon}</span>}
                    {title}
                </span>
                <span className={`collapsible-icon ${isExpanded ? 'expanded' : ''}`}>
                    ▼
                </span>
            </div>
            <div className="collapsible-content">
                {children}
            </div>
        </div>
    );
};

const TooltipWrapper = ({ children, content, position = 'top' }) => {
    return (
        <span className={`tooltip tooltip-${position}`} data-tooltip={content}>
            {children}
        </span>
    );
};

const ProgressiveDisclosure = ({ summary, details, maxSummaryLength = 100 }) => {
    const [showDetails, setShowDetails] = useState(false);

    const truncatedSummary = summary.length > maxSummaryLength
        ? summary.substring(0, maxSummaryLength) + '...'
        : summary;

    return (
        <div className="progressive-disclosure">
            <p className="disclosure-summary text-small">
                {showDetails ? summary : truncatedSummary}
                {summary.length > maxSummaryLength && (
                    <button
                        className="disclosure-toggle text-tiny text-accent"
                        onClick={() => setShowDetails(!showDetails)}
                    >
                        {showDetails ? ' Show Less' : ' Show More'}
                    </button>
                )}
            </p>
            {showDetails && details && (
                <div className="disclosure-details">
                    {details}
                </div>
            )}
        </div>
    );
};

const FilterableCardGrid = ({ items, filterOptions, renderCard }) => {
    const [activeFilters, setActiveFilters] = useState({});
    const [sortBy, setSortBy] = useState('name');

    const filteredItems = useMemo(() => {
        let result = items;

        // Apply filters
        Object.entries(activeFilters).forEach(([key, value]) => {
            if (value && value !== 'all') {
                result = result.filter(item => item[key] === value);
            }
        });

        // Apply sorting
        result.sort((a, b) => {
            if (a[sortBy] < b[sortBy]) return -1;
            if (a[sortBy] > b[sortBy]) return 1;
            return 0;
        });

        return result;
    }, [items, activeFilters, sortBy]);

    return (
        <div className="filterable-card-grid">
            <div className="filter-controls">
                <div className="filter-row">
                    {filterOptions.map(option => (
                        <div key={option.key} className="filter-group">
                            <label className="text-tiny font-medium">{option.label}</label>
                            <select
                                value={activeFilters[option.key] || 'all'}
                                onChange={(e) => setActiveFilters(prev => ({
                                    ...prev,
                                    [option.key]: e.target.value
                                }))}
                                className="filter-select"
                            >
                                <option value="all">All {option.label}</option>
                                {option.options.map(opt => (
                                    <option key={opt.value} value={opt.value}>
                                        {opt.label}
                                    </option>
                                ))}
                            </select>
                        </div>
                    ))}

                    <div className="sort-group">
                        <label className="text-tiny font-medium">Sort by</label>
                        <select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value)}
                            className="filter-select"
                        >
                            <option value="name">Name</option>
                            <option value="date">Date</option>
                            <option value="status">Status</option>
                            <option value="priority">Priority</option>
                        </select>
                    </div>
                </div>

                <div className="results-info">
                    <span className="text-small text-muted">
                        {filteredItems.length} of {items.length} items
                    </span>
                </div>
            </div>

            <div className="card-grid grid-dense">
                {filteredItems.map((item, index) => renderCard(item, index))}
            </div>
        </div>
    );
};

export {
    ExpandableSection, FilterableCardGrid, InteractiveDataTable, ProgressiveDisclosure, TooltipWrapper
};
