.d1namo-dashboard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1.5rem;
  min-height: 100vh;
  background: var(--gb-bg0);
  color: var(--gb-fg);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  background: linear-gradient(135deg, var(--gb-bg1) 0%, rgba(215, 153, 33, 0.12) 100%);
  color: var(--gb-fg);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.header-content h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-content p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

.header-controls {
  display: flex;
  gap: 2rem;
  align-items: flex-end;
}

.patient-selector,
.time-range-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 200px;
}

.patient-selector label,
.time-range-selector label {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
}

.patient-selector select,
.time-range-selector select {
  padding: 0.75rem 1rem;
  border: 1px solid rgba(235, 219, 178, 0.2);
  border-radius: 6px;
  background: rgba(235, 219, 178, 0.05);
  color: var(--gb-fg);
  font-size: 0.9rem;
}

.patient-selector select:focus,
.time-range-selector select:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(250, 189, 47, 0.1);
}

.patient-selector select option,
.time-range-selector select option {
  background: var(--gb-bg1);
  color: var(--gb-fg);
}

.error-message {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(220, 53, 69, 0.08);
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 8px;
  color: #f2b8b5;
}

.error-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.error-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.error-content p {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
}

.retry-button {
  padding: 0.5rem 1rem;
  background: rgba(220, 53, 69, 0.2);
  color: #ffb4ab;
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: rgba(220, 53, 69, 0.3);
}

.patient-summary {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  padding: 1.5rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  border-left: 4px solid var(--gb-accent2);
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.patient-info h3 {
  margin: 0 0 1rem 0;
  color: var(--gb-accent2);
  font-size: 1.5rem;
  font-weight: 600;
}

.patient-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.patient-details span {
  padding: 0.4rem 0.8rem;
  background: rgba(235, 219, 178, 0.08);
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--gb-fg);
  border: 1px solid rgba(235, 219, 178, 0.15);
}

.dashboard-stats {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 100px;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--gb-accent2);
  font-family: 'Courier New', monospace;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.75);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: rgba(235, 219, 178, 0.75);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(235, 219, 178, 0.15);
  border-top: 4px solid var(--gb-accent2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.analysis-section,
.waveform-section {
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.analysis-section h2,
.waveform-section h2 {
  margin: 0;
  padding: 1.25rem;
  background: rgba(235, 219, 178, 0.05);
  border-bottom: 1px solid rgba(235, 219, 178, 0.12);
  color: var(--gb-fg);
  font-size: 1.25rem;
  font-weight: 600;
}

.waveform-info {
  padding: 1rem 1.25rem;
  background: rgba(235, 219, 178, 0.03);
  border-bottom: 1px solid rgba(235, 219, 178, 0.12);
}

.waveform-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.85);
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  text-align: center;
  color: rgba(235, 219, 178, 0.75);
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.no-data-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data-message h3 {
  margin: 0 0 1rem 0;
  color: var(--gb-accent2);
  font-size: 1.5rem;
  font-weight: 600;
}

.no-data-message p {
  margin: 0.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
}

.no-data-message ul {
  text-align: left;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.no-data-message li {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.refresh-button {
  margin-top: 1.5rem;
  padding: 0.6rem 1.25rem;
  background: transparent;
  color: var(--gb-accent2);
  border: 1px solid var(--gb-accent2);
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background: rgba(250, 189, 47, 0.1);
}

.dashboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.footer-info {
  flex: 1;
}

.footer-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.75);
  line-height: 1.4;
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

.footer-actions button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--gb-accent2);
  border-radius: 4px;
  background: transparent;
  color: var(--gb-accent2);
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.footer-actions button:hover:not(:disabled) {
  background: rgba(250, 189, 47, 0.1);
}

.footer-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-header {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }

  .header-controls {
    justify-content: space-between;
  }

  .patient-summary {
    flex-direction: column;
    gap: 1.5rem;
  }

  .dashboard-stats {
    justify-content: center;
  }

  .dashboard-footer {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .d1namo-dashboard {
    padding: 1rem;
    gap: 1.5rem;
  }

  .dashboard-header {
    padding: 1.5rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .header-content p {
    font-size: 1rem;
  }

  .header-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .patient-selector,
  .time-range-selector {
    min-width: unset;
  }

  .dashboard-stats {
    gap: 1rem;
    justify-content: space-around;
  }

  .stat-item {
    min-width: 80px;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .dashboard-content {
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .d1namo-dashboard {
    padding: 0.5rem;
  }

  .dashboard-header {
    padding: 1rem;
  }

  .header-content h1 {
    font-size: 1.25rem;
  }

  .header-content p {
    font-size: 0.9rem;
  }

  .patient-summary {
    padding: 1rem;
  }

  .dashboard-stats {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .footer-actions {
    flex-direction: column;
    width: 100%;
  }

  .footer-actions button {
    width: 100%;
  }
}

/* Analytics placeholder styles */
.analytics-placeholder {
  padding: 2rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.12);
  text-align: center;
}

.analytics-placeholder p {
  margin: 0.5rem 0;
  color: var(--gb-fg);
}

.analytics-placeholder ul {
  text-align: left;
  display: inline-block;
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.analytics-placeholder li {
  margin: 0.5rem 0;
  color: var(--gb-fg-2);
}

.analytics-placeholder h3 {
  color: var(--gb-yellow);
  margin-bottom: 1rem;
}

/* Data availability and quality indicators */
.data-availability-info {
  margin-top: 8px;
}

.availability-badge,
.quality-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-block;
}

.availability-badge.complete,
.quality-badge.excellent {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.availability-badge.partial,
.quality-badge.good {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.availability-badge.no-data,
.quality-badge.limited {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f1aeb5;
}

/* Enhanced patient summary */
.data-quality-summary {
  margin-top: 16px;
  padding: 16px;
  background: var(--gb-bg0-soft);
  border-radius: 8px;
  border: 1px solid rgba(235, 219, 178, 0.12);
}

.data-quality-summary h4 {
  margin: 0 0 12px 0;
  color: var(--gb-fg);
  font-size: 1rem;
}

.quality-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 8px;
}

.quality-metrics .metric {
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.75);
}

.quality-metrics .metric strong {
  color: var(--gb-fg);
  font-weight: 600;
}

.time-range-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(235, 219, 178, 0.12);
}

.time-range-info small {
  display: block;
  color: rgba(235, 219, 178, 0.6);
  font-size: 0.8rem;
  margin: 2px 0;
}

/* Enhanced no-data message */
.no-data-guidance,
.data-guidance {
  margin: 16px 0;
  padding: 16px;
  background: rgba(250, 189, 47, 0.1);
  border: 1px solid rgba(250, 189, 47, 0.3);
  border-radius: 8px;
}

.no-data-guidance p:first-child {
  font-weight: 600;
  color: var(--gb-yellow);
  margin-bottom: 8px;
}

.no-data-guidance ul,
.data-guidance ul {
  margin: 12px 0;
  padding-left: 20px;
}

.no-data-guidance li,
.data-guidance li {
  margin: 4px 0;
  color: rgba(235, 219, 178, 0.85);
}

/* Dataset summary in footer */
.dataset-summary {
  margin: 12px 0;
  padding: 12px;
  background: rgba(250, 189, 47, 0.1);
  border-radius: 6px;
  font-size: 0.9rem;
}

.dataset-summary p {
  margin: 6px 0;
  color: rgba(235, 219, 178, 0.85);
}

.dataset-summary strong {
  color: var(--gb-fg);
}

/* Patient selector enhancements */
.patient-selector select {
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  font-size: 0.9rem;
}

.patient-selector option {
  padding: 8px;
}

/* Loading states for quality info */
.loading-quality {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(235, 219, 178, 0.2);
  border-top: 2px solid var(--gb-accent2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

/* Responsive adjustments for quality indicators */
@media (max-width: 768px) {
  .quality-metrics {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .data-quality-summary {
    padding: 12px;
  }
  
  .availability-badge,
  .quality-badge {
    font-size: 0.8rem;
    padding: 3px 6px;
  }
}

/* Enhanced stat value quality indicators */
.stat-value.quality-excellent {
  color: #28a745;
}

.stat-value.quality-good {
  color: #20c997;
}

.stat-value.quality-fair {
  color: #ffc107;
}

.stat-value.quality-poor {
  color: #dc3545;
}
