// Enhanced Neo4j Database Population Script for D1NAMO ECG-Glucose Integration
// Integrates ECG data with existing glucose monitoring system
// Designed to work with D1NAMO dataset structure

import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

// Database connection configuration
const uri = process.env.VITE_NEO4J_URI;
const username = process.env.VITE_NEO4J_USERNAME;
const password = process.env.VITE_NEO4J_PASSWORD;

if (!uri || !username || !password) {
  console.error('Missing Neo4j env vars. Please set VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD.');
  process.exit(1);
}

const driver = neo4j.driver(uri, neo4j.auth.basic(username, password));

// D1NAMO Dataset Patient Profiles (based on typical diabetes research datasets)
const d1namoPatients = [
  {
    patientId: 'D1NAMO_001',
    name: '<PERSON>',
    age: 42,
    gender: 'Female',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2020-03-15',
    insurance: 'Premium Health',
    phone: '555-2001',
    email: '<EMAIL>',
    diabetesDuration: 22, // years since diagnosis
    cgmModel: 'FreeStyle Libre',
    ecgDevice: 'Holter Monitor 24h',
    studyPhase: 'Phase 1',
    baselineHbA1c: 7.2
  },
  {
    patientId: 'D1NAMO_002',
    name: 'Carlos Rodriguez',
    age: 35,
    gender: 'Male',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2015-08-10',
    insurance: 'State Health',
    phone: '555-2002',
    email: '<EMAIL>',
    diabetesDuration: 9,
    cgmModel: 'Dexcom G6',
    ecgDevice: 'Holter Monitor 24h',
    studyPhase: 'Phase 1',
    baselineHbA1c: 6.8
  },
  {
    patientId: 'D1NAMO_003',
    name: 'Rachel Kim',
    age: 28,
    gender: 'Female',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2018-12-05',
    insurance: 'University Health',
    phone: '555-2003',
    email: '<EMAIL>',
    diabetesDuration: 6,
    cgmModel: 'FreeStyle Libre',
    ecgDevice: 'Holter Monitor 24h',
    studyPhase: 'Phase 2',
    baselineHbA1c: 7.1
  },
  {
    patientId: 'D1NAMO_004',
    name: 'James Wilson',
    age: 45,
    gender: 'Male',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2012-06-20',
    insurance: 'Federal Health',
    phone: '555-2004',
    email: '<EMAIL>',
    diabetesDuration: 12,
    cgmModel: 'Dexcom G6',
    ecgDevice: 'Holter Monitor 24h',
    studyPhase: 'Phase 2',
    baselineHbA1c: 7.5
  },
  {
    patientId: 'D1NAMO_005',
    name: 'Sophie Chen',
    age: 31,
    gender: 'Female',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2016-11-12',
    insurance: 'Research Insurance',
    phone: '555-2005',
    email: '<EMAIL>',
    diabetesDuration: 8,
    cgmModel: 'FreeStyle Libre',
    ecgDevice: 'Holter Monitor 24h',
    studyPhase: 'Phase 3',
    baselineHbA1c: 6.9
  }
];

// ECG Lead configurations (standard 12-lead ECG)
const ECG_LEADS = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6'];

// ECG Feature extraction parameters
const ECG_FEATURES = [
  'heart_rate', 'rr_interval', 'pr_interval', 'qrs_duration', 'qt_interval', 'qtc_interval',
  'p_wave_amplitude', 'q_wave_amplitude', 'r_wave_amplitude', 's_wave_amplitude', 't_wave_amplitude',
  'st_elevation', 'st_depression', 'heart_rate_variability', 'ectopic_beats'
];

// Generate realistic ECG data synchronized with glucose readings
function generateECGReading(patientId, timestamp, glucoseLevel) {
  const baseHeartRate = 70 + (Math.random() - 0.5) * 20; // 60-80 bpm baseline

  // Correlate heart rate with glucose levels (simplified physiological model)
  let heartRateAdjustment = 0;
  if (glucoseLevel < 70) heartRateAdjustment += 10; // Hypoglycemic tachycardia
  if (glucoseLevel > 180) heartRateAdjustment += 5;  // Hyperglycemic stress

  const heartRate = Math.max(50, Math.min(120, baseHeartRate + heartRateAdjustment));
  const rrInterval = Math.round(60000 / heartRate); // milliseconds

  // Generate ECG features with realistic correlations
  const ecgFeatures = {
    heart_rate: Math.round(heartRate),
    rr_interval: rrInterval,
    pr_interval: Math.round(120 + (Math.random() - 0.5) * 40), // 100-160ms
    qrs_duration: Math.round(90 + (Math.random() - 0.5) * 20),  // 80-100ms
    qt_interval: Math.round(380 + (Math.random() - 0.5) * 40),  // 360-400ms
    qtc_interval: Math.round(420 + (Math.random() - 0.5) * 40), // 400-440ms
    p_wave_amplitude: Math.round((0.1 + Math.random() * 0.15) * 1000) / 1000, // mV
    q_wave_amplitude: Math.round((-0.05 + Math.random() * 0.1) * 1000) / 1000,
    r_wave_amplitude: Math.round((0.8 + Math.random() * 0.4) * 1000) / 1000,
    s_wave_amplitude: Math.round((-0.2 + Math.random() * 0.3) * 1000) / 1000,
    t_wave_amplitude: Math.round((0.2 + Math.random() * 0.2) * 1000) / 1000,
    st_elevation: Math.round((Math.random() - 0.5) * 0.1 * 1000) / 1000,
    st_depression: Math.round((Math.random() - 0.5) * 0.1 * 1000) / 1000,
    heart_rate_variability: Math.round(30 + Math.random() * 40), // RMSSD ms
    ectopic_beats: Math.random() < 0.05 ? Math.floor(Math.random() * 3) : 0
  };

  // Generate lead-specific waveform data (simplified representation)
  const leadData = {};
  ECG_LEADS.forEach(lead => {
    // Generate a simplified ECG waveform (in a real implementation, this would be actual signal data)
    const samples = Array.from({ length: 1000 }, (_, i) => {
      // Simple sine wave approximation with ECG-like characteristics
      const t = i / 1000 * 2 * Math.PI;
      let amplitude = 0;

      // Simplified P-QRS-T complex generation
      if (i >= 100 && i <= 150) amplitude = ecgFeatures.p_wave_amplitude * Math.sin((i - 100) / 50 * Math.PI);
      if (i >= 300 && i <= 400) {
        // QRS complex
        if (i <= 320) amplitude = ecgFeatures.q_wave_amplitude;
        else if (i <= 360) amplitude = ecgFeatures.r_wave_amplitude * Math.sin((i - 320) / 40 * Math.PI);
        else amplitude = ecgFeatures.s_wave_amplitude;
      }
      if (i >= 500 && i <= 650) amplitude = ecgFeatures.t_wave_amplitude * Math.sin((i - 500) / 150 * Math.PI);

      return Math.round(amplitude * 1000) / 1000;
    });

    leadData[lead] = samples;
  });

  return {
    patientId,
    timestamp,
    glucoseLevel,
    sampling_rate: 1000, // Hz
    duration: 10, // seconds
    device_id: 'HOLTER_001',
    signal_quality: Math.random() > 0.1 ? 'Good' : 'Fair',
    features: ecgFeatures,
    lead_data: leadData,
    analysis_timestamp: new Date().toISOString()
  };
}

// Generate synchronized ECG and Glucose data
function generateSynchronizedData(patientId, days = 7) {
  const readings = [];
  const now = new Date();
  const startDate = new Date(now);
  startDate.setDate(startDate.getDate() - days);

  // Generate readings every 15 minutes (CGM frequency)
  for (let day = 0; day < days; day++) {
    for (let hour = 0; hour < 24; hour++) {
      for (let quarter = 0; quarter < 4; quarter++) {
        const timestamp = new Date(startDate);
        timestamp.setDate(timestamp.getDate() + day);
        timestamp.setHours(hour);
        timestamp.setMinutes(quarter * 15);
        timestamp.setSeconds(0);

        // Generate realistic glucose pattern
        let baseGlucose = 120;

        // Circadian rhythm effects
        if (hour >= 6 && hour <= 9) baseGlucose += 20; // Dawn phenomenon
        if ((hour >= 7 && hour <= 9) || (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20)) {
          baseGlucose += 25; // Meal times
        }
        if (hour >= 2 && hour <= 4) baseGlucose -= 10; // Early morning dip

        const glucose = Math.max(60, Math.min(300,
          baseGlucose + (Math.random() - 0.5) * 40
        ));

        // Generate corresponding ECG reading
        const ecgReading = generateECGReading(patientId, timestamp.toISOString(), glucose);

        readings.push({
          glucose: {
            timestamp: timestamp.toISOString(),
            glucose: Math.round(glucose),
            readingType: 'Continuous',
            device: 'CGM'
          },
          ecg: ecgReading
        });
      }
    }
  }

  return readings;
}

// Database creation functions
async function createD1NAMOSchema(session) {
  console.log('Creating D1NAMO schema extensions...');

  // Create indexes for performance
  const indexes = [
    'CREATE INDEX patient_id_idx IF NOT EXISTS FOR (p:Patient) ON (p.patientId)',
    'CREATE INDEX glucose_timestamp_idx IF NOT EXISTS FOR (g:GlucoseReading) ON (g.timestamp)',
    'CREATE INDEX ecg_timestamp_idx IF NOT EXISTS FOR (e:ECGReading) ON (e.timestamp)',
    'CREATE INDEX ecg_features_idx IF NOT EXISTS FOR (f:ECGFeatures) ON (f.heart_rate)',
    'CREATE INDEX study_phase_idx IF NOT EXISTS FOR (p:Patient) ON (p.studyPhase)',
  ];

  for (const index of indexes) {
    try {
      await session.run(index);
    } catch (error) {
      console.log(`Index creation note: ${error.message}`);
    }
  }

  console.log('Schema indexes created.');
}

async function createD1NAMOPatients(session) {
  console.log('Creating D1NAMO research patients...');

  for (const patient of d1namoPatients) {
    await session.run(`
      CREATE (p:Patient:D1NAMOSubject {
        patientId: $patientId,
        name: $name,
        age: $age,
        gender: $gender,
        condition: $condition,
        diagnosis_date: $diagnosis_date,
        insurance: $insurance,
        phone: $phone,
        email: $email,
        diabetesDuration: $diabetesDuration,
        cgmModel: $cgmModel,
        ecgDevice: $ecgDevice,
        studyPhase: $studyPhase,
        baselineHbA1c: $baselineHbA1c,
        enrolled_date: datetime(),
        created_at: datetime()
      })
    `, patient);
  }

  console.log(`Created ${d1namoPatients.length} D1NAMO research patients.`);
}

async function createSynchronizedReadings(session) {
  console.log('Creating synchronized ECG and glucose readings...');

  for (const patient of d1namoPatients) {
    console.log(`Generating synchronized data for ${patient.name}...`);
    const readings = generateSynchronizedData(patient.patientId, 7); // 7 days of data

    let batchCount = 0;
    const batchSize = 100;

    for (let i = 0; i < readings.length; i += batchSize) {
      const batch = readings.slice(i, i + batchSize);

      // Create glucose readings
      for (const reading of batch) {
        await session.run(`
          MATCH (p:Patient {patientId: $patientId})
          CREATE (g:GlucoseReading:D1NAMOReading {
            glucose: $glucose,
            timestamp: $timestamp,
            readingType: $readingType,
            device: $device,
            created_at: datetime()
          })
          CREATE (p)-[:HAD_READING]->(g)
        `, {
          patientId: reading.ecg.patientId,
          glucose: reading.glucose.glucose,
          timestamp: reading.glucose.timestamp,
          readingType: reading.glucose.readingType,
          device: reading.glucose.device
        });

        // Create ECG reading
        await session.run(`
          MATCH (p:Patient {patientId: $patientId})
          MATCH (g:GlucoseReading {timestamp: $timestamp, patientId: $patientId})
          CREATE (e:ECGReading:D1NAMOReading {
            timestamp: $ecgTimestamp,
            sampling_rate: $sampling_rate,
            duration: $duration,
            device_id: $device_id,
            signal_quality: $signal_quality,
            glucose_level: $glucose_level,
            analysis_timestamp: $analysis_timestamp,
            created_at: datetime()
          })
          CREATE (p)-[:HAD_ECG]->(e)
          CREATE (e)-[:SYNCHRONIZED_WITH]->(g)
        `, {
          patientId: reading.ecg.patientId,
          timestamp: reading.glucose.timestamp,
          ecgTimestamp: reading.ecg.timestamp,
          sampling_rate: reading.ecg.sampling_rate,
          duration: reading.ecg.duration,
          device_id: reading.ecg.device_id,
          signal_quality: reading.ecg.signal_quality,
          glucose_level: reading.ecg.glucoseLevel,
          analysis_timestamp: reading.ecg.analysis_timestamp
        });

        // Create ECG features
        await session.run(`
          MATCH (e:ECGReading {timestamp: $timestamp})
          CREATE (f:ECGFeatures {
            heart_rate: $heart_rate,
            rr_interval: $rr_interval,
            pr_interval: $pr_interval,
            qrs_duration: $qrs_duration,
            qt_interval: $qt_interval,
            qtc_interval: $qtc_interval,
            p_wave_amplitude: $p_wave_amplitude,
            q_wave_amplitude: $q_wave_amplitude,
            r_wave_amplitude: $r_wave_amplitude,
            s_wave_amplitude: $s_wave_amplitude,
            t_wave_amplitude: $t_wave_amplitude,
            st_elevation: $st_elevation,
            st_depression: $st_depression,
            heart_rate_variability: $heart_rate_variability,
            ectopic_beats: $ectopic_beats,
            created_at: datetime()
          })
          CREATE (e)-[:HAS_FEATURES]->(f)
        `, {
          timestamp: reading.ecg.timestamp,
          ...reading.ecg.features
        });

        // Create ECG leads (store lead data)
        for (const [leadName, samples] of Object.entries(reading.ecg.lead_data)) {
          await session.run(`
            MATCH (e:ECGReading {timestamp: $timestamp})
            CREATE (l:ECGLead {
              lead_name: $lead_name,
              samples: $samples,
              sample_count: $sample_count,
              created_at: datetime()
            })
            CREATE (e)-[:HAS_LEAD]->(l)
          `, {
            timestamp: reading.ecg.timestamp,
            lead_name: leadName,
            samples: samples.slice(0, 100), // Store first 100 samples for demo
            sample_count: samples.length
          });
        }
      }

      batchCount++;
      if (batchCount % 10 === 0) {
        console.log(`Processed ${batchCount * batchSize} readings for ${patient.name}...`);
      }
    }

    console.log(`Completed synchronized data for ${patient.name}: ${readings.length} readings`);
  }
}

async function createD1NAMOStudyMetadata(session) {
  console.log('Creating D1NAMO study metadata...');

  await session.run(`
    CREATE (s:Study:D1NAMOStudy {
      studyId: 'D1NAMO-2024',
      title: 'D1NAMO: Diabetes and ECG Monitoring Study',
      description: 'Synchronized ECG and continuous glucose monitoring in Type 1 diabetes patients',
      principal_investigator: 'Dr. Sarah Wilson',
      institution: 'Metro General Hospital Research Center',
      start_date: '2024-01-01',
      expected_end_date: '2024-12-31',
      total_subjects: $total_subjects,
      data_collection_frequency: 'Every 15 minutes',
      ecg_sampling_rate: 1000,
      cgm_model: 'FreeStyle Libre / Dexcom G6',
      study_phase: 'Active Data Collection',
      ethics_approval: 'IRB-2023-D1NAMO-001',
      created_at: datetime()
    })
  `, { total_subjects: d1namoPatients.length });

  // Link patients to study
  for (const patient of d1namoPatients) {
    await session.run(`
      MATCH (p:Patient {patientId: $patientId})
      MATCH (s:Study {studyId: 'D1NAMO-2024'})
      CREATE (p)-[:ENROLLED_IN {
        enrollment_date: $enrollment_date,
        phase: $phase,
        consent_signed: true
      }]->(s)
    `, {
      patientId: patient.patientId,
      enrollment_date: patient.diagnosis_date,
      phase: patient.studyPhase
    });
  }

  console.log('D1NAMO study metadata created.');
}

// Main function
async function populateD1NAMODatabase() {
  const session = driver.session();

  try {
    console.log('Starting D1NAMO database integration...');
    console.log('Connected to Neo4j at:', uri);

    await createD1NAMOSchema(session);
    await createD1NAMOPatients(session);
    await createSynchronizedReadings(session);
    await createD1NAMOStudyMetadata(session);

    console.log('✅ D1NAMO database integration completed successfully!');
    console.log('Summary:');
    console.log(`- ${d1namoPatients.length} D1NAMO research patients created`);
    console.log('- ~672 synchronized ECG-glucose readings per patient (7 days, 15-minute intervals)');
    console.log('- ECG features extracted for each reading');
    console.log('- 12-lead ECG waveform data stored');
    console.log('- Study metadata and patient enrollment records');
    console.log('- Synchronized glucose and ECG relationships');

  } catch (error) {
    console.error('Error in D1NAMO database integration:', error);
  } finally {
    await session.close();
  }
}

// Export functions
export {
  ECG_FEATURES, ECG_LEADS, d1namoPatients,
  generateSynchronizedData, populateD1NAMODatabase
};

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  populateD1NAMODatabase()
    .then(() => {
      console.log('D1NAMO integration script completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    })
    .finally(() => {
      driver.close();
    });
}
