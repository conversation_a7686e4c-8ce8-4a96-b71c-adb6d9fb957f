import { useEffect, useState } from 'react';
import AIRecommendationService from '../services/aiService';
import neo4jService from '../services/neo4jService';
import { calculateAGPData } from '../utils/agpCalculation';
import { generateAGPReport } from '../utils/agpReporting';
import AGPChart from './AGPChart';
import AGPStatistics from './AGPStatistics';
import AIControlPanel from './AIControlPanel';
import './PatientDashboard.css';

const PatientDashboard = () => {
    const [patients, setPatients] = useState([]);
    const [selectedPatient, setSelectedPatient] = useState(null);
    const [patientData, setPatientData] = useState({});
    const [agpData, setAgpData] = useState(null);
    const [aiRecommendations, setAiRecommendations] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [view, setView] = useState('overview'); // overview, analytics, trends, reports
    const [dateRange, setDateRange] = useState('all'); // all, or number of days
    const [comparisonMode, setComparisonMode] = useState(false);
    const [selectedPatientsForComparison, setSelectedPatientsForComparison] = useState([]);

    const aiService = new AIRecommendationService();

    useEffect(() => {
        loadPatients();
    }, []);

    useEffect(() => {
        if (selectedPatient) {
            loadPatientData(selectedPatient.patientId);
        }
    }, [selectedPatient, dateRange]);

    const loadPatients = async () => {
        try {
            setLoading(true);
            await neo4jService.connect();

            const query = `
        MATCH (p:Patient)
        OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
        WITH p, count(g) as totalReadings, max(g.timestamp) as lastReading, min(g.timestamp) as firstReading
        RETURN
          p.patientId as patientId,
          p.name as name,
          p.condition as condition,
          p.age as age,
          p.gender as gender,
          p.insurance as insurance,
          totalReadings,
          lastReading,
          firstReading
        ORDER BY lastReading DESC, p.name ASC
      `;

            const results = await neo4jService.runQuery(query, {});
            setPatients(results);

            if (results.length > 0 && !selectedPatient) {
                setSelectedPatient(results[0]);
            }
        } catch (err) {
            setError('Failed to load patients: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    const loadPatientData = async (patientId) => {
        try {
            setLoading(true);
            setError(null);

            // First, get patient's data range
            const dataRangeQuery = `
        MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
        RETURN min(g.timestamp) as firstReading, max(g.timestamp) as lastReading
      `;

            const dataRangeResult = await neo4jService.runQuery(dataRangeQuery, { patientId });
            const dataRange = dataRangeResult[0];

            // Calculate the cutoff date relative to the patient's last reading
            let cutoffDate;
            if (dataRange?.lastReading && dateRange !== 'all') {
                const lastReadingDate = new Date(dataRange.lastReading);
                const daysBack = parseInt(dateRange);
                cutoffDate = new Date(lastReadingDate);
                cutoffDate.setDate(cutoffDate.getDate() - daysBack);
            }

            // Load glucose data
            const glucoseQuery = cutoffDate ? `
        MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
        WHERE g.timestamp >= datetime($cutoffDate)
        RETURN
          g.timestamp as timestamp,
          g.glucose as glucose,
          g.readingType as readingType
        ORDER BY g.timestamp ASC
      ` : `
        MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
        RETURN
          g.timestamp as timestamp,
          g.glucose as glucose,
          g.readingType as readingType
        ORDER BY g.timestamp ASC
      `;

            const glucoseData = await neo4jService.runQuery(glucoseQuery, {
                patientId,
                cutoffDate: cutoffDate?.toISOString()
            });

            // Load patient details
            const patientQuery = cutoffDate ? `
        MATCH (p:Patient {patientId: $patientId})
        OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
        WHERE g.timestamp >= datetime($cutoffDate)
        OPTIONAL MATCH (p)-[:HAS_CONDITION]->(c:Condition)
        OPTIONAL MATCH (p)-[:TAKES_MEDICATION]->(m:Medication)
        WITH p,
             collect(DISTINCT c) as conditions,
             collect(DISTINCT m) as medications,
             count(g) as totalReadings,
             avg(g.glucose) as avgGlucose,
             min(g.glucose) as minGlucose,
             max(g.glucose) as maxGlucose
        RETURN
          p.patientId as patientId,
          p.name as name,
          p.condition as primaryCondition,
          p.age as age,
          p.gender as gender,
          p.insurance as insurance,
          p.phoneNumber as phoneNumber,
          p.email as email,
          p.emergencyContact as emergencyContact,
          conditions,
          medications,
          totalReadings,
          avgGlucose,
          minGlucose,
          maxGlucose
      ` : `
        MATCH (p:Patient {patientId: $patientId})
        OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
        OPTIONAL MATCH (p)-[:HAS_CONDITION]->(c:Condition)
        OPTIONAL MATCH (p)-[:TAKES_MEDICATION]->(m:Medication)
        WITH p,
             collect(DISTINCT c) as conditions,
             collect(DISTINCT m) as medications,
             count(g) as totalReadings,
             avg(g.glucose) as avgGlucose,
             min(g.glucose) as minGlucose,
             max(g.glucose) as maxGlucose
        RETURN
          p.patientId as patientId,
          p.name as name,
          p.condition as primaryCondition,
          p.age as age,
          p.gender as gender,
          p.insurance as insurance,
          p.phoneNumber as phoneNumber,
          p.email as email,
          p.emergencyContact as emergencyContact,
          conditions,
          medications,
          totalReadings,
          avgGlucose,
          minGlucose,
          maxGlucose
      `;

            const patientDetails = await neo4jService.runQuery(patientQuery, {
                patientId,
                cutoffDate: cutoffDate?.toISOString()
            });

            // Calculate AGP data
            const calculatedAgpData = calculateAGPData(glucoseData);
            setAgpData(calculatedAgpData);

            // Generate AI recommendations
            if (calculatedAgpData) {
                const recommendations = await aiService.generateAGPRecommendations(
                    calculatedAgpData,
                    patientDetails[0] || {}
                );
                setAiRecommendations(recommendations);
            }

            setPatientData({
                glucose: glucoseData,
                details: patientDetails[0] || {},
                agp: calculatedAgpData
            });

        } catch (err) {
            setError('Failed to load patient data: ' + err.message);
        } finally {
            setLoading(false);
        }
    };

    const handlePatientSelect = (patient) => {
        setSelectedPatient(patient);
        setComparisonMode(false);
        setSelectedPatientsForComparison([]);
    };

    const toggleComparisonMode = () => {
        setComparisonMode(!comparisonMode);
        if (!comparisonMode) {
            setSelectedPatientsForComparison([]);
        }
    };

    const togglePatientForComparison = (patient) => {
        if (selectedPatientsForComparison.find(p => p.patientId === patient.patientId)) {
            setSelectedPatientsForComparison(
                selectedPatientsForComparison.filter(p => p.patientId !== patient.patientId)
            );
        } else if (selectedPatientsForComparison.length < 4) {
            setSelectedPatientsForComparison([...selectedPatientsForComparison, patient]);
        }
    };

    const generateReport = async () => {
        if (!selectedPatient || !agpData) return;

        try {
            const report = await generateAGPReport(agpData, patientData.details, aiRecommendations);
            // Trigger download or display
            console.log('Report generated:', report);
        } catch (err) {
            setError('Failed to generate report: ' + err.message);
        }
    };

    const renderPatientList = () => (
        <div className="patient-list">
            <div className="patient-list-header">
                <h3>Patients ({patients.length})</h3>
                <div className="patient-controls">
                    <button
                        className={`comparison-toggle ${comparisonMode ? 'active' : ''}`}
                        onClick={toggleComparisonMode}
                    >
                        {comparisonMode ? '❌ Cancel Compare' : '📊 Compare Patients'}
                    </button>
                    {comparisonMode && (
                        <span className="comparison-count">
                            {selectedPatientsForComparison.length}/4 selected
                        </span>
                    )}
                </div>
            </div>

            <div className="patient-search">
                <input
                    type="text"
                    placeholder="Search patients..."
                    className="search-input"
                />
            </div>

            <div className="patients-grid">
                {patients.map(patient => (
                    <div
                        key={patient.patientId}
                        className={`patient-card ${selectedPatient?.patientId === patient.patientId ? 'selected' : ''
                            } ${comparisonMode && selectedPatientsForComparison.find(p => p.patientId === patient.patientId)
                                ? 'comparison-selected' : ''
                            }`}
                        onClick={() =>
                            comparisonMode
                                ? togglePatientForComparison(patient)
                                : handlePatientSelect(patient)
                        }
                    >
                        <div className="patient-info">
                            <h4>{patient?.name || 'Unknown Patient'}</h4>
                            <p className="condition">{patient?.condition || 'No condition'}</p>
                            <div className="patient-meta">
                                <span>Age: {patient?.age || 'Unknown'}</span>
                                <span>Gender: {patient?.gender || 'Unknown'}</span>
                            </div>
                            <div className="patient-stats">
                                <span className="readings-count">
                                    📊 {patient?.totalReadings || 0} readings (total)
                                </span>
                                {patient?.lastReading && (
                                    <span className="last-reading">
                                        ⏱️ Last: {new Date(patient.lastReading).toLocaleDateString()}
                                    </span>
                                )}
                                {patient?.firstReading && patient?.totalReadings > 0 && (
                                    <span className="date-range">
                                        📅 Range: {new Date(patient.firstReading).toLocaleDateString()} - {new Date(patient.lastReading).toLocaleDateString()}
                                    </span>
                                )}
                            </div>
                        </div>

                        {comparisonMode && (
                            <div className="comparison-checkbox">
                                <input
                                    type="checkbox"
                                    checked={selectedPatientsForComparison.find(p => p.patientId === patient.patientId) !== undefined}
                                    readOnly
                                />
                            </div>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );

    const renderPatientOverview = () => {
        if (!patientData.details) {
            return <div className="loading">Loading patient data...</div>;
        }

        return (
            <div className="patient-overview">
                <div className="overview-header">
                    <div className="patient-details">
                        <h2>{patientData.details.name || 'Unknown Patient'}</h2>
                        <div className="patient-badges">
                            <span className="condition-badge">{patientData.details.primaryCondition || 'No condition'}</span>
                            <span className="age-badge">Age {patientData.details.age || 'Unknown'}</span>
                            <span className="gender-badge">{patientData.details.gender || 'Unknown'}</span>
                        </div>
                    </div>

                    <div className="overview-controls">
                        <select
                            value={dateRange}
                            onChange={(e) => setDateRange(e.target.value)}
                            className="date-range-select"
                        >
                            <option value="7">Last 7 days of data</option>
                            <option value="14">Last 14 days of data</option>
                            <option value="30">Last 30 days of data</option>
                            <option value="90">Last 90 days of data</option>
                            <option value="180">Last 6 months of data</option>
                            <option value="365">Last year of data</option>
                            <option value="all">All available data</option>
                        </select>

                        <button onClick={generateReport} className="generate-report-btn">
                            📄 Generate Report
                        </button>
                    </div>
                </div>

                <div className="overview-stats">
                    <div className="stat-card">
                        <h4>Total Readings</h4>
                        <div className="stat-value">{patientData.details.totalReadings || 0}</div>
                    </div>
                    <div className="stat-card">
                        <h4>Average Glucose</h4>
                        <div className="stat-value">
                            {patientData.details.avgGlucose
                                ? Math.round(patientData.details.avgGlucose) + ' mg/dL'
                                : 'N/A'
                            }
                        </div>
                    </div>
                    <div className="stat-card">
                        <h4>Range</h4>
                        <div className="stat-value">
                            {patientData.details.minGlucose && patientData.details.maxGlucose
                                ? `${Math.round(patientData.details.minGlucose)}-${Math.round(patientData.details.maxGlucose)} mg/dL`
                                : 'N/A'
                            }
                        </div>
                    </div>
                    {agpData?.timeInRange?.targetRangePercentage && (
                        <div className="stat-card highlight">
                            <h4>Time in Range</h4>
                            <div className="stat-value">
                                {Math.round(agpData.timeInRange.targetRangePercentage)}%
                            </div>
                        </div>
                    )}
                </div>

                <div className="contact-info">
                    <h4>Contact Information</h4>
                    <div className="contact-grid">
                        <div>📞 {patientData.details.phoneNumber || 'N/A'}</div>
                        <div>✉️ {patientData.details.email || 'N/A'}</div>
                        <div>🏥 {patientData.details.insurance || 'N/A'}</div>
                        <div>🚨 {patientData.details.emergencyContact || 'N/A'}</div>
                    </div>
                </div>

                {patientData.details.conditions && patientData.details.conditions.length > 0 && (
                    <div className="conditions-section">
                        <h4>Medical Conditions</h4>
                        <div className="conditions-list">
                            {patientData.details.conditions.map((condition, index) => (
                                <span key={index} className="condition-tag">
                                    {condition.name || condition}
                                </span>
                            ))}
                        </div>
                    </div>
                )}

                {patientData.details.medications && patientData.details.medications.length > 0 && (
                    <div className="medications-section">
                        <h4>Current Medications</h4>
                        <div className="medications-list">
                            {patientData.details.medications.map((medication, index) => (
                                <div key={index} className="medication-item">
                                    <span className="medication-name">{medication.name || medication}</span>
                                    {medication.dosage && <span className="medication-dosage">{medication.dosage}</span>}
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderAnalyticsView = () => (
        <div className="analytics-view">
            {!patientData.glucose || patientData.glucose.length === 0 ? (
                <div className="no-data-message">
                    <h3>📊 No Analytics Data Available</h3>
                    <p>This patient doesn't have enough glucose readings to generate analytics.</p>
                    <p>Analytics require glucose measurements to calculate AGP (Ambulatory Glucose Profile) and other metrics.</p>
                </div>
            ) : (
                <div className="analytics-grid">
                    {agpData && (
                        <>
                            <div className="chart-section">
                                <AGPChart data={agpData} />
                            </div>
                            <div className="statistics-section">
                                <AGPStatistics data={agpData} />
                            </div>
                        </>
                    )}

                    {!agpData && (
                        <div className="analytics-loading">
                            <h3>Calculating Analytics...</h3>
                            <p>Processing glucose data to generate AGP and statistical insights.</p>
                        </div>
                    )}

                    {aiRecommendations && (
                        <div className="ai-recommendations-section">
                            <AIControlPanel
                                agpData={agpData}
                                patientData={patientData.details}
                                recommendations={aiRecommendations}
                            />
                        </div>
                    )}
                </div>
            )}
        </div>
    );

    const renderReportsView = () => (
        <div className="reports-view">
            <div className="reports-header">
                <h3>📄 Patient Reports</h3>
                <p>Generate and download comprehensive patient reports</p>
            </div>

            {!patientData.details ? (
                <div className="no-data-message">
                    <h3>📄 No Patient Data Available</h3>
                    <p>Please select a patient to generate reports.</p>
                </div>
            ) : (
                <div className="reports-content">
                    <div className="report-options">
                        <div className="report-option">
                            <h4>📊 AGP Report</h4>
                            <p>Comprehensive Ambulatory Glucose Profile with statistics and insights</p>
                            <button
                                onClick={generateReport}
                                disabled={!agpData}
                                className={`generate-btn ${!agpData ? 'disabled' : ''}`}
                            >
                                {agpData ? '📄 Generate AGP Report' : '⏳ Calculating AGP Data...'}
                            </button>
                        </div>

                        <div className="report-option">
                            <h4>📈 Trends Summary</h4>
                            <p>Patient glucose trends and pattern analysis</p>
                            <button
                                onClick={() => downloadTrendsReport()}
                                disabled={!patientData.glucose || patientData.glucose.length === 0}
                                className={`generate-btn ${!patientData.glucose || patientData.glucose.length === 0 ? 'disabled' : ''}`}
                            >
                                {patientData.glucose && patientData.glucose.length > 0 ?
                                    '📊 Generate Trends Report' :
                                    '❌ No Glucose Data Available'
                                }
                            </button>
                        </div>

                        <div className="report-option">
                            <h4>👤 Patient Summary</h4>
                            <p>Complete patient information and contact details</p>
                            <button
                                onClick={() => downloadPatientSummary()}
                                className="generate-btn"
                            >
                                📋 Generate Patient Summary
                            </button>
                        </div>
                    </div>

                    <div className="report-info">
                        <h4>Report Information</h4>
                        <div className="info-grid">
                            <div className="info-item">
                                <span className="info-label">Patient:</span>
                                <span className="info-value">{patientData.details.name || 'Unknown'}</span>
                            </div>
                            <div className="info-item">
                                <span className="info-label">Date Range:</span>
                                <span className="info-value">
                                    {patientData.glucose && patientData.glucose.length > 0 ?
                                        `${new Date(Math.min(...patientData.glucose.map(g => new Date(g.timestamp)))).toLocaleDateString()} - ${new Date(Math.max(...patientData.glucose.map(g => new Date(g.timestamp)))).toLocaleDateString()}`
                                        : 'No glucose data'
                                    }
                                </span>
                            </div>
                            <div className="info-item">
                                <span className="info-label">Data Points:</span>
                                <span className="info-value">{patientData.glucose ? patientData.glucose.length : 0} glucose readings</span>
                            </div>
                            <div className="info-item">
                                <span className="info-label">Report Generated:</span>
                                <span className="info-value">{new Date().toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );

    const downloadTrendsReport = () => {
        if (!patientData.glucose || patientData.glucose.length === 0) return;

        const reportData = {
            patient: patientData.details.name,
            dateRange: `${new Date(Math.min(...patientData.glucose.map(g => new Date(g.timestamp)))).toLocaleDateString()} - ${new Date(Math.max(...patientData.glucose.map(g => new Date(g.timestamp)))).toLocaleDateString()}`,
            totalReadings: patientData.glucose.length,
            averageGlucose: patientData.details.avgGlucose ? Math.round(patientData.details.avgGlucose) : 'N/A',
            minGlucose: patientData.details.minGlucose ? Math.round(patientData.details.minGlucose) : 'N/A',
            maxGlucose: patientData.details.maxGlucose ? Math.round(patientData.details.maxGlucose) : 'N/A'
        };

        const reportText = `GLUCOSE TRENDS REPORT
Patient: ${reportData.patient}
Date Range: ${reportData.dateRange}
Total Readings: ${reportData.totalReadings}
Average Glucose: ${reportData.averageGlucose} mg/dL
Glucose Range: ${reportData.minGlucose} - ${reportData.maxGlucose} mg/dL
Generated: ${new Date().toLocaleString()}`;

        downloadTextFile(`trends-report-${patientData.details.name}-${new Date().getTime()}.txt`, reportText);
    };

    const downloadPatientSummary = () => {
        if (!patientData.details) return;

        const summary = `PATIENT SUMMARY REPORT
Name: ${patientData.details.name || 'Unknown'}
Patient ID: ${patientData.details.patientId || 'Unknown'}
Age: ${patientData.details.age || 'Unknown'}
Gender: ${patientData.details.gender || 'Unknown'}
Condition: ${patientData.details.primaryCondition || 'Unknown'}
Insurance: ${patientData.details.insurance || 'N/A'}
Phone: ${patientData.details.phoneNumber || 'N/A'}
Email: ${patientData.details.email || 'N/A'}
Emergency Contact: ${patientData.details.emergencyContact || 'N/A'}

GLUCOSE DATA SUMMARY:
Total Readings: ${patientData.details.totalReadings || 0}
Average Glucose: ${patientData.details.avgGlucose ? Math.round(patientData.details.avgGlucose) + ' mg/dL' : 'N/A'}
Glucose Range: ${patientData.details.minGlucose && patientData.details.maxGlucose ?
                Math.round(patientData.details.minGlucose) + ' - ' + Math.round(patientData.details.maxGlucose) + ' mg/dL' : 'N/A'}

Generated: ${new Date().toLocaleString()}`;

        downloadTextFile(`patient-summary-${patientData.details.name}-${new Date().getTime()}.txt`, summary);
    };

    const downloadTextFile = (filename, content) => {
        const element = document.createElement('a');
        const file = new Blob([content], { type: 'text/plain' });
        element.href = URL.createObjectURL(file);
        element.download = filename;
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    };

    const renderTrendsView = () => (
        <div className="trends-view">
            {!patientData.glucose || patientData.glucose.length === 0 ? (
                <div className="no-data-message">
                    <h3>📈 No Trends Data Available</h3>
                    <p>This patient doesn't have enough glucose readings to show trends.</p>
                    <p>Trends analysis requires multiple glucose measurements over time.</p>
                </div>
            ) : (
                <div className="trends-content">
                    <div className="trends-header">
                        <h3>📈 Glucose Trends Analysis</h3>
                        <p>Showing patterns and trends in glucose readings over time</p>
                    </div>

                    <div className="trends-grid">
                        <div className="trend-summary">
                            <h4>Key Trends</h4>
                            <div className="trend-stats">
                                <div className="trend-stat">
                                    <span className="trend-label">Total Readings:</span>
                                    <span className="trend-value">{patientData.glucose.length}</span>
                                </div>
                                <div className="trend-stat">
                                    <span className="trend-label">Date Range:</span>
                                    <span className="trend-value">
                                        {patientData.glucose.length > 0 ?
                                            `${new Date(Math.min(...patientData.glucose.map(g => new Date(g.timestamp)))).toLocaleDateString()} - ${new Date(Math.max(...patientData.glucose.map(g => new Date(g.timestamp)))).toLocaleDateString()}`
                                            : 'N/A'
                                        }
                                    </span>
                                </div>
                                <div className="trend-stat">
                                    <span className="trend-label">Average Glucose:</span>
                                    <span className="trend-value">
                                        {patientData.details.avgGlucose ?
                                            `${Math.round(patientData.details.avgGlucose)} mg/dL`
                                            : 'N/A'
                                        }
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div className="trend-insights">
                            <h4>Pattern Analysis</h4>
                            <div className="insights-list">
                                <div className="insight-item">
                                    📊 Data collection spans {patientData.glucose.length > 0 ?
                                        Math.ceil((new Date(Math.max(...patientData.glucose.map(g => new Date(g.timestamp)))) -
                                            new Date(Math.min(...patientData.glucose.map(g => new Date(g.timestamp))))) / (1000 * 60 * 60 * 24))
                                        : 0} days
                                </div>
                                <div className="insight-item">
                                    ⏱️ Average readings per day: {patientData.glucose.length > 0 ?
                                        (patientData.glucose.length / Math.max(1, Math.ceil((new Date(Math.max(...patientData.glucose.map(g => new Date(g.timestamp)))) -
                                            new Date(Math.min(...patientData.glucose.map(g => new Date(g.timestamp))))) / (1000 * 60 * 60 * 24)))).toFixed(1)
                                        : 0}
                                </div>
                                {patientData.details.minGlucose && patientData.details.maxGlucose && (
                                    <div className="insight-item">
                                        📈 Glucose range: {Math.round(patientData.details.minGlucose)} - {Math.round(patientData.details.maxGlucose)} mg/dL
                                        (variability: {Math.round(patientData.details.maxGlucose - patientData.details.minGlucose)} mg/dL)
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="trends-note">
                        <p>💡 <strong>Note:</strong> Advanced trend visualization and statistical analysis will be available when the full AGP analytics are loaded.</p>
                    </div>
                </div>
            )}
        </div>
    );

    const renderViewTabs = () => (
        <div className="view-tabs">
            <button
                className={view === 'overview' ? 'active' : ''}
                onClick={() => setView('overview')}
            >
                📋 Overview
            </button>
            <button
                className={view === 'analytics' ? 'active' : ''}
                onClick={() => setView('analytics')}
            >
                📊 Analytics
            </button>
            <button
                className={view === 'trends' ? 'active' : ''}
                onClick={() => setView('trends')}
            >
                📈 Trends
            </button>
            <button
                className={view === 'reports' ? 'active' : ''}
                onClick={() => setView('reports')}
            >
                📄 Reports
            </button>
        </div>
    );

    if (loading && patients.length === 0) {
        return (
            <div className="loading-container">
                <div className="loading-spinner">⏳</div>
                <p>Loading patients...</p>
            </div>
        );
    }

    return (
        <div className="patient-dashboard">
            <div className="dashboard-header">
                <h1>Patient Dashboard</h1>
                <div className="dashboard-controls">
                    <button onClick={loadPatients} className="refresh-btn">
                        🔄 Refresh
                    </button>
                </div>
            </div>

            {error && (
                <div className="error-banner">
                    ❌ {error}
                    <button onClick={() => setError(null)} className="close-error">✖️</button>
                </div>
            )}

            <div className="dashboard-layout">
                <div className="sidebar">
                    {renderPatientList()}
                </div>

                <div className="main-content">
                    {selectedPatient ? (
                        <>
                            {renderViewTabs()}

                            <div className="view-content">
                                {loading && (
                                    <div className="loading-overlay">
                                        <div className="loading-spinner">⏳</div>
                                        <p>Loading patient data...</p>
                                    </div>
                                )}

                                {view === 'overview' && renderPatientOverview()}
                                {view === 'analytics' && renderAnalyticsView()}
                                {view === 'trends' && renderTrendsView()}
                                {view === 'reports' && renderReportsView()}
                            </div>
                        </>
                    ) : (
                        <div className="no-patient-selected">
                            <h3>Select a patient to view details</h3>
                            <p>Choose a patient from the sidebar to view their healthcare data and analytics.</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PatientDashboard;
