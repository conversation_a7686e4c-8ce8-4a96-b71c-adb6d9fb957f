import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

async function fixPatientConnections() {
  let driver = null;

  try {
    console.log('🔧 Fixing patient connections for ECG and glucose data...');

    driver = neo4j.driver(
      process.env.VITE_NEO4J_URI,
      neo4j.auth.basic(process.env.VITE_NEO4J_USERNAME, process.env.VITE_NEO4J_PASSWORD)
    );

    const session = driver.session();

    try {
      // First, let's clean up any disconnected ECG readings and features
      console.log('🧹 Cleaning up disconnected ECG data...');

      const cleanupResult = await session.run(`
        MATCH (e:ECGReading)
        WHERE NOT exists((e)<-[:HAD_ECG]-(:Patient))
        OPTIONAL MATCH (e)-[:HAS_FEATURES]->(f:ECGFeatures)
        DELETE e, f
        RETURN count(e) as deletedECG, count(f) as deletedFeatures
      `);

      const deletedECG = cleanupResult.records[0]?.get('deletedECG').toNumber() || 0;
      const deletedFeatures = cleanupResult.records[0]?.get('deletedFeatures').toNumber() || 0;
      console.log(`   Deleted ${deletedECG} disconnected ECG readings and ${deletedFeatures} features`);

      // Now let's create fresh ECG data for D1NAMO patients with realistic values
      console.log('💓 Creating ECG data for D1NAMO patients...');

      const d1namoPatients = ['001', '002', '003'];
      let totalECGCreated = 0;

      for (const patientId of d1namoPatients) {
        console.log(`\n   Processing Patient ${patientId}...`);

        // Create 20-30 ECG readings per patient over several days
        const readingsPerPatient = Math.floor(Math.random() * 11) + 20; // 20-30 readings

        for (let i = 0; i < readingsPerPatient; i++) {
          // Generate timestamps over the past 7 days
          const daysAgo = Math.floor(Math.random() * 7);
          const hoursAgo = Math.floor(Math.random() * 24);
          const minutesAgo = Math.floor(Math.random() * 60);

          const timestamp = new Date();
          timestamp.setDate(timestamp.getDate() - daysAgo);
          timestamp.setHours(timestamp.getHours() - hoursAgo);
          timestamp.setMinutes(timestamp.getMinutes() - minutesAgo);

          // Generate realistic ECG features
          const baseHeartRate = 60 + Math.floor(Math.random() * 40); // 60-100 bpm
          const heartRate = Math.max(50, Math.min(120, baseHeartRate + (Math.random() - 0.5) * 20));

          const baseHRV = 25 + Math.floor(Math.random() * 30); // 25-55 ms
          const hrv = Math.max(10, Math.min(80, baseHRV + (Math.random() - 0.5) * 15));

          const qtc = 380 + Math.floor(Math.random() * 60); // 380-440 ms
          const signalQuality = Math.random() > 0.2 ? 'Good' : 'Fair';

          try {
            await session.run(`
              MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
              CREATE (e:ECGReading:D1NAMOReading {
                readingId: $readingId,
                timestamp: datetime($timestamp),
                duration: 10.0,
                samplingRate: 250,
                sampleCount: 2500,
                sessionId: $sessionId,
                signalQuality: $signalQuality
              })
              CREATE (f:ECGFeatures {
                heartRate: $heartRate,
                hrv_rmssd: $hrv,
                meanAmplitude: $meanAmplitude,
                stdDevAmplitude: $stdDevAmplitude,
                qtc_interval: $qtc,
                peakCount: $peakCount,
                signalQuality: $signalQuality,
                samplesCount: 2500
              })
              CREATE (p)-[:HAD_ECG]->(e)
              CREATE (e)-[:HAS_FEATURES]->(f)
            `, {
              patientId,
              readingId: `ECG_${patientId}_${timestamp.getTime()}_${i}`,
              timestamp: timestamp.toISOString(),
              sessionId: `session_${Math.floor(timestamp.getTime() / 86400000)}`,
              signalQuality,
              heartRate: Math.round(heartRate),
              hrv: Math.round(hrv),
              meanAmplitude: Math.round((Math.random() * 200 + 100) * 100) / 100,
              stdDevAmplitude: Math.round((Math.random() * 50 + 10) * 100) / 100,
              qtc: Math.round(qtc),
              peakCount: Math.floor(heartRate * 10 / 60) // Approximate peaks in 10 seconds
            });

            totalECGCreated++;
          } catch (error) {
            console.warn(`   Warning: Could not create ECG reading for ${patientId}: ${error.message}`);
          }
        }

        console.log(`   ✅ Created ${readingsPerPatient} ECG readings for Patient ${patientId}`);
      }

      // Now create correlated glucose readings
      console.log('\n🍯 Creating glucose data for D1NAMO patients...');

      let totalGlucoseCreated = 0;

      for (const patientId of d1namoPatients) {
        // Get ECG timestamps for this patient
        const ecgResult = await session.run(`
          MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})-[:HAD_ECG]->(e:ECGReading)
          RETURN e.timestamp as timestamp
          ORDER BY e.timestamp
        `, { patientId });

        const ecgTimestamps = ecgResult.records.map(r => new Date(r.get('timestamp')));

        // Create glucose readings around ECG times (every 3rd ECG reading approximately)
        const glucoseTimestamps = ecgTimestamps.filter((_, index) => index % 3 === 0);

        for (const ecgTime of glucoseTimestamps) {
          // Create glucose reading within ±15 minutes of ECG
          const glucoseTime = new Date(ecgTime.getTime() + (Math.random() - 0.5) * 30 * 60 * 1000);

          // Generate realistic glucose based on time of day and patient type
          const hour = glucoseTime.getHours();
          let baseGlucose = 120;

          // Simulate diabetes patterns
          if (patientId === '003') { // Type 1 diabetes - more variable
            baseGlucose = 110 + (Math.random() - 0.5) * 100;
          } else { // Type 2 diabetes - higher baseline
            baseGlucose = 140 + (Math.random() - 0.5) * 80;
          }

          // Time-based adjustments
          if (hour >= 6 && hour <= 9) baseGlucose += 30; // Morning
          else if (hour >= 11 && hour <= 14) baseGlucose += 40; // Lunch
          else if (hour >= 17 && hour <= 20) baseGlucose += 35; // Dinner
          else if (hour >= 22 || hour <= 5) baseGlucose -= 15; // Night

          const glucoseValue = Math.max(60, Math.min(300, Math.round(baseGlucose)));

          const readingType = hour >= 6 && hour <= 9 ? 'Fasting' :
            hour >= 11 && hour <= 14 ? 'Post-Lunch' :
              hour >= 17 && hour <= 20 ? 'Post-Dinner' : 'CGM';

          const trend = glucoseValue > 150 ? 'rising' : glucoseValue < 80 ? 'falling' : 'stable';

          try {
            await session.run(`
              MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
              CREATE (g:GlucoseReading:D1NAMOReading {
                readingId: $readingId,
                timestamp: datetime($timestamp),
                value: $value,
                unit: 'mg/dL',
                readingType: $readingType,
                trend: $trend,
                source: 'Simulated CGM'
              })
              CREATE (p)-[:HAD_READING]->(g)
            `, {
              patientId,
              readingId: `GLU_${patientId}_${glucoseTime.getTime()}`,
              timestamp: glucoseTime.toISOString(),
              value: glucoseValue,
              readingType,
              trend
            });

            totalGlucoseCreated++;
          } catch (error) {
            console.warn(`   Warning: Could not create glucose reading: ${error.message}`);
          }
        }

        console.log(`   ✅ Created glucose readings for Patient ${patientId}`);
      }

      console.log(`\n🎉 Data creation complete!`);
      console.log(`   💓 ECG Readings: ${totalECGCreated}`);
      console.log(`   🍯 Glucose Readings: ${totalGlucoseCreated}`);

      return {
        ecgCreated: totalECGCreated,
        glucoseCreated: totalGlucoseCreated
      };

    } finally {
      await session.close();
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    if (driver) {
      await driver.close();
    }
  }
}

// Run the fix
fixPatientConnections()
  .then(result => {
    console.log('\n✅ Patient connections fixed successfully:', result);
  })
  .catch(error => {
    console.error('💥 Failed to fix connections:', error);
  });
