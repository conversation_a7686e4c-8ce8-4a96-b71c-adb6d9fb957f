/* Advanced AGP Analytics Styles */
.advanced-agp-analytics {
  padding: 1.5rem;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  min-height: 100vh;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.analytics-header h2 {
  color: var(--gb-accent2);
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
}

.analytics-controls {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.mode-selector,
.timeframe-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mode-selector label,
.timeframe-selector label {
  color: #a89984;
  font-size: 0.875rem;
  font-weight: 500;
}

.mode-selector select,
.timeframe-selector select {
  background: var(--gb-bg1);
  color: var(--gb-fg);
  border: 1px solid var(--gb-accent);
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* Advanced Metrics */
.advanced-metrics {
  margin: 2rem 0;
}

.advanced-metrics h3 {
  color: var(--gb-accent2);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg0);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.metric-card:hover {
  border-color: var(--gb-accent);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.metric-card h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Risk Assessment */
.risk-indicators {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.risk-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--gb-bg0);
  border-radius: 6px;
  border-left: 4px solid;
}

.risk-indicator.low {
  border-left-color: #b8bb26;
  background: rgba(184, 187, 38, 0.1);
}

.risk-indicator.moderate {
  border-left-color: #fabd2f;
  background: rgba(250, 189, 47, 0.1);
}

.risk-indicator.high {
  border-left-color: #fb4934;
  background: rgba(251, 73, 52, 0.1);
}

.risk-label {
  font-weight: 500;
  color: var(--gb-fg);
}

.risk-value {
  text-transform: capitalize;
  font-weight: 600;
}

.risk-score {
  font-family: monospace;
  background: rgba(235, 219, 178, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* Variability Profile */
.variability-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.variability-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gb-bg0);
}

.variability-item:last-child {
  border-bottom: none;
}

.cv-value.optimal {
  color: #b8bb26;
  font-weight: 600;
}

.cv-value.acceptable {
  color: #fabd2f;
  font-weight: 600;
}

.cv-value.high {
  color: #fb4934;
  font-weight: 600;
}

/* TIR Detailed */
.tir-grade-container {
  text-align: center;
}

.tir-grade {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 50%;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}

.tir-grade.grade-a {
  background: rgba(184, 187, 38, 0.2);
  color: #b8bb26;
  border: 3px solid #b8bb26;
}

.tir-grade.grade-b {
  background: rgba(250, 189, 47, 0.2);
  color: #fabd2f;
  border: 3px solid #fabd2f;
}

.tir-grade.grade-c {
  background: rgba(254, 128, 25, 0.2);
  color: #fe8019;
  border: 3px solid #fe8019;
}

.tir-grade.grade-d {
  background: rgba(251, 73, 52, 0.2);
  color: #fb4934;
  border: 3px solid #fb4934;
}

.tir-improvement {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.optimal-indicator {
  color: #b8bb26;
  font-weight: 600;
  font-size: 1rem;
}

/* Clinical Profile */
.clinical-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.clinical-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gb-bg0);
}

.clinical-item:last-child {
  border-bottom: none;
}

.diabetes-type {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.risk-score-value.low {
  color: #b8bb26;
  font-weight: 600;
}

.risk-score-value.moderate {
  color: #fabd2f;
  font-weight: 600;
}

.risk-score-value.high {
  color: #fb4934;
  font-weight: 600;
}

.treatment-response {
  text-transform: capitalize;
  font-weight: 500;
}

.treatment-response.excellent {
  color: #b8bb26;
}

.treatment-response.good {
  color: #fabd2f;
}

.treatment-response.fair {
  color: #fe8019;
}

.treatment-response.needs_improvement {
  color: #fb4934;
}

/* Clinical Recommendations */
.clinical-recommendations {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.clinical-recommendations h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  background: var(--gb-bg0);
  border-radius: 6px;
  padding: 1rem;
  border-left: 4px solid;
}

.recommendation-item.priority-high {
  border-left-color: #fb4934;
  background: rgba(251, 73, 52, 0.05);
}

.recommendation-item.priority-medium {
  border-left-color: #fabd2f;
  background: rgba(250, 189, 47, 0.05);
}

.recommendation-item.priority-low {
  border-left-color: #83a598;
  background: rgba(131, 165, 152, 0.05);
}

.recommendation-header {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.priority-badge,
.category-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.high {
  background: #fb4934;
  color: white;
}

.priority-badge.medium {
  background: #fabd2f;
  color: var(--gb-bg0);
}

.priority-badge.low {
  background: #83a598;
  color: white;
}

.category-badge {
  background: var(--gb-bg1);
  color: var(--gb-fg);
  border: 1px solid var(--gb-accent);
}

.recommendation-message {
  color: var(--gb-fg);
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.recommendation-action {
  color: var(--gb-accent);
  margin: 0;
  font-size: 0.875rem;
}

/* Comparison View */
.comparison-view {
  margin-top: 2rem;
}

.comparison-view h3 {
  color: var(--gb-accent2);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.comparison-controls {
  margin-bottom: 1.5rem;
}

.comparison-controls label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  color: #a89984;
  font-size: 0.875rem;
  font-weight: 500;
  max-width: 200px;
}

.comparison-controls select {
  background: var(--gb-bg1);
  color: var(--gb-fg);
  border: 1px solid var(--gb-accent);
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* Comparison Charts */
.comparison-charts.overlay {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
}

.comparison-charts.sidebyside {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

.chart-container {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid transparent;
}

.chart-container.primary {
  border-color: var(--gb-accent2);
}

.chart-container.comparison {
  border-color: #83a598;
}

.chart-container h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  text-align: center;
  font-size: 1.1rem;
}

/* Combined Analysis */
.combined-analysis {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
}

.combined-metrics-table {
  overflow-x: auto;
}

.combined-metrics-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.combined-metrics-table th,
.combined-metrics-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--gb-bg0);
}

.combined-metrics-table th {
  background: var(--gb-bg0);
  color: var(--gb-accent2);
  font-weight: 600;
  position: sticky;
  top: 0;
}

.combined-metrics-table .primary-row {
  background: rgba(250, 189, 47, 0.1);
  font-weight: 600;
}

.combined-metrics-table .comparison-row:hover {
  background: rgba(131, 165, 152, 0.1);
}

.grade-cell {
  text-align: center;
  font-weight: bold;
  font-size: 1rem;
}

/* Trends and Population Analysis */
.trends-analysis,
.population-analysis {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 3rem;
  text-align: center;
  color: #928374;
}

.trends-analysis h3,
.population-analysis h3 {
  color: var(--gb-accent2);
  margin-bottom: 1rem;
  font-size: 1.75rem;
}

/* Single Patient Analysis */
.single-patient-analysis {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.primary-charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
}

@media (max-width: 1024px) {
  .primary-charts {
    grid-template-columns: 1fr;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .advanced-agp-analytics {
    padding: 1rem;
  }

  .analytics-header {
    flex-direction: column;
    align-items: stretch;
  }

  .analytics-controls {
    justify-content: space-between;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .comparison-charts.sidebyside {
    grid-template-columns: 1fr;
  }

  .combined-metrics-table {
    font-size: 0.75rem;
  }

  .combined-metrics-table th,
  .combined-metrics-table td {
    padding: 0.5rem;
  }
}
