import csv from 'csv-parser';
import dotenv from 'dotenv';
import fs from 'fs';
import neo4j from 'neo4j-driver';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

console.log('🔍 Environment loaded, starting script...');
console.log('🔗 Neo4j URI:', process.env.VITE_NEO4J_URI);

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function parseD1NAMOTimestamp(timeString) {
  try {
    // Handle different timestamp formats: "02/10/2014 10:56:44.420"
    const cleaned = timeString.trim().replace(/"/g, '');
    const [datePart, timePart] = cleaned.split(' ');
    const [day, month, year] = datePart.split('/');
    const [hour, minute, secondWithMs] = timePart.split(':');

    // Handle seconds with milliseconds
    const [second, millisecond = '0'] = secondWithMs.split('.');

    // Create Date object with proper European date format (DD/MM/YYYY)
    const date = new Date(
      parseInt(year),
      parseInt(month) - 1, // Month is 0-indexed
      parseInt(day),
      parseInt(hour),
      parseInt(minute),
      parseInt(second),
      parseInt(millisecond.padEnd(3, '0').substring(0, 3)) // Ensure 3 digits for milliseconds
    );

    // Validate the date
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date created from: ${timeString}`);
      return new Date();
    }

    return date;
  } catch (error) {
    console.error('Error parsing timestamp:', timeString, error);
    return new Date();
  }
}

// Neo4j connection setup - Env only
const { VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD } = process.env;
if (!VITE_NEO4J_URI || !VITE_NEO4J_USERNAME || !VITE_NEO4J_PASSWORD) {
  console.error('Missing Neo4j env vars. Set VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD.');
  process.exit(1);
}
const driver = neo4j.driver(
  VITE_NEO4J_URI,
  neo4j.auth.basic(
    VITE_NEO4J_USERNAME,
    VITE_NEO4J_PASSWORD
  )
);

// Dataset configuration
const DATASET_PATH = path.join(__dirname, '..', 'data', 'd1namo', 'diabetes_subset_ecg_data');
const BATCH_SIZE = 1000;
const ECG_SAMPLING_RATE = 250; // Hz - Typical ECG sampling rate

// Patient metadata based on typical D1NAMO structure
const PATIENT_METADATA = {
  '001': { name: 'D1NAMO Subject 001', age: 45, gender: 'F', diabetesDuration: 8, baselineHbA1c: 7.2, condition: 'Type 2 Diabetes' },
  '002': { name: 'D1NAMO Subject 002', age: 52, gender: 'M', diabetesDuration: 12, baselineHbA1c: 8.1, condition: 'Type 2 Diabetes' },
  '003': { name: 'D1NAMO Subject 003', age: 38, gender: 'F', diabetesDuration: 5, baselineHbA1c: 6.8, condition: 'Type 1 Diabetes' },
  '004': { name: 'D1NAMO Subject 004', age: 59, gender: 'M', diabetesDuration: 15, baselineHbA1c: 8.9, condition: 'Type 2 Diabetes' },
  '005': { name: 'D1NAMO Subject 005', age: 41, gender: 'F', diabetesDuration: 7, baselineHbA1c: 7.5, condition: 'Type 2 Diabetes' },
  '006': { name: 'D1NAMO Subject 006', age: 48, gender: 'M', diabetesDuration: 10, baselineHbA1c: 7.8, condition: 'Type 2 Diabetes' },
  '007': { name: 'D1NAMO Subject 007', age: 43, gender: 'F', diabetesDuration: 6, baselineHbA1c: 7.1, condition: 'Type 1 Diabetes' },
  '008': { name: 'D1NAMO Subject 008', age: 55, gender: 'M', diabetesDuration: 13, baselineHbA1c: 8.4, condition: 'Type 2 Diabetes' },
  '009': { name: 'D1NAMO Subject 009', age: 37, gender: 'F', diabetesDuration: 4, baselineHbA1c: 6.9, condition: 'Type 1 Diabetes' }
};

async function clearExistingD1NAMOData() {
  const session = driver.session();
  try {
    console.log('🧹 Clearing existing D1NAMO data...');

    await session.run(`
      MATCH (n:D1NAMOSubject)
      DETACH DELETE n
    `);

    await session.run(`
      MATCH (n:D1NAMOReading)
      DETACH DELETE n
    `);

    await session.run(`
      MATCH (n:ECGFeatures)
      DETACH DELETE n
    `);

    await session.run(`
      MATCH (n:ECGLead)
      DETACH DELETE n
    `);

    console.log('✅ Existing D1NAMO data cleared');
  } catch (error) {
    console.error('❌ Error clearing data:', error);
    throw error;
  } finally {
    await session.close();
  }
}

async function createConstraintsAndIndexes() {
  const session = driver.session();
  try {
    console.log('📊 Creating database constraints and indexes...');

    // Create constraints
    const constraints = [
      'CREATE CONSTRAINT d1namo_patient_id IF NOT EXISTS FOR (p:D1NAMOSubject) REQUIRE p.patientId IS UNIQUE',
      'CREATE INDEX d1namo_reading_timestamp IF NOT EXISTS FOR (r:D1NAMOReading) ON r.timestamp',
      'CREATE INDEX d1namo_patient_lookup IF NOT EXISTS FOR (p:D1NAMOSubject) ON p.patientId',
      'CREATE INDEX ecg_features_hr IF NOT EXISTS FOR (f:ECGFeatures) ON f.heartRate'
    ];

    for (const constraint of constraints) {
      try {
        await session.run(constraint);
      } catch (error) {
        // Constraint may already exist
        if (!error.message.includes('already exists')) {
          console.warn('Warning:', error.message);
        }
      }
    }

    console.log('✅ Constraints and indexes created');
  } catch (error) {
    console.error('❌ Error creating constraints:', error);
    throw error;
  } finally {
    await session.close();
  }
}

async function createPatient(patientId, metadata) {
  const session = driver.session();
  try {
    await session.run(`
      MERGE (p:Patient:D1NAMOSubject {patientId: $patientId})
      SET p.name = $name,
          p.age = $age,
          p.gender = $gender,
          p.condition = $condition,
          p.diabetesDuration = $diabetesDuration,
          p.baselineHbA1c = $baselineHbA1c,
          p.studyPhase = 'D1NAMO_ECG_ONLY',
          p.enrollmentDate = datetime(),
          p.dataType = 'ECG_CONTINUOUS'
    `, {
      patientId,
      name: metadata.name,
      age: metadata.age,
      gender: metadata.gender,
      condition: metadata.condition,
      diabetesDuration: metadata.diabetesDuration,
      baselineHbA1c: metadata.baselineHbA1c
    });

    console.log(`👤 Created patient: ${patientId} - ${metadata.name}`);
  } catch (error) {
    console.error(`❌ Error creating patient ${patientId}:`, error);
    throw error;
  } finally {
    await session.close();
  }
}

function parseECGTimestamp(timeString) {
  try {
    // Handle different timestamp formats: "02/10/2014 10:56:44.420"
    const cleaned = timeString.trim().replace(/"/g, '');
    const [datePart, timePart] = cleaned.split(' ');
    const [day, month, year] = datePart.split('/');
    const [hour, minute, second] = timePart.split(':');

    // Create ISO datetime string
    const isoString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:${second.padStart(2, '0')}.000Z`;
    return new Date(isoString);
  } catch (error) {
    console.error('Error parsing timestamp:', timeString, error);
    return new Date();
  }
}

function calculateECGFeatures(waveformSegment, samplingRate = ECG_SAMPLING_RATE) {
  if (!waveformSegment || waveformSegment.length < 10) {
    return null;
  }

  // Basic feature extraction from waveform
  const values = waveformSegment.map(point => parseFloat(point.value) || 0);

  // Calculate basic statistics
  const mean = values.reduce((a, b) => a + b, 0) / values.length;
  const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);

  // Estimate heart rate from peak detection (simplified)
  const peaks = findPeaks(values);
  const heartRate = peaks.length > 1 ? (peaks.length - 1) * 60 * samplingRate / values.length : 70;

  // Calculate R-R intervals for HRV
  const rrIntervals = calculateRRIntervals(peaks, samplingRate);
  const hrv_rmssd = rrIntervals.length > 1 ? calculateRMSSD(rrIntervals) : 25;

  return {
    heartRate: Math.round(Math.max(40, Math.min(200, heartRate))), // Clamp to reasonable range
    rrInterval: rrIntervals.length > 0 ? Math.round(rrIntervals[rrIntervals.length - 1]) : 800,
    hrv_rmssd: Math.round(hrv_rmssd),
    qrsDuration: 85 + Math.round(Math.random() * 30), // Simulated
    prInterval: 140 + Math.round(Math.random() * 60), // Simulated
    qtInterval: 380 + Math.round(Math.random() * 80), // Simulated
    signalQuality: stdDev > 10 ? 'Good' : stdDev > 5 ? 'Fair' : 'Poor'
  };
}

function findPeaks(values, threshold = 0.7) {
  const peaks = [];
  const maxVal = Math.max(...values);
  const minVal = Math.min(...values);
  const thresholdValue = minVal + (maxVal - minVal) * threshold;

  for (let i = 1; i < values.length - 1; i++) {
    if (values[i] > values[i - 1] &&
      values[i] > values[i + 1] &&
      values[i] > thresholdValue) {
      peaks.push(i);
    }
  }

  return peaks;
}

function calculateRRIntervals(peaks, samplingRate) {
  const intervals = [];
  for (let i = 1; i < peaks.length; i++) {
    const interval = (peaks[i] - peaks[i - 1]) * 1000 / samplingRate; // Convert to milliseconds
    intervals.push(interval);
  }
  return intervals;
}

function calculateRMSSD(rrIntervals) {
  if (rrIntervals.length < 2) return 25;

  const differences = [];
  for (let i = 1; i < rrIntervals.length; i++) {
    differences.push(Math.pow(rrIntervals[i] - rrIntervals[i - 1], 2));
  }

  const meanSquaredDiff = differences.reduce((a, b) => a + b, 0) / differences.length;
  return Math.sqrt(meanSquaredDiff);
}

async function processECGFile(filePath, patientId) {
  return new Promise((resolve, reject) => {
    const results = [];
    const waveformData = [];
    let recordingStart = null;
    let sampleCount = 0;

    console.log(`📈 Processing ECG file: ${path.basename(filePath)}`);

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        try {
          const timestamp = parseECGTimestamp(row.Time);
          const value = parseFloat(row.EcgWaveform);

          if (!recordingStart) {
            recordingStart = timestamp;
          }

          waveformData.push({
            timestamp,
            value,
            sampleIndex: sampleCount++
          });

        } catch (error) {
          console.warn('Skipping invalid row:', error.message);
        }
      })
      .on('end', () => {
        try {
          if (waveformData.length === 0) {
            console.warn(`⚠️ No valid data in file: ${filePath}`);
            resolve([]);
            return;
          }

          console.log(`📊 Processing ${waveformData.length} ECG samples...`);

          // Group data into 10-second segments for analysis
          const segmentDuration = 10 * ECG_SAMPLING_RATE; // 10 seconds worth of samples
          const segments = [];

          for (let i = 0; i < waveformData.length; i += segmentDuration) {
            const segment = waveformData.slice(i, i + segmentDuration);
            if (segment.length >= ECG_SAMPLING_RATE) { // At least 1 second of data
              segments.push({
                timestamp: segment[0].timestamp,
                data: segment,
                duration: segment.length / ECG_SAMPLING_RATE
              });
            }
          }

          console.log(`📈 Created ${segments.length} ECG segments for analysis`);

          // Process each segment
          segments.forEach((segment, index) => {
            const features = calculateECGFeatures(segment.data, ECG_SAMPLING_RATE);

            if (features) {
              results.push({
                patientId,
                timestamp: segment.timestamp,
                timestampString: segment.timestamp.toISOString(),
                duration: segment.duration,
                samplingRate: ECG_SAMPLING_RATE,
                sampleCount: segment.data.length,
                features,
                leadData: {
                  'II': segment.data.map(d => d.value) // Assuming Lead II from single-lead ECG
                }
              });
            }
          });

          console.log(`✅ Processed ${results.length} ECG readings from ${path.basename(filePath)}`);
          resolve(results);

        } catch (error) {
          reject(error);
        }
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

async function importECGData(ecgReadings) {
  if (!ecgReadings || ecgReadings.length === 0) {
    return;
  }

  const session = driver.session();
  try {
    console.log(`💾 Importing ${ecgReadings.length} ECG readings...`);

    // Process in batches
    for (let i = 0; i < ecgReadings.length; i += BATCH_SIZE) {
      const batch = ecgReadings.slice(i, i + BATCH_SIZE);

      // Simplify the batch data to avoid complex object issues
      const simplifiedBatch = batch.map(reading => ({
        patientId: reading.patientId,
        timestampString: reading.timestampString,
        duration: reading.duration,
        samplingRate: reading.samplingRate,
        sampleCount: reading.sampleCount,
        heartRate: reading.features.heartRate,
        rrInterval: reading.features.rrInterval,
        prInterval: reading.features.prInterval,
        qrsDuration: reading.features.qrsDuration,
        qtInterval: reading.features.qtInterval,
        hrv_rmssd: reading.features.hrv_rmssd,
        signalQuality: reading.features.signalQuality,
        leadIIsamples: reading.leadData['II'].slice(0, 100) // Take first 100 samples to avoid size issues
      }));

      await session.run(`
        UNWIND $batch AS reading
        MATCH (p:Patient:D1NAMOSubject {patientId: reading.patientId})

        CREATE (ecg:ECGReading:D1NAMOReading {
          timestamp: datetime(reading.timestampString),
          duration: reading.duration,
          samplingRate: reading.samplingRate,
          sampleCount: reading.sampleCount,
          signalQuality: reading.signalQuality,
          readingId: reading.patientId + '_ECG_' + reading.timestampString,
          dataType: 'ECG'
        })

        CREATE (features:ECGFeatures {
          heartRate: reading.heartRate,
          rrInterval: reading.rrInterval,
          prInterval: reading.prInterval,
          qrsDuration: reading.qrsDuration,
          qtInterval: reading.qtInterval,
          hrv_rmssd: reading.hrv_rmssd,
          signalQuality: reading.signalQuality
        })

        CREATE (lead:ECGLead {
          leadName: 'II',
          sampleCount: reading.sampleCount,
          samplingRate: reading.samplingRate,
          previewSamples: reading.leadIIsamples
        })

        CREATE (p)-[:HAD_ECG]->(ecg)
        CREATE (ecg)-[:HAS_FEATURES]->(features)
        CREATE (ecg)-[:HAS_LEAD]->(lead)
      `, { batch: simplifiedBatch });

      console.log(`💾 Imported batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(ecgReadings.length / BATCH_SIZE)}`);
    }

    console.log(`✅ Successfully imported ${ecgReadings.length} ECG readings`);

  } catch (error) {
    console.error('❌ Error importing ECG data:', error);
    throw error;
  } finally {
    await session.close();
  }
}

async function scanAndImportECGFiles() {
  const diabetesECGPath = path.join(DATASET_PATH, 'diabetes_subset_ecg_data');

  if (!fs.existsSync(diabetesECGPath)) {
    throw new Error(`Dataset path not found: ${diabetesECGPath}`);
  }

  const patientDirs = fs.readdirSync(diabetesECGPath)
    .filter(dir => /^\d+$/.test(dir)) // Only numeric patient directories
    .sort();

  console.log(`🔍 Found ${patientDirs.length} patient directories`);

  let totalECGReadings = 0;

  for (const patientId of patientDirs) {
    const patientDir = path.join(diabetesECGPath, patientId);
    const sensorDataDir = path.join(patientDir, 'sensor_data');

    if (!fs.existsSync(sensorDataDir)) {
      console.warn(`⚠️ No sensor data found for patient ${patientId}`);
      continue;
    }

    // Create patient
    const metadata = PATIENT_METADATA[patientId];
    if (metadata) {
      await createPatient(patientId, metadata);
    } else {
      console.warn(`⚠️ No metadata found for patient ${patientId}, using defaults`);
      await createPatient(patientId, {
        name: `D1NAMO Subject ${patientId}`,
        age: 45,
        gender: 'Unknown',
        condition: 'Type 2 Diabetes',
        diabetesDuration: 10,
        baselineHbA1c: 7.5
      });
    }

    // Process ECG files for this patient
    const sessionDirs = fs.readdirSync(sensorDataDir);
    let patientECGReadings = [];

    for (const sessionDir of sessionDirs) {
      const sessionPath = path.join(sensorDataDir, sessionDir);
      const ecgFiles = fs.readdirSync(sessionPath)
        .filter(file => file.endsWith('_ECG.csv'));

      for (const ecgFile of ecgFiles) {
        const filePath = path.join(sessionPath, ecgFile);

        try {
          console.log(`📊 Processing: Patient ${patientId}, Session ${sessionDir}, File ${ecgFile}`);
          const readings = await processECGFile(filePath, patientId);
          patientECGReadings = patientECGReadings.concat(readings);

          // Import in batches to avoid memory issues
          if (patientECGReadings.length >= BATCH_SIZE * 2) {
            await importECGData(patientECGReadings);
            totalECGReadings += patientECGReadings.length;
            patientECGReadings = [];
          }

        } catch (error) {
          console.error(`❌ Error processing file ${filePath}:`, error);
          continue;
        }
      }
    }

    // Import remaining readings for this patient
    if (patientECGReadings.length > 0) {
      await importECGData(patientECGReadings);
      totalECGReadings += patientECGReadings.length;
    }

    console.log(`✅ Completed patient ${patientId}`);
  }

  return totalECGReadings;
}

async function generateSummaryStatistics() {
  const session = driver.session();
  try {
    console.log('📈 Generating summary statistics...');

    const result = await session.run(`
      MATCH (p:D1NAMOSubject)
      OPTIONAL MATCH (p)-[:HAD_ECG]->(ecg:ECGReading)
      OPTIONAL MATCH (ecg)-[:HAS_FEATURES]->(f:ECGFeatures)
      RETURN
        count(DISTINCT p) as totalPatients,
        count(ecg) as totalECGReadings,
        avg(f.heartRate) as avgHeartRate,
        avg(f.hrv_rmssd) as avgHRV,
        min(ecg.timestamp) as earliestReading,
        max(ecg.timestamp) as latestReading
    `);

    const stats = result.records[0].toObject();

    console.log('📊 D1NAMO Import Summary:');
    console.log(`   Patients: ${stats.totalPatients}`);
    console.log(`   ECG Readings: ${stats.totalECGReadings}`);
    console.log(`   Average Heart Rate: ${stats.avgHeartRate ? Math.round(stats.avgHeartRate) : 'N/A'} BPM`);
    console.log(`   Average HRV: ${stats.avgHRV ? Math.round(stats.avgHRV) : 'N/A'} ms`);
    console.log(`   Date Range: ${stats.earliestReading} to ${stats.latestReading}`);

    return stats;

  } catch (error) {
    console.error('❌ Error generating statistics:', error);
    throw error;
  } finally {
    await session.close();
  }
}

async function importD1NAMODataset() {
  console.log('🚀 Starting D1NAMO Real Dataset Import...');
  console.log(`📂 Dataset Path: ${DATASET_PATH}`);
  console.log(`🔍 Checking if path exists: ${fs.existsSync(DATASET_PATH)}`);

  try {
    // Clear existing data
    await clearExistingD1NAMOData();

    // Setup database
    await createConstraintsAndIndexes();

    // Import ECG data
    const totalReadings = await scanAndImportECGFiles();

    // Generate summary
    await generateSummaryStatistics();

    console.log(`✅ D1NAMO import completed successfully!`);
    console.log(`📊 Total ECG readings processed: ${totalReadings}`);

    return true;

  } catch (error) {
    console.error('❌ D1NAMO import failed:', error);
    throw error;
  }
}

// Export functions
export {
  calculateECGFeatures, importD1NAMODataset, PATIENT_METADATA, processECGFile
};

// Run if called directly
if (import.meta.url === `file://${process.argv[1].replace(/\\/g, '/')}` || process.argv[1].endsWith('importRealD1NAMODataset.js')) {
  console.log('🚀 Starting D1NAMO import as main script...');
  importD1NAMODataset()
    .then(() => {
      console.log('🎉 D1NAMO import script completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Import script failed:', error);
      console.error('Error details:', error.stack);
      process.exit(1);
    })
    .finally(() => {
      driver.close();
    });
}
