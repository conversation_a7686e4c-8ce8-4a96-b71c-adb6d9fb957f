// AGP (Ambulatory Glucose Profile) calculation utility
// Based on historical implementation from FastAPI backend

export function calculateAGPData(records) {
  if (!records || !Array.isArray(records) || records.length === 0) {
    console.info("calculate_agp_data received no valid records array.");
    return null;
  }

  try {
    const data = [];

    for (const r of records) {
      // Handle different possible property names for timestamp and glucose
      let tsVal = r["g.timestamp"] || r.timestamp || r["glucose.timestamp"] || r["reading.timestamp"];
      let glucoseVal = r["g.glucose"] || r.glucose || r["glucose.value"] || r["reading.glucose"];

      // Parse timestamp
      if (typeof tsVal === 'string') {
        try {
          // Handle various timestamp formats
          tsVal = new Date(tsVal.replace('Z', '+00:00'));
        } catch (error) {
          console.debug('Failed to parse timestamp string:', tsVal);
          tsVal = null;
        }
      } else if (tsVal && typeof tsVal === 'object' && tsVal.constructor && tsVal.constructor.name === 'DateTime') {
        // Handle Neo4j DateTime objects
        try {
          tsVal = new Date(tsVal.toString());
        } catch (error) {
          console.debug('Failed to parse Neo4j DateTime:', tsVal);
          tsVal = null;
        }
      }

      if (!(tsVal instanceof Date) || isNaN(tsVal)) {
        console.debug('Invalid timestamp for record:', r);
        continue;
      }

      // Parse glucose value
      if (typeof glucoseVal === 'string') {
        try {
          glucoseVal = parseFloat(glucoseVal);
        } catch (error) {
          console.debug('Failed to parse glucose string:', glucoseVal);
          glucoseVal = null;
        }
      } else if (typeof glucoseVal === 'object' && glucoseVal !== null) {
        // Handle Neo4j Integer objects
        if (typeof glucoseVal.toNumber === 'function') {
          try {
            glucoseVal = glucoseVal.toNumber();
          } catch (error) {
            console.debug('Failed to convert Neo4j Integer:', glucoseVal);
            glucoseVal = null;
          }
        } else {
          console.debug('Unknown glucose object type:', glucoseVal);
          glucoseVal = null;
        }
      }

      if (tsVal && typeof glucoseVal === 'number' && !isNaN(glucoseVal) && glucoseVal > 0) {
        data.push({ timestamp: tsVal, glucose: parseFloat(glucoseVal) });
      } else {
        console.debug(`Skipping AGP calculation for invalid record:`, {
          timestamp: tsVal,
          glucose: glucoseVal,
          original: r
        });
      }
    }

    if (data.length === 0) {
      console.warn("No valid timestamp-glucose pairs found for AGP calculation after processing records.");
      return null;
    }

    console.log(`Successfully processed ${data.length} valid readings for AGP calculation`);

    // Group readings by hour
    const hourlyReadings = {};
    for (let hour = 0; hour < 24; hour++) {
      hourlyReadings[hour] = [];
    }

    for (const reading of data) {
      const hour = reading.timestamp.getHours();
      hourlyReadings[hour].push(reading.glucose);
    }

    // Calculate percentiles for each hour
    const percentiles = {
      p10: [],
      p25: [],
      p50: [],
      p75: [],
      p90: []
    };
    const hoursLabels = Array.from({ length: 24 }, (_, i) => i);

    for (const hour of hoursLabels) {
      const readingsForHour = hourlyReadings[hour];

      if (readingsForHour.length >= 2) {
        try {
          percentiles.p10.push(calculatePercentile(readingsForHour, 10));
          percentiles.p25.push(calculatePercentile(readingsForHour, 25));
          percentiles.p50.push(calculateMedian(readingsForHour));
          percentiles.p75.push(calculatePercentile(readingsForHour, 75));
          percentiles.p90.push(calculatePercentile(readingsForHour, 90));
        } catch (calcError) {
          console.error(`Error calculating percentiles for hour ${hour}:`, calcError);
          for (const key in percentiles) {
            percentiles[key].push(null);
          }
        }
      } else if (readingsForHour.length === 1) {
        // If only one reading, use that value for all percentiles
        const singleValue = readingsForHour[0];
        percentiles.p10.push(singleValue);
        percentiles.p25.push(singleValue);
        percentiles.p50.push(singleValue);
        percentiles.p75.push(singleValue);
        percentiles.p90.push(singleValue);
      } else {
        // No readings for this hour
        for (const key in percentiles) {
          percentiles[key].push(null);
        }
      }
    }

    // Convert all percentiles to numbers or null
    for (const key in percentiles) {
      percentiles[key] = percentiles[key].map(p =>
        p !== null && !isNaN(p) ? parseFloat(p) : null
      );
    }

    const result = {
      labels: hoursLabels,
      percentiles: percentiles,
      totalReadings: data.length,
      dateRange: {
        start: new Date(Math.min(...data.map(d => d.timestamp))),
        end: new Date(Math.max(...data.map(d => d.timestamp)))
      }
    };

    console.log('AGP calculation completed successfully:', {
      totalReadings: result.totalReadings,
      dateRange: result.dateRange,
      hoursWithData: Object.values(hourlyReadings).filter(arr => arr.length > 0).length
    });

    return result;

  } catch (error) {
    console.error("Error calculating AGP data:", error);
    console.error("Records that caused the error:", records);
    return null;
  }
}

// Helper function to calculate percentile
function calculatePercentile(arr, percentile) {
  const sorted = arr.slice().sort((a, b) => a - b);
  const index = (percentile / 100) * (sorted.length - 1);
  const lower = Math.floor(index);
  const upper = Math.ceil(index);
  const weight = index % 1;

  if (upper >= sorted.length) return sorted[sorted.length - 1];
  return sorted[lower] * (1 - weight) + sorted[upper] * weight;
}

// Helper function to calculate median
function calculateMedian(arr) {
  const sorted = arr.slice().sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);

  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2;
  } else {
    return sorted[middle];
  }
}

// Glucose range classifications
export const GLUCOSE_RANGES = {
  VERY_LOW: { min: 0, max: 54, label: 'Very Low', color: '#dc2626' },
  LOW: { min: 54, max: 70, label: 'Low', color: '#f59e0b' },
  TARGET: { min: 70, max: 180, label: 'Target Range', color: '#10b981' },
  HIGH: { min: 180, max: 250, label: 'High', color: '#f59e0b' },
  VERY_HIGH: { min: 250, max: 400, label: 'Very High', color: '#dc2626' }
};

// Default glucose thresholds
export const DEFAULT_THRESHOLDS = {
  HYPO: 70,
  HYPER: 180
};

// Generate mock glucose data for testing
export function generateMockGlucoseData(days = 14, readingsPerDay = 12) {
  const data = [];
  const now = new Date();

  for (let day = 0; day < days; day++) {
    for (let reading = 0; reading < readingsPerDay; reading++) {
      const timestamp = new Date(now);
      timestamp.setDate(timestamp.getDate() - day);
      timestamp.setHours(Math.floor(reading * 24 / readingsPerDay));
      timestamp.setMinutes(Math.random() * 60);
      timestamp.setSeconds(0);

      // Generate realistic glucose values with some patterns
      const hour = timestamp.getHours();
      let baseGlucose = 100;

      // Add patterns based on time of day
      if (hour >= 6 && hour <= 9) baseGlucose += 20; // Morning spike
      if (hour >= 12 && hour <= 14) baseGlucose += 15; // Lunch spike
      if (hour >= 18 && hour <= 20) baseGlucose += 15; // Dinner spike
      if (hour >= 22 || hour <= 5) baseGlucose -= 10; // Night time lower

      // Add some randomness
      const glucose = Math.max(60, Math.min(250,
        baseGlucose + (Math.random() - 0.5) * 40
      ));

      data.push({
        timestamp: timestamp.toISOString(),
        glucose: Math.round(glucose)
      });
    }
  }

  return data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
}

// Calculate Time-in-Range (TIR) Statistics - CRITICAL AGP FEATURE
export function calculateTimeInRange(data, customRanges = null) {
  if (!data || !Array.isArray(data) || data.length === 0) {
    console.warn("calculateTimeInRange received invalid data:", data);
    return null;
  }

  const ranges = customRanges || {
    veryLow: { min: 0, max: 54, label: 'Very Low (<54)', color: '#dc2626' },
    low: { min: 54, max: 70, label: 'Low (54-70)', color: '#f59e0b' },
    target: { min: 70, max: 180, label: 'Target Range (70-180)', color: '#10b981' },
    high: { min: 180, max: 250, label: 'High (180-250)', color: '#f59e0b' },
    veryHigh: { min: 250, max: 400, label: 'Very High (>250)', color: '#dc2626' }
  };

  // Extract glucose values safely
  const glucoseValues = data.map(reading => {
    let glucose = reading.glucose || reading['g.glucose'] || reading['glucose.value'];

    // Handle Neo4j Integer objects
    if (typeof glucose === 'object' && glucose !== null && typeof glucose.toNumber === 'function') {
      glucose = glucose.toNumber();
    }

    return typeof glucose === 'number' && !isNaN(glucose) ? glucose : null;
  }).filter(g => g !== null);

  if (glucoseValues.length === 0) {
    console.warn("No valid glucose values found for Time-in-Range calculation");
    return null;
  }

  const totalReadings = glucoseValues.length;
  const stats = {};

  // Count readings in each range
  for (const [key, range] of Object.entries(ranges)) {
    const count = glucoseValues.filter(glucose =>
      glucose >= range.min && glucose <= range.max
    ).length;

    stats[key] = {
      count,
      percentage: ((count / totalReadings) * 100).toFixed(1),
      label: range.label,
      color: range.color
    };
  }

  return {
    totalReadings,
    ranges: stats,
    targetRangePercentage: stats.target?.percentage || 0
  };
}

// Calculate Glucose Management Indicator (GMI) - CRITICAL AGP FEATURE
export function calculateGMI(data) {
  if (!data || data.length === 0) return null;

  const glucoseValues = data.map(reading => {
    return typeof reading.glucose === 'number' ? reading.glucose : reading['g.glucose'];
  }).filter(g => g && !isNaN(g));

  if (glucoseValues.length === 0) return null;

  // Calculate mean glucose
  const meanGlucose = glucoseValues.reduce((sum, g) => sum + g, 0) / glucoseValues.length;

  // Convert to GMI using the formula: GMI = 3.31 + (0.02392 × mean glucose in mg/dL)
  const gmi = 3.31 + (0.02392 * meanGlucose);

  return {
    gmi: gmi.toFixed(1),
    meanGlucose: meanGlucose.toFixed(1),
    estimatedA1C: gmi.toFixed(1) // GMI is equivalent to estimated A1C
  };
}

// Calculate AGP Summary Statistics
export function calculateAGPSummary(data) {
  if (!data || data.length === 0) return null;

  const glucoseValues = data.map(reading => {
    return typeof reading.glucose === 'number' ? reading.glucose : reading['g.glucose'];
  }).filter(g => g && !isNaN(g));

  if (glucoseValues.length === 0) return null;

  const sorted = glucoseValues.slice().sort((a, b) => a - b);
  const mean = glucoseValues.reduce((sum, g) => sum + g, 0) / glucoseValues.length;
  const variance = glucoseValues.reduce((sum, g) => sum + Math.pow(g - mean, 2), 0) / glucoseValues.length;
  const standardDeviation = Math.sqrt(variance);

  // Calculate coefficient of variation (CV) - important diabetes metric
  const coefficientOfVariation = (standardDeviation / mean) * 100;

  return {
    count: glucoseValues.length,
    mean: mean.toFixed(1),
    median: calculateMedian(glucoseValues).toFixed(1),
    standardDeviation: standardDeviation.toFixed(1),
    coefficientOfVariation: coefficientOfVariation.toFixed(1),
    minimum: Math.min(...glucoseValues),
    maximum: Math.max(...glucoseValues),
    range: Math.max(...glucoseValues) - Math.min(...glucoseValues),
    percentile25: calculatePercentile(glucoseValues, 25).toFixed(1),
    percentile75: calculatePercentile(glucoseValues, 75).toFixed(1)
  };
}

// Analyze daily patterns
export function analyzeDailyPatterns(data) {
  if (!data || data.length === 0) return null;

  const patterns = {
    dawn: [], // 4-8 AM
    morning: [], // 8-12 PM
    afternoon: [], // 12-6 PM
    evening: [], // 6-10 PM
    night: [] // 10 PM - 4 AM
  };

  for (const reading of data) {
    const timestamp = typeof reading.timestamp === 'string'
      ? new Date(reading.timestamp)
      : reading.timestamp;

    if (!timestamp || !(timestamp instanceof Date)) continue;

    const hour = timestamp.getHours();
    const glucose = typeof reading.glucose === 'number' ? reading.glucose : reading['g.glucose'];

    if (!glucose || isNaN(glucose)) continue;

    if (hour >= 4 && hour < 8) patterns.dawn.push(glucose);
    else if (hour >= 8 && hour < 12) patterns.morning.push(glucose);
    else if (hour >= 12 && hour < 18) patterns.afternoon.push(glucose);
    else if (hour >= 18 && hour < 22) patterns.evening.push(glucose);
    else patterns.night.push(glucose);
  }

  const summary = {};
  for (const [period, values] of Object.entries(patterns)) {
    if (values.length > 0) {
      const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
      const sorted = values.slice().sort((a, b) => a - b);
      summary[period] = {
        count: values.length,
        mean: mean.toFixed(1),
        median: calculateMedian(values).toFixed(1),
        min: Math.min(...values),
        max: Math.max(...values),
        standardDeviation: Math.sqrt(
          values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length
        ).toFixed(1)
      };
    } else {
      summary[period] = null;
    }
  }

  return summary;
}
