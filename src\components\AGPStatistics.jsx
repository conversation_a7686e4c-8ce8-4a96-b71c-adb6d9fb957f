import { analyzeDailyPatterns, calculateAGPSummary, calculateGMI, calculateTimeInRange } from '../utils/agpCalculation';
import './AGPStatistics.css';

function AGPStatistics({ glucoseData, customRanges, className = "" }) {
  if (!glucoseData || glucoseData.length === 0) {
    return (
      <div className={`agp-statistics ${className}`}>
        <div className="agp-stats-placeholder">
          <p>No glucose data available for AGP statistics</p>
        </div>
      </div>
    );
  }

  // Calculate all statistics
  const tirStats = calculateTimeInRange(glucoseData, customRanges);
  const gmiStats = calculateGMI(glucoseData);
  const summaryStats = calculateAGPSummary(glucoseData);
  const dailyPatterns = analyzeDailyPatterns(glucoseData);

  return (
    <div className={`agp-statistics ${className}`}>
      {/* Time in Range - Most Critical AGP Feature */}
      <div className="agp-stat-section tir-section">
        <h3 className="section-title">Time in Range (TIR)</h3>
        {tirStats && (
          <div className="tir-container">
            <div className="tir-visual">
              {Object.entries(tirStats.ranges).map(([key, range]) => (
                <div
                  key={key}
                  className="tir-bar"
                  style={{
                    width: `${range.percentage}%`,
                    backgroundColor: range.color,
                    minWidth: range.percentage > 0 ? '1%' : '0'
                  }}
                  title={`${range.label}: ${range.percentage}%`}
                />
              ))}
            </div>
            <div className="tir-legend">
              {Object.entries(tirStats.ranges).map(([key, range]) => (
                <div key={key} className="tir-legend-item">
                  <div
                    className="tir-color-box"
                    style={{ backgroundColor: range.color }}
                  />
                  <span className="tir-label">{range.label}</span>
                  <span className="tir-percentage">{range.percentage}%</span>
                  <span className="tir-count">({range.count} readings)</span>
                </div>
              ))}
            </div>
            <div className="tir-target-highlight">
              <h4>Target Range Achievement: <span className="target-percentage">{tirStats.targetRangePercentage}%</span></h4>
              <p className="target-goal">Clinical Goal: ≥70% in target range (70-180 mg/dL)</p>
            </div>
          </div>
        )}
      </div>

      {/* Glucose Management Indicator */}
      <div className="agp-stat-section gmi-section">
        <h3 className="section-title">Glucose Management Indicator (GMI)</h3>
        {gmiStats && (
          <div className="gmi-container">
            <div className="gmi-main">
              <div className="gmi-value">
                <span className="gmi-number">{gmiStats.gmi}%</span>
                <span className="gmi-label">Estimated A1C</span>
              </div>
              <div className="mean-glucose">
                <span className="mean-value">{gmiStats.meanGlucose}</span>
                <span className="mean-unit">mg/dL</span>
                <span className="mean-label">Mean Glucose</span>
              </div>
            </div>
            <div className="gmi-interpretation">
              <p>GMI is derived from your continuous glucose data and provides an estimate of what your A1C might be.</p>
            </div>
          </div>
        )}
      </div>

      {/* Summary Statistics */}
      <div className="agp-stat-section summary-section">
        <h3 className="section-title">Glucose Statistics Summary</h3>
        {summaryStats && (
          <div className="summary-grid">
            <div className="stat-card">
              <div className="stat-value">{summaryStats.count}</div>
              <div className="stat-label">Total Readings</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{summaryStats.mean} mg/dL</div>
              <div className="stat-label">Mean Glucose</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{summaryStats.median} mg/dL</div>
              <div className="stat-label">Median</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{summaryStats.standardDeviation} mg/dL</div>
              <div className="stat-label">Standard Deviation</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{summaryStats.coefficientOfVariation}%</div>
              <div className="stat-label">Coefficient of Variation</div>
              <div className="stat-note">Goal: &lt;36%</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{summaryStats.minimum} - {summaryStats.maximum}</div>
              <div className="stat-label">Range (mg/dL)</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{summaryStats.percentile25} mg/dL</div>
              <div className="stat-label">25th Percentile</div>
            </div>
            <div className="stat-card">
              <div className="stat-value">{summaryStats.percentile75} mg/dL</div>
              <div className="stat-label">75th Percentile</div>
            </div>
          </div>
        )}
      </div>

      {/* Daily Patterns Analysis */}
      <div className="agp-stat-section patterns-section">
        <h3 className="section-title">Daily Pattern Analysis</h3>
        {dailyPatterns && (
          <div className="patterns-grid">
            {Object.entries(dailyPatterns).map(([period, stats]) => (
              <div key={period} className="pattern-card">
                <h4 className="pattern-title">
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                  {period === 'dawn' && ' (4-8 AM)'}
                  {period === 'morning' && ' (8-12 PM)'}
                  {period === 'afternoon' && ' (12-6 PM)'}
                  {period === 'evening' && ' (6-10 PM)'}
                  {period === 'night' && ' (10 PM-4 AM)'}
                </h4>
                {stats ? (
                  <div className="pattern-stats">
                    <div className="pattern-main">
                      <span className="pattern-mean">{stats.mean} mg/dL</span>
                      <span className="pattern-label">Mean</span>
                    </div>
                    <div className="pattern-details">
                      <div>Median: {stats.median} mg/dL</div>
                      <div>Range: {stats.min}-{stats.max} mg/dL</div>
                      <div>SD: {stats.standardDeviation} mg/dL</div>
                      <div>Readings: {stats.count}</div>
                    </div>
                  </div>
                ) : (
                  <div className="pattern-no-data">No data</div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Clinical Insights */}
      <div className="agp-stat-section insights-section">
        <h3 className="section-title">Clinical Insights</h3>
        <div className="insights-container">
          {tirStats && (
            <div className="insight">
              <strong>Time in Range: </strong>
              {parseFloat(tirStats.targetRangePercentage) >= 70
                ? "✅ Excellent glucose control - meeting clinical goals!"
                : parseFloat(tirStats.targetRangePercentage) >= 50
                  ? "⚠️ Good progress - work toward 70% target range goal"
                  : "🔴 Focus needed - significant opportunity for improvement"}
            </div>
          )}
          {summaryStats && (
            <div className="insight">
              <strong>Glucose Variability: </strong>
              {parseFloat(summaryStats.coefficientOfVariation) <= 36
                ? "✅ Low variability - stable glucose patterns"
                : "⚠️ High variability - consider pattern analysis with healthcare provider"}
            </div>
          )}
          {gmiStats && (
            <div className="insight">
              <strong>Estimated A1C: </strong>
              {parseFloat(gmiStats.gmi) <= 7.0
                ? "✅ Meeting A1C target for most adults with diabetes"
                : "⚠️ Above target - discuss glucose management strategies"}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default AGPStatistics;
