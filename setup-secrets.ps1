# Setup Cloudflare Pages Secrets Script
# Run this to configure environment variables for production

Write-Host "🔐 Setting up Cloudflare Pages secrets..." -ForegroundColor Green

$projectName = "healthhub-research-platform"

# Read from .env file
if (Test-Path ".env") {
    Write-Host "📋 Reading configuration from .env file..." -ForegroundColor Yellow

    # Neo4j Configuration
    Write-Host "Setting up Neo4j secrets..." -ForegroundColor Cyan
    $neo4jUri = (Get-Content .env | Where-Object { $_ -match "^VITE_NEO4J_URI=" }) -replace "VITE_NEO4J_URI=", ""
    $neo4jUsername = (Get-Content .env | Where-Object { $_ -match "^VITE_NEO4J_USERNAME=" }) -replace "VITE_NEO4J_USERNAME=", ""
    $neo4jPassword = (Get-Content .env | Where-Object { $_ -match "^VITE_NEO4J_PASSWORD=" }) -replace "VITE_NEO4J_PASSWORD=", ""

    if ($neo4jUri) {
        Write-Host "Setting NEO4J_URI..." -ForegroundColor Gray
        echo $neo4jUri | wrangler pages secret put NEO4J_URI --project-name=$projectName
    }

    if ($neo4jUsername) {
        Write-Host "Setting NEO4J_USERNAME..." -ForegroundColor Gray
        echo $neo4jUsername | wrangler pages secret put NEO4J_USERNAME --project-name=$projectName
    }

    if ($neo4jPassword) {
        Write-Host "Setting NEO4J_PASSWORD..." -ForegroundColor Gray
        echo $neo4jPassword | wrangler pages secret put NEO4J_PASSWORD --project-name=$projectName
    }

    # OpenAI Configuration
    Write-Host "Setting up OpenAI secrets..." -ForegroundColor Cyan
    $openaiKey = (Get-Content .env | Where-Object { $_ -match "^VITE_OPENAI_API_KEY=" }) -replace "VITE_OPENAI_API_KEY=", ""

    if ($openaiKey) {
        Write-Host "Setting OPENAI_API_KEY..." -ForegroundColor Gray
        echo $openaiKey | wrangler pages secret put OPENAI_API_KEY --project-name=$projectName
    }

    Write-Host "✅ Secrets configuration complete!" -ForegroundColor Green
    Write-Host "🔄 Deploy your project to activate the new configuration" -ForegroundColor Yellow

} else {
    Write-Host "❌ .env file not found. Please create one first." -ForegroundColor Red
    exit 1
}
