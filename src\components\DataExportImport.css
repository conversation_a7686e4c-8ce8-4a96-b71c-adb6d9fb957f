/* Data Export Import Styles */
.data-export-import {
  padding: 1.5rem;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  min-height: 100vh;
}

.export-import-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--gb-accent);
}

.export-import-header h2 {
  color: var(--gb-accent2);
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
}

.export-import-header p {
  color: #a89984;
  margin: 0;
  font-size: 1.1rem;
}

/* Tab Layout */
.export-import-tabs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

@media (max-width: 1024px) {
  .export-import-tabs {
    grid-template-columns: 1fr;
  }
}

.export-section,
.import-section {
  background: var(--gb-bg1);
  border-radius: 12px;
  padding: 2rem;
  border: 2px solid var(--gb-bg0);
  transition: all 0.3s ease;
}

.export-section:hover,
.import-section:hover {
  border-color: var(--gb-accent);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.export-section h3,
.import-section h3 {
  color: var(--gb-accent2);
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Export Controls */
.export-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.control-group label {
  color: #a89984;
  font-size: 0.95rem;
  font-weight: 500;
}

.control-group select {
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 2px solid var(--gb-bg0);
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.control-group select:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 3px rgba(215, 153, 33, 0.2);
}

/* Custom Fields Selector */
.custom-fields-selector {
  background: var(--gb-bg0);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0 2rem 0;
  border: 1px solid var(--gb-bg1);
}

.custom-fields-selector h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
}

.field-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gb-fg);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.field-checkbox:hover {
  background: rgba(215, 153, 33, 0.1);
}

.field-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--gb-accent);
}

/* Export Actions */
.export-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
}

.export-btn {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn:hover:not(:disabled) {
  background: var(--gb-accent2);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.export-info {
  color: #a89984;
  font-size: 0.875rem;
  line-height: 1.5;
}

.export-info p {
  margin: 0 0 0.25rem 0;
}

.export-info strong {
  color: var(--gb-accent2);
}

/* Import Controls */
.import-controls {
  margin-bottom: 2rem;
}

.import-area {
  position: relative;
  margin-bottom: 1.5rem;
}

.file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.file-input-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2.5rem 2rem;
  border: 3px dashed var(--gb-accent);
  border-radius: 12px;
  background: var(--gb-bg0);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.file-input-label:hover {
  background: rgba(215, 153, 33, 0.05);
  border-color: var(--gb-accent2);
  transform: scale(1.02);
}

.upload-icon {
  font-size: 3rem;
  color: var(--gb-accent);
}

.file-input-label>span:nth-child(2) {
  color: var(--gb-fg);
  font-size: 1.1rem;
  font-weight: 600;
}

.file-types {
  color: #928374;
  font-size: 0.875rem;
}

/* Import Status */
.import-status {
  padding: 1rem 1.25rem;
  border-radius: 6px;
  margin-top: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  font-size: 0.95rem;
}

.import-status.loading {
  background: rgba(131, 165, 152, 0.1);
  border: 1px solid #83a598;
  color: #83a598;
}

.import-status.success {
  background: rgba(184, 187, 38, 0.1);
  border: 1px solid #b8bb26;
  color: #b8bb26;
}

.import-status.error {
  background: rgba(251, 73, 52, 0.1);
  border: 1px solid #fb4934;
  color: #fb4934;
}

.status-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.import-summary {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.import-summary p {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.import-summary ul {
  margin: 0;
  padding-left: 1.25rem;
}

.import-summary li {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

/* Import Instructions */
.import-instructions {
  background: var(--gb-bg0);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--gb-bg1);
}

.import-instructions h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.import-instructions ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #a89984;
}

.import-instructions li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.import-instructions strong {
  color: var(--gb-fg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-export-import {
    padding: 1rem;
  }

  .export-import-header h2 {
    font-size: 1.5rem;
  }

  .export-section,
  .import-section {
    padding: 1.5rem;
  }

  .fields-grid {
    grid-template-columns: 1fr;
  }

  .export-controls {
    gap: 1.5rem;
  }

  .file-input-label {
    padding: 2rem 1rem;
  }

  .upload-icon {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .export-import-header h2 {
    font-size: 1.25rem;
  }

  .export-section h3,
  .import-section h3 {
    font-size: 1.25rem;
  }

  .export-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  .file-input-label>span:nth-child(2) {
    font-size: 1rem;
  }
}

/* Animation for loading states */
@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

.import-status.loading .status-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

.export-btn:disabled .export-icon {
  animation: pulse 1s ease-in-out infinite;
}

/* Success animations */
@keyframes checkmark {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

.import-status.success .status-icon {
  animation: checkmark 0.5s ease-out;
}
