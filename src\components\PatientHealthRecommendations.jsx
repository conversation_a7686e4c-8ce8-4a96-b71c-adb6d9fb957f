import { useEffect, useState } from 'react';
import AIRecommendationService from '../services/aiService';
import './PatientHealthRecommendations.css';

const PatientHealthRecommendations = ({
    patientData,
    preferredTechnique = 'structured',
    showTechniqueSelector = true,
    onRecommendationsUpdate
}) => {
    const [recommendations, setRecommendations] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedTechnique, setSelectedTechnique] = useState(preferredTechnique);
    const [expandedCards, setExpandedCards] = useState(new Set());
    const [completedActions, setCompletedActions] = useState(new Set());
    const [error, setError] = useState(null);
    const [refreshKey, setRefreshKey] = useState(0);

    const aiService = new AIRecommendationService();

    const promptTechniques = [
        // Traditional approaches
        { id: 'structured', name: '📋 Structured', description: 'Clear, organized recommendations' },
        { id: 'conversational', name: '💬 Friendly', description: 'Supportive, encouraging tone' },
        { id: 'motivational', name: '🚀 Motivational', description: 'Inspiring, empowering approach' },
        { id: 'clinical', name: '🏥 Clinical', description: 'Medical, evidence-based guidance' },

        // Advanced technical techniques
        { id: 'zero_shot', name: '🎯 Zero-Shot', description: 'Direct task execution without examples' },
        { id: 'one_shot', name: '1️⃣ One-Shot', description: 'Learning from single example' },
        { id: 'few_shot', name: '📚 Few-Shot', description: 'Pattern learning from multiple examples' },
        { id: 'chain_of_thought', name: '🔗 Chain-of-Thought', description: 'Step-by-step reasoning process' },
        { id: 'tree_of_thought', name: '🌳 Tree-of-Thought', description: 'Multiple reasoning paths explored' },
        { id: 'self_consistency', name: '🔄 Self-Consistency', description: 'Multiple perspectives synthesized' },
        { id: 'react', name: '⚡ ReAct', description: 'Reasoning + Acting approach' },
        { id: 'role_playing', name: '🎭 Role-Playing', description: 'Expert persona consultation' }
    ];

    useEffect(() => {
        if (patientData) {
            generateRecommendations();
        }
    }, [patientData, selectedTechnique, refreshKey]);

    const generateRecommendations = async () => {
        if (!patientData) return;

        setIsLoading(true);
        setError(null);

        try {
            const results = await aiService.generatePatientHealthRecommendations(patientData, {
                promptingTechnique: selectedTechnique
            });

            setRecommendations(results);

            if (onRecommendationsUpdate) {
                onRecommendationsUpdate(results);
            }

        } catch (err) {
            console.error('Failed to generate recommendations:', err);
            setError(err.message);
            setRecommendations(null);
        } finally {
            setIsLoading(false);
        }
    };

    const toggleCardExpansion = (index) => {
        const newExpanded = new Set(expandedCards);
        if (newExpanded.has(index)) {
            newExpanded.delete(index);
        } else {
            newExpanded.add(index);
        }
        setExpandedCards(newExpanded);
    };

    const toggleActionComplete = (recIndex, actionIndex) => {
        const actionKey = `${recIndex}-${actionIndex}`;
        const newCompleted = new Set(completedActions);

        if (newCompleted.has(actionKey)) {
            newCompleted.delete(actionKey);
        } else {
            newCompleted.add(actionKey);
        }

        setCompletedActions(newCompleted);
        localStorage.setItem('completedHealthActions', JSON.stringify(Array.from(newCompleted)));
    };

    const getPriorityColor = (priority) => {
        const colors = {
            urgent: '#dc3545',
            high: '#fd7e14',
            medium: '#ffc107',
            low: '#28a745'
        };
        return colors[priority] || '#6c757d';
    };

    const getDifficultyIcon = (difficulty) => {
        const icons = {
            easy: '🟢',
            moderate: '🟡',
            challenging: '🔴'
        };
        return icons[difficulty] || '⚪';
    };

    const formatTimeframe = (timeline) => {
        if (!timeline) return 'Ongoing';
        return timeline.replace(/^\d+[-\s]*/, ''); // Clean up timeline formatting
    };

    const refreshRecommendations = () => {
        setRefreshKey(prev => prev + 1);
    };

    if (!patientData) {
        return (
            <div className="health-recommendations-empty">
                <div className="empty-icon">💡</div>
                <h3>Personalized Health Recommendations</h3>
                <p>Patient data is required to generate personalized health improvement recommendations.</p>
            </div>
        );
    }

    return (
        <div className="patient-health-recommendations">
            <div className="recommendations-header">
                <div className="header-content">
                    <h2>💡 Your Personalized Health Recommendations</h2>
                    <p>AI-powered suggestions tailored to help you improve your health and manage your diabetes better.</p>
                </div>

                <div className="header-controls">
                    {showTechniqueSelector && (
                        <div className="technique-selector">
                            <label>Recommendation Style:</label>
                            <select
                                value={selectedTechnique}
                                onChange={(e) => setSelectedTechnique(e.target.value)}
                                disabled={isLoading}
                            >
                                {promptTechniques.map(technique => (
                                    <option key={technique.id} value={technique.id}>
                                        {technique.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}

                    <button
                        className="refresh-btn"
                        onClick={refreshRecommendations}
                        disabled={isLoading}
                        title="Get fresh recommendations"
                    >
                        {isLoading ? '🔄' : '🔄'} Refresh
                    </button>
                </div>
            </div>

            {isLoading && (
                <div className="loading-state">
                    <div className="loading-spinner"></div>
                    <p>Generating your personalized recommendations...</p>
                    <small>Using {promptTechniques.find(t => t.id === selectedTechnique)?.name} approach</small>
                </div>
            )}

            {error && (
                <div className="error-state">
                    <div className="error-icon">⚠️</div>
                    <h4>Unable to Generate Recommendations</h4>
                    <p>{error}</p>
                    <button onClick={refreshRecommendations} className="retry-btn">
                        Try Again
                    </button>
                </div>
            )}

            {recommendations && recommendations.recommendations && (
                <div className="recommendations-content">
                    <div className="recommendations-summary">
                        <div className="summary-stats">
                            <div className="stat-item">
                                <span className="stat-number">{recommendations.recommendations.length}</span>
                                <span className="stat-label">Recommendations</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-number">
                                    {recommendations.recommendations.filter(r => r.priority === 'high' || r.priority === 'urgent').length}
                                </span>
                                <span className="stat-label">High Priority</span>
                            </div>
                            <div className="stat-item">
                                <span className="stat-number">
                                    {recommendations.recommendations.filter(r => r.difficulty === 'easy').length}
                                </span>
                                <span className="stat-label">Easy to Start</span>
                            </div>
                        </div>

                        <div className="technique-info">
                            <span className="technique-badge">
                                {promptTechniques.find(t => t.id === selectedTechnique)?.name}
                            </span>
                            <span className="generation-time">
                                Generated {new Date(recommendations.generated_at).toLocaleString()}
                            </span>
                        </div>
                    </div>

                    <div className="recommendations-grid">
                        {recommendations.recommendations.map((rec, index) => {
                            const isExpanded = expandedCards.has(index);

                            return (
                                <div key={index} className="recommendation-card">
                                    <div className="card-header">
                                        <div className="header-left">
                                            <div className="category-badge" style={{ backgroundColor: getPriorityColor(rec.priority) }}>
                                                {rec.category}
                                            </div>
                                            <div className="difficulty-indicator" title={`Difficulty: ${rec.difficulty}`}>
                                                {getDifficultyIcon(rec.difficulty)}
                                            </div>
                                        </div>
                                        <div className="priority-badge" style={{ color: getPriorityColor(rec.priority) }}>
                                            {rec.priority?.toUpperCase()}
                                        </div>
                                    </div>

                                    <div className="card-body">
                                        <h4 className="recommendation-title">{rec.title}</h4>
                                        <p className="recommendation-description">{rec.description}</p>

                                        {rec.expected_benefit && (
                                            <div className="benefit-section">
                                                <strong>Expected Benefit:</strong>
                                                <span>{rec.expected_benefit}</span>
                                            </div>
                                        )}

                                        {rec.timeline && (
                                            <div className="timeline-section">
                                                <strong>Timeline:</strong>
                                                <span>{formatTimeframe(rec.timeline)}</span>
                                            </div>
                                        )}

                                        {rec.action_items && rec.action_items.length > 0 && (
                                            <div className="actions-section">
                                                <h5 onClick={() => toggleCardExpansion(index)} className="actions-toggle">
                                                    Action Steps {isExpanded ? '▼' : '▶'} ({rec.action_items.length})
                                                </h5>

                                                {isExpanded && (
                                                    <div className="action-items">
                                                        {rec.action_items.map((action, actionIndex) => {
                                                            const actionKey = `${index}-${actionIndex}`;
                                                            const isCompleted = completedActions.has(actionKey);

                                                            return (
                                                                <div key={actionIndex} className={`action-item ${isCompleted ? 'completed' : ''}`}>
                                                                    <label className="action-checkbox">
                                                                        <input
                                                                            type="checkbox"
                                                                            checked={isCompleted}
                                                                            onChange={() => toggleActionComplete(index, actionIndex)}
                                                                        />
                                                                        <span className="checkmark"></span>
                                                                        <span className="action-text">{action}</span>
                                                                    </label>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {rec.success_metrics && rec.success_metrics.length > 0 && isExpanded && (
                                            <div className="success-metrics">
                                                <h5>Success Indicators:</h5>
                                                <ul>
                                                    {rec.success_metrics.map((metric, idx) => (
                                                        <li key={idx}>{metric}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>

                                    <div className="card-footer">
                                        <button
                                            className="expand-btn"
                                            onClick={() => toggleCardExpansion(index)}
                                        >
                                            {isExpanded ? '▲ Show Less' : '▼ Show More'}
                                        </button>
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    <div className="recommendations-footer">
                        <div className="disclaimer">
                            <p><strong>Important:</strong> These recommendations are AI-generated suggestions for informational purposes only. Always consult with your healthcare provider before making significant changes to your treatment plan.</p>
                        </div>

                        <div className="action-buttons">
                            <button className="share-btn" onClick={() => {
                                if (navigator.share) {
                                    navigator.share({
                                        title: 'My Health Recommendations',
                                        text: `I have ${recommendations.recommendations.length} personalized health recommendations from my AI health assistant.`,
                                    });
                                }
                            }}>
                                📤 Share Progress
                            </button>

                            <button className="print-btn" onClick={() => window.print()}>
                                🖨️ Print Recommendations
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default PatientHealthRecommendations;
