import { lazy, Suspense } from 'react'
import styles from './Neo4jWorkbench.module.css'

const QueryRunner = lazy(() => import('./QueryRunner'))

function Neo4jWorkbench() {
    return (
        <div className={styles['neo4j-workbench']}>
            <div className={styles['workbench-panel'] + ' ' + styles['single']}>
                <Suspense fallback={<div className={styles['loading']}>Loading Query Runner...</div>}>
                    <QueryRunner />
                </Suspense>
            </div>
        </div>
    )
}

export default Neo4jWorkbench
