import { useEffect, useState } from 'react';
import Neo4jService from '../services/neo4jService';
import './AdvancedSearch.css';

const AdvancedSearch = ({ onResultsUpdate, onPatientSelect }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchType, setSearchType] = useState('all'); // all, patients, readings, conditions, medications
    const [filters, setFilters] = useState({
        dateRange: {
            start: '',
            end: '',
            preset: '30' // 7, 14, 30, 90, 180, custom
        },
        glucoseRange: {
            min: '',
            max: '',
            enabled: false
        },
        conditions: [],
        ageRange: {
            min: '',
            max: '',
            enabled: false
        },
        gender: '',
        readingType: '',
        timeInRangeFilter: {
            min: '',
            max: '',
            enabled: false
        },
        riskLevel: '', // low, moderate, high
        sortBy: 'lastReading', // name, age, lastReading, avgGlucose, readingCount
        sortOrder: 'desc' // asc, desc
    });

    const [searchResults, setSearchResults] = useState({
        patients: [],
        readings: [],
        totalCount: 0,
        searchTime: 0
    });

    const [availableConditions, setAvailableConditions] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [searchHistory, setSearchHistory] = useState([]);
    const [savedSearches, setSavedSearches] = useState([]);
    const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

    const neo4jService = new Neo4jService();

    useEffect(() => {
        loadAvailableConditions();
        loadSearchHistory();
        loadSavedSearches();
    }, []);

    useEffect(() => {
        if (searchQuery || hasActiveFilters()) {
            const debounceTimer = setTimeout(() => {
                performSearch();
            }, 300);
            return () => clearTimeout(debounceTimer);
        } else {
            setSearchResults({ patients: [], readings: [], totalCount: 0, searchTime: 0 });
        }
    }, [searchQuery, filters, searchType]);

    const loadAvailableConditions = async () => {
        try {
            await neo4jService.connect();
            const query = `
        MATCH (c:Condition)
        RETURN DISTINCT c.name as condition
        ORDER BY c.name
      `;
            const results = await neo4jService.runQuery(query);
            setAvailableConditions(results.map(r => r.condition));
        } catch (error) {
            console.error('Failed to load conditions:', error);
            // Fallback to common conditions
            setAvailableConditions(['Type 1 Diabetes', 'Type 2 Diabetes', 'Prediabetes', 'Gestational Diabetes']);
        }
    };

    const loadSearchHistory = () => {
        try {
            const history = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            setSearchHistory(history.slice(0, 5)); // Keep only last 5 searches
        } catch (error) {
            console.error('Failed to load search history:', error);
        }
    };

    const loadSavedSearches = () => {
        try {
            const saved = JSON.parse(localStorage.getItem('savedSearches') || '[]');
            setSavedSearches(saved);
        } catch (error) {
            console.error('Failed to load saved searches:', error);
        }
    };

    const hasActiveFilters = () => {
        return (
            filters.dateRange.preset !== '30' ||
            filters.dateRange.start ||
            filters.dateRange.end ||
            filters.glucoseRange.enabled ||
            filters.conditions.length > 0 ||
            filters.ageRange.enabled ||
            filters.gender ||
            filters.readingType ||
            filters.timeInRangeFilter.enabled ||
            filters.riskLevel
        );
    };

    const performSearch = async () => {
        setIsSearching(true);
        const startTime = Date.now();

        try {
            await neo4jService.connect();
            let results;

            switch (searchType) {
                case 'patients':
                    results = await searchPatients();
                    break;
                case 'readings':
                    results = await searchReadings();
                    break;
                case 'conditions':
                    results = await searchConditions();
                    break;
                case 'medications':
                    results = await searchMedications();
                    break;
                default:
                    results = await searchAll();
            }

            const searchTime = Date.now() - startTime;
            setSearchResults({ ...results, searchTime });

            // Save to search history
            if (searchQuery.trim()) {
                saveToHistory(searchQuery, searchType, results.totalCount);
            }

            // Update parent component
            if (onResultsUpdate) {
                onResultsUpdate(results);
            }

        } catch (error) {
            console.error('Search failed:', error);
            setSearchResults({ patients: [], readings: [], totalCount: 0, searchTime: 0 });
        } finally {
            setIsSearching(false);
        }
    };

    const buildWhereClause = () => {
        const conditions = [];
        const parameters = {};

        // Date range filter
        if (filters.dateRange.start || filters.dateRange.end || filters.dateRange.preset !== '30') {
            if (filters.dateRange.preset !== 'custom') {
                conditions.push('g.timestamp >= datetime() - duration({days: $days})');
                parameters.days = parseInt(filters.dateRange.preset);
            } else {
                if (filters.dateRange.start) {
                    conditions.push('g.timestamp >= datetime($startDate)');
                    parameters.startDate = filters.dateRange.start;
                }
                if (filters.dateRange.end) {
                    conditions.push('g.timestamp <= datetime($endDate)');
                    parameters.endDate = filters.dateRange.end;
                }
            }
        }

        // Glucose range filter
        if (filters.glucoseRange.enabled) {
            if (filters.glucoseRange.min) {
                conditions.push('g.glucose >= $minGlucose');
                parameters.minGlucose = parseFloat(filters.glucoseRange.min);
            }
            if (filters.glucoseRange.max) {
                conditions.push('g.glucose <= $maxGlucose');
                parameters.maxGlucose = parseFloat(filters.glucoseRange.max);
            }
        }

        // Age range filter
        if (filters.ageRange.enabled) {
            if (filters.ageRange.min) {
                conditions.push('p.age >= $minAge');
                parameters.minAge = parseInt(filters.ageRange.min);
            }
            if (filters.ageRange.max) {
                conditions.push('p.age <= $maxAge');
                parameters.maxAge = parseInt(filters.ageRange.max);
            }
        }

        // Gender filter
        if (filters.gender) {
            conditions.push('p.gender = $gender');
            parameters.gender = filters.gender;
        }

        // Reading type filter
        if (filters.readingType) {
            conditions.push('g.readingType = $readingType');
            parameters.readingType = filters.readingType;
        }

        // Conditions filter
        if (filters.conditions.length > 0) {
            conditions.push('p.condition IN $conditions');
            parameters.conditions = filters.conditions;
        }

        return { conditions, parameters };
    };

    const searchPatients = async () => {
        const { conditions, parameters } = buildWhereClause();

        let whereClause = conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '';

        // Add text search if provided
        if (searchQuery.trim()) {
            const textCondition = '(toLower(p.name) CONTAINS toLower($searchText) OR toLower(p.condition) CONTAINS toLower($searchText) OR p.patientId CONTAINS $searchText)';
            whereClause = whereClause
                ? whereClause + ' AND ' + textCondition
                : 'WHERE ' + textCondition;
            parameters.searchText = searchQuery.trim();
        }

        const query = `
      MATCH (p:Patient)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      ${whereClause}
      WITH p,
           count(g) as readingCount,
           avg(g.glucose) as avgGlucose,
           max(g.timestamp) as lastReading,
           min(g.glucose) as minGlucose,
           max(g.glucose) as maxGlucose
      ${filters.timeInRangeFilter.enabled ? this.buildTimeInRangeFilter() : ''}
      RETURN
        p.patientId as patientId,
        p.name as name,
        p.condition as condition,
        p.age as age,
        p.gender as gender,
        p.insurance as insurance,
        readingCount,
        avgGlucose,
        lastReading,
        minGlucose,
        maxGlucose
      ORDER BY ${this.getSortClause()}
      LIMIT 100
    `;

        const patients = await neo4jService.runQuery(query, parameters);

        return {
            patients: patients,
            readings: [],
            totalCount: patients.length
        };
    };

    const searchReadings = async () => {
        const { conditions, parameters } = buildWhereClause();

        let whereClause = conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : '';

        const query = `
      MATCH (p:Patient)-[:HAD_READING]->(g:GlucoseReading)
      ${whereClause}
      RETURN
        p.patientId as patientId,
        p.name as patientName,
        g.timestamp as timestamp,
        g.glucose as glucose,
        g.readingType as readingType
      ORDER BY g.timestamp DESC
      LIMIT 500
    `;

        const readings = await neo4jService.runQuery(query, parameters);

        return {
            patients: [],
            readings: readings,
            totalCount: readings.length
        };
    };

    const searchConditions = async () => {
        const query = `
      MATCH (p:Patient)
      WHERE toLower(p.condition) CONTAINS toLower($searchText)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      WHERE g.timestamp >= datetime() - duration({days: 30})
      WITH p, count(g) as recentReadings
      RETURN
        p.patientId as patientId,
        p.name as name,
        p.condition as condition,
        p.age as age,
        p.gender as gender,
        recentReadings
      ORDER BY p.name
      LIMIT 100
    `;

        const patients = await neo4jService.runQuery(query, { searchText: searchQuery.trim() });

        return {
            patients: patients,
            readings: [],
            totalCount: patients.length
        };
    };

    const searchMedications = async () => {
        const query = `
      MATCH (p:Patient)-[:TAKES_MEDICATION]->(m:Medication)
      WHERE toLower(m.name) CONTAINS toLower($searchText)
      OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
      WHERE g.timestamp >= datetime() - duration({days: 30})
      WITH p, m, count(g) as recentReadings
      RETURN DISTINCT
        p.patientId as patientId,
        p.name as name,
        p.condition as condition,
        p.age as age,
        collect(m.name) as medications,
        recentReadings
      ORDER BY p.name
      LIMIT 100
    `;

        const patients = await neo4jService.runQuery(query, { searchText: searchQuery.trim() });

        return {
            patients: patients,
            readings: [],
            totalCount: patients.length
        };
    };

    const searchAll = async () => {
        // Perform all searches and combine results
        const [patientResults, readingResults] = await Promise.all([
            searchPatients(),
            searchReadings()
        ]);

        return {
            patients: patientResults.patients,
            readings: readingResults.readings.slice(0, 100), // Limit readings
            totalCount: patientResults.totalCount + readingResults.totalCount
        };
    };

    const getSortClause = () => {
        const orderDirection = filters.sortOrder.toUpperCase();
        switch (filters.sortBy) {
            case 'name':
                return `p.name ${orderDirection}`;
            case 'age':
                return `p.age ${orderDirection}`;
            case 'lastReading':
                return `lastReading ${orderDirection}`;
            case 'avgGlucose':
                return `avgGlucose ${orderDirection}`;
            case 'readingCount':
                return `readingCount ${orderDirection}`;
            default:
                return `lastReading DESC`;
        }
    };

    const buildTimeInRangeFilter = () => {
        // This would calculate TIR on the fly - simplified for demo
        return `
      WITH p, readingCount, avgGlucose, lastReading, minGlucose, maxGlucose,
           sum(CASE WHEN g.glucose >= 70 AND g.glucose <= 180 THEN 1 ELSE 0 END) as inRangeCount
      WITH p, readingCount, avgGlucose, lastReading, minGlucose, maxGlucose,
           (inRangeCount * 100.0 / readingCount) as timeInRange
      WHERE timeInRange >= ${filters.timeInRangeFilter.min || 0}
        AND timeInRange <= ${filters.timeInRangeFilter.max || 100}
    `;
    };

    const saveToHistory = (query, type, resultCount) => {
        const historyItem = {
            query,
            type,
            resultCount,
            timestamp: new Date().toISOString()
        };

        const updatedHistory = [historyItem, ...searchHistory.filter(h => h.query !== query)].slice(0, 5);
        setSearchHistory(updatedHistory);

        try {
            localStorage.setItem('searchHistory', JSON.stringify(updatedHistory));
        } catch (error) {
            console.error('Failed to save search history:', error);
        }
    };

    const saveCurrentSearch = () => {
        const searchName = prompt('Enter a name for this saved search:');
        if (!searchName) return;

        const savedSearch = {
            id: Date.now(),
            name: searchName,
            query: searchQuery,
            type: searchType,
            filters: { ...filters },
            createdAt: new Date().toISOString()
        };

        const updatedSaved = [...savedSearches, savedSearch];
        setSavedSearches(updatedSaved);

        try {
            localStorage.setItem('savedSearches', JSON.stringify(updatedSaved));
        } catch (error) {
            console.error('Failed to save search:', error);
        }
    };

    const loadSavedSearch = (savedSearch) => {
        setSearchQuery(savedSearch.query);
        setSearchType(savedSearch.type);
        setFilters(savedSearch.filters);
    };

    const clearAllFilters = () => {
        setSearchQuery('');
        setFilters({
            dateRange: { start: '', end: '', preset: '30' },
            glucoseRange: { min: '', max: '', enabled: false },
            conditions: [],
            ageRange: { min: '', max: '', enabled: false },
            gender: '',
            readingType: '',
            timeInRangeFilter: { min: '', max: '', enabled: false },
            riskLevel: '',
            sortBy: 'lastReading',
            sortOrder: 'desc'
        });
    };

    const renderBasicSearch = () => (
        <div className="basic-search">
            <div className="search-input-container">
                <input
                    type="text"
                    className="search-input"
                    placeholder="Search patients, conditions, medications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                />
                <div className="search-controls">
                    <select
                        value={searchType}
                        onChange={(e) => setSearchType(e.target.value)}
                        className="search-type-select"
                    >
                        <option value="all">All</option>
                        <option value="patients">Patients</option>
                        <option value="readings">Readings</option>
                        <option value="conditions">Conditions</option>
                        <option value="medications">Medications</option>
                    </select>

                    <button
                        className="advanced-toggle"
                        onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    >
                        🔧 {showAdvancedFilters ? 'Hide' : 'Advanced'}
                    </button>
                </div>
            </div>
        </div>
    );

    const renderAdvancedFilters = () => (
        <div className={`advanced-filters ${showAdvancedFilters ? 'expanded' : ''}`}>
            <div className="filters-grid">
                {/* Date Range */}
                <div className="filter-group">
                    <label>📅 Date Range</label>
                    <select
                        value={filters.dateRange.preset}
                        onChange={(e) => setFilters(prev => ({
                            ...prev,
                            dateRange: { ...prev.dateRange, preset: e.target.value }
                        }))}
                    >
                        <option value="7">Last 7 days</option>
                        <option value="14">Last 14 days</option>
                        <option value="30">Last 30 days</option>
                        <option value="90">Last 90 days</option>
                        <option value="180">Last 6 months</option>
                        <option value="custom">Custom Range</option>
                    </select>

                    {filters.dateRange.preset === 'custom' && (
                        <div className="date-range-inputs">
                            <input
                                type="date"
                                value={filters.dateRange.start}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    dateRange: { ...prev.dateRange, start: e.target.value }
                                }))}
                            />
                            <input
                                type="date"
                                value={filters.dateRange.end}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    dateRange: { ...prev.dateRange, end: e.target.value }
                                }))}
                            />
                        </div>
                    )}
                </div>

                {/* Glucose Range */}
                <div className="filter-group">
                    <label>
                        <input
                            type="checkbox"
                            checked={filters.glucoseRange.enabled}
                            onChange={(e) => setFilters(prev => ({
                                ...prev,
                                glucoseRange: { ...prev.glucoseRange, enabled: e.target.checked }
                            }))}
                        />
                        🩸 Glucose Range (mg/dL)
                    </label>
                    {filters.glucoseRange.enabled && (
                        <div className="range-inputs">
                            <input
                                type="number"
                                placeholder="Min"
                                value={filters.glucoseRange.min}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    glucoseRange: { ...prev.glucoseRange, min: e.target.value }
                                }))}
                            />
                            <input
                                type="number"
                                placeholder="Max"
                                value={filters.glucoseRange.max}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    glucoseRange: { ...prev.glucoseRange, max: e.target.value }
                                }))}
                            />
                        </div>
                    )}
                </div>

                {/* Age Range */}
                <div className="filter-group">
                    <label>
                        <input
                            type="checkbox"
                            checked={filters.ageRange.enabled}
                            onChange={(e) => setFilters(prev => ({
                                ...prev,
                                ageRange: { ...prev.ageRange, enabled: e.target.checked }
                            }))}
                        />
                        👤 Age Range
                    </label>
                    {filters.ageRange.enabled && (
                        <div className="range-inputs">
                            <input
                                type="number"
                                placeholder="Min age"
                                value={filters.ageRange.min}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, min: e.target.value }
                                }))}
                            />
                            <input
                                type="number"
                                placeholder="Max age"
                                value={filters.ageRange.max}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, max: e.target.value }
                                }))}
                            />
                        </div>
                    )}
                </div>

                {/* Gender */}
                <div className="filter-group">
                    <label>⚤ Gender</label>
                    <select
                        value={filters.gender}
                        onChange={(e) => setFilters(prev => ({ ...prev, gender: e.target.value }))}
                    >
                        <option value="">Any</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                    </select>
                </div>

                {/* Conditions */}
                <div className="filter-group">
                    <label>🏥 Conditions</label>
                    <select
                        multiple
                        value={filters.conditions}
                        onChange={(e) => setFilters(prev => ({
                            ...prev,
                            conditions: Array.from(e.target.selectedOptions, option => option.value)
                        }))}
                        className="conditions-select"
                    >
                        {availableConditions.map(condition => (
                            <option key={condition} value={condition}>
                                {condition}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Time in Range Filter */}
                <div className="filter-group">
                    <label>
                        <input
                            type="checkbox"
                            checked={filters.timeInRangeFilter.enabled}
                            onChange={(e) => setFilters(prev => ({
                                ...prev,
                                timeInRangeFilter: { ...prev.timeInRangeFilter, enabled: e.target.checked }
                            }))}
                        />
                        🎯 Time in Range (%)
                    </label>
                    {filters.timeInRangeFilter.enabled && (
                        <div className="range-inputs">
                            <input
                                type="number"
                                placeholder="Min %"
                                min="0"
                                max="100"
                                value={filters.timeInRangeFilter.min}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    timeInRangeFilter: { ...prev.timeInRangeFilter, min: e.target.value }
                                }))}
                            />
                            <input
                                type="number"
                                placeholder="Max %"
                                min="0"
                                max="100"
                                value={filters.timeInRangeFilter.max}
                                onChange={(e) => setFilters(prev => ({
                                    ...prev,
                                    timeInRangeFilter: { ...prev.timeInRangeFilter, max: e.target.value }
                                }))}
                            />
                        </div>
                    )}
                </div>

                {/* Sort Options */}
                <div className="filter-group">
                    <label>📊 Sort By</label>
                    <div className="sort-controls">
                        <select
                            value={filters.sortBy}
                            onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                        >
                            <option value="lastReading">Last Reading</option>
                            <option value="name">Name</option>
                            <option value="age">Age</option>
                            <option value="avgGlucose">Avg Glucose</option>
                            <option value="readingCount">Reading Count</option>
                        </select>
                        <select
                            value={filters.sortOrder}
                            onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value }))}
                        >
                            <option value="asc">Ascending</option>
                            <option value="desc">Descending</option>
                        </select>
                    </div>
                </div>
            </div>

            <div className="filter-actions">
                <button onClick={clearAllFilters} className="clear-filters-btn">
                    🗑️ Clear All
                </button>
                <button onClick={saveCurrentSearch} className="save-search-btn">
                    💾 Save Search
                </button>
            </div>
        </div>
    );

    const renderSearchHistory = () => (
        <div className="search-history">
            <h4>Recent Searches</h4>
            {searchHistory.map((item, index) => (
                <div key={index} className="history-item" onClick={() => setSearchQuery(item.query)}>
                    <span className="history-query">{item.query}</span>
                    <span className="history-meta">
                        {item.type} • {item.resultCount} results
                    </span>
                </div>
            ))}
        </div>
    );

    const renderSavedSearches = () => (
        <div className="saved-searches">
            <h4>Saved Searches</h4>
            {savedSearches.map((search) => (
                <div key={search.id} className="saved-search-item">
                    <div className="saved-search-info" onClick={() => loadSavedSearch(search)}>
                        <span className="saved-search-name">{search.name}</span>
                        <span className="saved-search-query">{search.query}</span>
                    </div>
                    <button
                        className="delete-saved-search"
                        onClick={() => {
                            const updated = savedSearches.filter(s => s.id !== search.id);
                            setSavedSearches(updated);
                            localStorage.setItem('savedSearches', JSON.stringify(updated));
                        }}
                    >
                        ✖️
                    </button>
                </div>
            ))}
        </div>
    );

    const renderSearchResults = () => (
        <div className="search-results">
            <div className="results-header">
                <h3>Search Results</h3>
                <div className="results-meta">
                    {isSearching ? (
                        <span className="searching">🔍 Searching...</span>
                    ) : (
                        <span>
                            {searchResults.totalCount} results in {searchResults.searchTime}ms
                        </span>
                    )}
                </div>
            </div>

            {/* Patient Results */}
            {searchResults.patients.length > 0 && (
                <div className="results-section">
                    <h4>👥 Patients ({searchResults.patients.length})</h4>
                    <div className="patient-results">
                        {searchResults.patients.map(patient => (
                            <div
                                key={patient.patientId}
                                className="patient-result-card"
                                onClick={() => onPatientSelect && onPatientSelect(patient)}
                            >
                                <div className="patient-info">
                                    <h5>{patient.name}</h5>
                                    <p className="patient-condition">{patient.condition}</p>
                                    <div className="patient-meta">
                                        <span>Age: {patient.age}</span>
                                        <span>Gender: {patient.gender}</span>
                                        {patient.readingCount && (
                                            <span>📊 {patient.readingCount} readings</span>
                                        )}
                                    </div>
                                </div>
                                <div className="patient-stats">
                                    {patient.avgGlucose && (
                                        <div className="stat">
                                            <span className="stat-label">Avg Glucose</span>
                                            <span className="stat-value">{Math.round(patient.avgGlucose)} mg/dL</span>
                                        </div>
                                    )}
                                    {patient.lastReading && (
                                        <div className="stat">
                                            <span className="stat-label">Last Reading</span>
                                            <span className="stat-value">
                                                {new Date(patient.lastReading).toLocaleDateString()}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Reading Results */}
            {searchResults.readings.length > 0 && (
                <div className="results-section">
                    <h4>📈 Readings ({searchResults.readings.length})</h4>
                    <div className="reading-results">
                        <table className="readings-table">
                            <thead>
                                <tr>
                                    <th>Patient</th>
                                    <th>Date/Time</th>
                                    <th>Glucose</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {searchResults.readings.slice(0, 50).map((reading, index) => (
                                    <tr key={index}>
                                        <td>{reading.patientName}</td>
                                        <td>{new Date(reading.timestamp).toLocaleString()}</td>
                                        <td className={`glucose-value ${reading.glucose < 70 ? 'low' :
                                                reading.glucose > 180 ? 'high' : 'normal'
                                            }`}>
                                            {reading.glucose} mg/dL
                                        </td>
                                        <td>{reading.readingType}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                        {searchResults.readings.length > 50 && (
                            <p className="results-truncated">
                                Showing first 50 readings of {searchResults.readings.length} total
                            </p>
                        )}
                    </div>
                </div>
            )}

            {!isSearching && searchResults.totalCount === 0 && (searchQuery || hasActiveFilters()) && (
                <div className="no-results">
                    <h4>No results found</h4>
                    <p>Try adjusting your search terms or filters.</p>
                </div>
            )}
        </div>
    );

    return (
        <div className="advanced-search">
            <div className="search-header">
                <h2>🔍 Advanced Search & Filtering</h2>
                <p>Search across patients, readings, conditions, and more with powerful filtering options</p>
            </div>

            <div className="search-layout">
                <div className="search-controls">
                    {renderBasicSearch()}
                    {renderAdvancedFilters()}

                    <div className="search-sidebar">
                        {renderSearchHistory()}
                        {renderSavedSearches()}
                    </div>
                </div>

                <div className="search-content">
                    {renderSearchResults()}
                </div>
            </div>
        </div>
    );
};

export default AdvancedSearch;
