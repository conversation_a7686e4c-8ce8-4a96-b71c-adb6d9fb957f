import { useEffect, useState } from 'react';
import AIRecommendationService from '../services/aiService';
import Neo4jService from '../services/neo4jService';
import { calculateAGPData } from '../utils/agpCalculation';
import { exportAGPReportCSV, exportAGPReportJSON, generateAGPReport } from '../utils/agpReporting';
import AG<PERSON>hart from './AGPChart';
import AGPStatistics from './AGPStatistics';
import AIControlPanel from './AIControlPanel';
import ConfirmationModal from './ConfirmationModal';
import InputModal from './InputModal';
import { useConfirmation, useInputModal } from '../hooks/useConfirmation';
import { SCHEMA_QUERY_TEMPLATES, SCHEMA_NODE_QUERIES, SCHEMA_RELATIONSHIP_QUERIES } from '../constants/queryTemplates';
import './QueryRunner.css';

function QueryRunner() {
    const [neo4jService] = useState(() => new Neo4jService());
    const [aiService] = useState(() => new AIRecommendationService());
    const [connectionStatus, setConnectionStatus] = useState('disconnected');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // Modal hooks
    const { confirmationState, showConfirmation } = useConfirmation();
    const { inputModalState, showInputModal } = useInputModal();

    // Query state
    const [selectedQueryKey, setSelectedQueryKey] = useState('');
    const [customQuery, setCustomQuery] = useState('');
    const [queryParameters, setQueryParameters] = useState({ patientId: 'Patient_1' });

    // Results state
    const [queryResults, setQueryResults] = useState([]);
    const [filteredResults, setFilteredResults] = useState([]);
    const [sortColumn, setSortColumn] = useState(null);
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(50);
    const [agpData, setAgpData] = useState(null);
    const [showAGPChart, setShowAGPChart] = useState(false);
    const [queryStats, setQueryStats] = useState(null);

    // UI state
    const [activeTab, setActiveTab] = useState('predefined'); // 'predefined', 'custom', 'history', 'bookmarks', or 'ai-control'
    const [showPerformanceStats, setShowPerformanceStats] = useState(false);
    const [agpSettings, setAgpSettings] = useState({
        hypoThreshold: 70,
        hyperThreshold: 180,
        targetMin: 70,
        targetMax: 180
    });

    // Query management state
    const [queryHistory, setQueryHistory] = useState([]);
    const [savedQueries, setSavedQueries] = useState([]);
    const [queryBookmarks, setQueryBookmarks] = useState([]);

    // AI state
    const [aiSettings, setAiSettings] = useState({});
    const [aiRecommendations, setAiRecommendations] = useState(null);
    const [aiInsights, setAiInsights] = useState(null);
    const [aiLoading, setAiLoading] = useState(false);
    const [showAIRecommendations, setShowAIRecommendations] = useState(true);

    const commonQueries = Neo4jService.getCommonQueries();

    useEffect(() => {
        // Auto-connect on component mount
        handleConnect();
    }, []);

    const handleConnect = async () => {
        setLoading(true);
        setError(null);

        try {
            await neo4jService.connect();
            setConnectionStatus('connected');
        } catch (err) {
            setError(err.message);
            setConnectionStatus('error');
        } finally {
            setLoading(false);
        }
    };

    const handleQuerySelect = (queryKey) => {
        setSelectedQueryKey(queryKey);
        const query = commonQueries[queryKey];
        if (query) {
            setCustomQuery(query.query);
            // Auto-execute predefined queries for better UX
            if (connectionStatus === 'connected') {
                executeQuery(query.query, queryParameters);
            }
        }
    };

    const executeQuery = async (query, parameters) => {
        setLoading(true);
        setError(null);
        setQueryResults([]);
        setAgpData(null);
        setShowAGPChart(false);

        try {
            // Enforce read-only safety: block write or administrative Cypher
            const forbidden = /\b(CREATE|MERGE|DELETE|DETACH\s+DELETE|SET\s+|REMOVE\s+|CALL\s+db\.|USE\s+|LOAD\s+CSV|APOC\.\w+\.(?:(?!(toJSON|convert|map|coll|text|math|date|temporal)).)*)\b/i;
            if (forbidden.test(query)) {
                throw new Error('Write or administrative queries are not allowed in this app. This interface is read-only and intended for data exploration and visualization.');
            }

            // Ensure all required parameters have default values
            const safeParameters = {
                ...parameters,
                days: parameters?.days || 30,
                limit: parameters?.limit || 1000
            };

            const startTime = performance.now();
            const results = await neo4jService.executeQuery(query, safeParameters);
            const endTime = performance.now();

            console.log('Query executed with parameters:', safeParameters);
            console.log('Query results received:', results);
            // Normalize results to an array of rows regardless of backend shape
            const rows = Array.isArray(results)
                ? results
                : (results && Array.isArray(results.records) ? results.records : []);

            setQueryResults(rows);
            setFilteredResults(rows);
            setCurrentPage(1);
            setSearchTerm('');
            setSortColumn(null);
            setQueryStats({
                executionTime: Math.round(endTime - startTime),
                recordCount: rows.length,
                timestamp: new Date().toLocaleString()
            });

            // Add to query history
            addToQueryHistory(query, safeParameters, rows.length, endTime - startTime);

            // Check if this is a glucose over time query for AGP generation
            if (selectedQueryKey === 'glucose_over_time' ||
                query.toLowerCase().includes('glucose') &&
                query.toLowerCase().includes('timestamp')) {

                console.log('Attempting AGP calculation with results:', rows);
                const calculatedAGP = calculateAGPData(rows);
                if (calculatedAGP) {
                    console.log('AGP data calculated successfully:', calculatedAGP);
                    setAgpData(calculatedAGP);
                    setShowAGPChart(true);

                    // Generate AI recommendations for AGP data
                    generateAIRecommendations(calculatedAGP, safeParameters);
                } else {
                    console.warn('AGP calculation returned null, check data format');
                }
            } else {
                // Generate AI insights for other query types
                generateAIInsights(rows, selectedQueryKey, safeParameters);
            }

        } catch (err) {
            console.error('Query execution failed:', err);

            // Enhanced error handling with categorization
            const errorInfo = categorizeError(err);
            setError(errorInfo);
        } finally {
            setLoading(false);
        }
    };

    const categorizeError = (err) => {
        const message = err.message || 'Unknown error occurred';

        // Connection errors
        if (message.includes('connection') || message.includes('connect') ||
            message.includes('network') || message.includes('timeout')) {
            return {
                type: 'connection',
                title: 'Connection Error',
                message: 'Unable to connect to the database. Please check your connection.',
                suggestion: 'Try reconnecting to the database or check your network connection.',
                icon: '🔌',
                retryable: true,
                originalError: message
            };
        }

        // Syntax errors
        if (message.includes('syntax') || message.includes('Invalid input') ||
            message.includes('expected') || message.includes('SyntaxError')) {
            return {
                type: 'syntax',
                title: 'Query Syntax Error',
                message: 'There is a syntax error in your Cypher query.',
                suggestion: 'Check your query syntax. Ensure all brackets, quotes, and keywords are correct.',
                icon: '📝',
                retryable: false,
                originalError: message
            };
        }

        // Permission/Security errors
        if (message.includes('not allowed') || message.includes('permission') ||
            message.includes('unauthorized') || message.includes('forbidden')) {
            return {
                type: 'security',
                title: 'Security Restriction',
                message: 'This operation is not permitted.',
                suggestion: 'Only read-only queries are allowed. Remove any CREATE, DELETE, SET, or other write operations.',
                icon: '🔒',
                retryable: false,
                originalError: message
            };
        }

        // Performance/Timeout errors
        if (message.includes('timeout') || message.includes('slow') ||
            message.includes('memory') || message.includes('limit exceeded')) {
            return {
                type: 'performance',
                title: 'Performance Issue',
                message: 'The query took too long to execute or used too much memory.',
                suggestion: 'Try adding LIMIT clauses, using more specific WHERE conditions, or simplifying your query.',
                icon: '⏱️',
                retryable: true,
                originalError: message
            };
        }

        // Data/Node not found errors
        if (message.includes('not found') || message.includes('does not exist') ||
            message.includes('no such') || message.includes('missing')) {
            return {
                type: 'data',
                title: 'Data Not Found',
                message: 'The requested data could not be found.',
                suggestion: 'Check that the specified nodes, relationships, or properties exist in the database.',
                icon: '🔍',
                retryable: false,
                originalError: message
            };
        }

        // API/Server errors
        if (message.includes('API') || message.includes('server') ||
            message.includes('503') || message.includes('500') || message.includes('502')) {
            return {
                type: 'server',
                title: 'Server Error',
                message: 'There was an error on the server side.',
                suggestion: 'This is likely a temporary issue. Please try again in a few moments.',
                icon: '🖥️',
                retryable: true,
                originalError: message
            };
        }

        // Generic error
        return {
            type: 'unknown',
            title: 'Unexpected Error',
            message: 'An unexpected error occurred.',
            suggestion: 'Please try again. If the problem persists, check your query or contact support.',
            icon: '❌',
            retryable: true,
            originalError: message
        };
    };

    const retryQuery = () => {
        if (selectedQueryKey) {
            const query = commonQueries[selectedQueryKey];
            if (query) {
                executeQuery(query.query, queryParameters);
            }
        } else if (customQuery.trim()) {
            executeQuery(customQuery, queryParameters);
        }
    };

    // Query management functions
    const addToQueryHistory = (query, parameters, resultCount, executionTime) => {
        const historyItem = {
            id: Date.now() + Math.random(),
            query: query.trim(),
            parameters,
            resultCount,
            executionTime,
            timestamp: new Date().toISOString(),
            status: 'success'
        };

        setQueryHistory(prev => {
            const newHistory = [historyItem, ...prev];
            // Keep only the last 50 queries
            return newHistory.slice(0, 50);
        });

        // Save to localStorage
        try {
            const historyForStorage = [historyItem, ...queryHistory].slice(0, 50);
            localStorage.setItem('neo4j-query-history', JSON.stringify(historyForStorage));
        } catch (e) {
            console.warn('Could not save query history to localStorage:', e);
        }
    };

    const saveQuery = (name, query, parameters) => {
        const savedQuery = {
            id: Date.now() + Math.random(),
            name: name.trim(),
            query: query.trim(),
            parameters,
            createdAt: new Date().toISOString()
        };

        setSavedQueries(prev => {
            const newSaved = [...prev, savedQuery];
            // Save to localStorage
            try {
                localStorage.setItem('neo4j-saved-queries', JSON.stringify(newSaved));
            } catch (e) {
                console.warn('Could not save query to localStorage:', e);
            }
            return newSaved;
        });
    };

    const deleteQuery = (queryId) => {
        setSavedQueries(prev => {
            const newSaved = prev.filter(q => q.id !== queryId);
            try {
                localStorage.setItem('neo4j-saved-queries', JSON.stringify(newSaved));
            } catch (e) {
                console.warn('Could not update saved queries in localStorage:', e);
            }
            return newSaved;
        });
    };

    const bookmarkQuery = (query, parameters, name) => {
        const bookmark = {
            id: Date.now() + Math.random(),
            name: name || `Bookmark ${queryBookmarks.length + 1}`,
            query: query.trim(),
            parameters,
            bookmarkedAt: new Date().toISOString()
        };

        setQueryBookmarks(prev => {
            const newBookmarks = [...prev, bookmark];
            try {
                localStorage.setItem('neo4j-query-bookmarks', JSON.stringify(newBookmarks));
            } catch (e) {
                console.warn('Could not save bookmark to localStorage:', e);
            }
            return newBookmarks;
        });
    };

    const removeBookmark = (bookmarkId) => {
        setQueryBookmarks(prev => {
            const newBookmarks = prev.filter(b => b.id !== bookmarkId);
            try {
                localStorage.setItem('neo4j-query-bookmarks', JSON.stringify(newBookmarks));
            } catch (e) {
                console.warn('Could not update bookmarks in localStorage:', e);
            }
            return newBookmarks;
        });
    };

    // Load saved data from localStorage on mount
    useEffect(() => {
        try {
            const savedHistory = localStorage.getItem('neo4j-query-history');
            if (savedHistory) {
                setQueryHistory(JSON.parse(savedHistory));
            }

            const savedQueries = localStorage.getItem('neo4j-saved-queries');
            if (savedQueries) {
                setSavedQueries(JSON.parse(savedQueries));
            }

            const bookmarks = localStorage.getItem('neo4j-query-bookmarks');
            if (bookmarks) {
                setQueryBookmarks(JSON.parse(bookmarks));
            }
        } catch (e) {
            console.warn('Could not load saved queries from localStorage:', e);
        }
    }, []);
});

const handleExecuteQuery = async () => {
    if (connectionStatus !== 'connected') {
        setError('Please connect to Neo4j first');
        return;
    }

    if (!customQuery.trim()) {
        setError('Please enter a Cypher query');
        return;
    }

    await executeQuery(customQuery, queryParameters);
};

const handleParameterChange = (key, value) => {
    setQueryParameters(prev => ({
        ...prev,
        [key]: value
    }));
};

// Results management functions
const handleSort = (column) => {
    if (sortColumn === column) {
        setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
        setSortColumn(column);
        setSortDirection('asc');
    }
};

const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
};

const handlePageSizeChange = (size) => {
    setPageSize(size);
    setCurrentPage(1);
};

// Apply filtering and sorting
useEffect(() => {
    let filtered = [...queryResults];

    // Apply search filter
    if (searchTerm) {
        filtered = filtered.filter(row =>
            Object.values(row).some(value =>
                String(value).toLowerCase().includes(searchTerm.toLowerCase())
            )
        );
    }

    // Apply sorting
    if (sortColumn && filtered.length > 0) {
        filtered.sort((a, b) => {
            const aVal = a[sortColumn];
            const bVal = b[sortColumn];

            if (aVal === null || aVal === undefined) return 1;
            if (bVal === null || bVal === undefined) return -1;

            if (typeof aVal === 'number' && typeof bVal === 'number') {
                return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
            }

            const aStr = String(aVal).toLowerCase();
            const bStr = String(bVal).toLowerCase();

            if (sortDirection === 'asc') {
                return aStr.localeCompare(bStr);
            } else {
                return bStr.localeCompare(aStr);
            }
        });
    }

    setFilteredResults(filtered);
}, [queryResults, searchTerm, sortColumn, sortDirection]);

// Export functionality
const exportResults = (format) => {
    if (!queryResults.length) return;

    let content;
    let filename;
    let mimeType;

    if (format === 'json') {
        content = JSON.stringify(queryResults, null, 2);
        filename = `neo4j_results_${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
    } else if (format === 'csv') {
        // Convert results to CSV
        if (queryResults.length === 0) return;

        // Get all unique keys from all results
        const allKeys = new Set();
        queryResults.forEach(result => {
            Object.keys(result).forEach(key => allKeys.add(key));
        });

        const headers = Array.from(allKeys);
        const csvContent = [
            headers.join(','),
            ...queryResults.map(result =>
                headers.map(header => {
                    let value = result[header];
                    if (value === null || value === undefined) return '';
                    if (typeof value === 'object') value = JSON.stringify(value);
                    // Escape quotes and wrap in quotes if contains comma
                    value = String(value).replace(/"/g, '""');
                    return value.includes(',') ? `"${value}"` : value;
                }).join(',')
            )
        ].join('\n');

        content = csvContent;
        filename = `neo4j_results_${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
    }

    // Create and trigger download
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
};

// AI Functions
const generateAIRecommendations = async (agpData, patientContext = {}) => {
    if (!aiSettings.enabled || !showAIRecommendations) return;

    setAiLoading(true);
    try {
        console.log('🤖 Generating AI recommendations for AGP data...');
        const recommendations = await aiService.generateAGPRecommendations(agpData, {
            patientId: patientContext.patientId || queryParameters.patientId,
            ...patientContext
        });
        setAiRecommendations(recommendations);
        console.log('✅ AI recommendations generated:', recommendations);
    } catch (error) {
        console.error('❌ Error generating AI recommendations:', error);
        setAiRecommendations({
            error: true,
            message: 'Failed to generate AI recommendations',
            fallback: true
        });
    } finally {
        setAiLoading(false);
    }
};

const generateAIInsights = async (queryResults, queryType, patientContext = {}) => {
    if (!aiSettings.enabled || !showAIRecommendations) return;

    setAiLoading(true);
    try {
        console.log('🤖 Generating AI insights for query results...');
        const insights = await aiService.generateClinicalInsights(queryResults, queryType, {
            patientId: patientContext.patientId || queryParameters.patientId,
            queryType: queryType,
            ...patientContext
        });
        setAiInsights(insights);
        console.log('✅ AI insights generated:', insights);
    } catch (error) {
        console.error('❌ Error generating AI insights:', error);
        setAiInsights({
            error: true,
            message: 'Failed to generate AI insights',
            fallback: true
        });
    } finally {
        setAiLoading(false);
    }
};

const handleAISettingsChange = (newSettings) => {
    setAiSettings(newSettings);
    console.log('🔧 AI settings updated:', newSettings);
};

const toggleAIRecommendations = () => {
    setShowAIRecommendations(!showAIRecommendations);
};

const refreshAIRecommendations = () => {
    if (agpData) {
        generateAIRecommendations(agpData, queryParameters);
    } else if (queryResults.length > 0) {
        generateAIInsights(queryResults, selectedQueryKey, queryParameters);
    }
};

return (
    <div className="query-runner">
        <div className="query-runner-header">
            <h2>Neo4j Query Runner</h2>
            <div className="header-controls">
                <div className={`connection-status ${connectionStatus}`}>
                    <span className="status-indicator"></span>
                    {connectionStatus === 'connected' ? 'Connected' :
                        connectionStatus === 'error' ? 'Error' : 'Disconnected'}
                </div>
                <button
                    onClick={() => setShowPerformanceStats(!showPerformanceStats)}
                    className="stats-button"
                    title="View Performance Statistics"
                >
                    📊 Stats
                </button>
                <button
                    onClick={handleConnect}
                    disabled={loading || connectionStatus === 'connected'}
                    className="connect-button"
                >
                    {loading ? 'Connecting...' : 'Connect'}
                </button>
            </div>
        </div>

        {error && (
            <div className={`enhanced-error-message ${error.type || 'unknown'}`}>
                <div className="error-header">
                    <span className="error-icon">{typeof error === 'object' ? error.icon : '❌'}</span>
                    <h4 className="error-title">{typeof error === 'object' ? error.title : 'Error'}</h4>
                    {typeof error === 'object' && error.retryable && (
                        <button onClick={retryQuery} className="retry-button" disabled={loading}>
                            {loading ? '🔄' : '🔄'} Retry
                        </button>
                    )}
                    <button onClick={() => setError(null)} className="dismiss-button">
                        ✖️
                    </button>
                </div>
                <div className="error-content">
                    <p className="error-message">{typeof error === 'object' ? error.message : error}</p>
                    {typeof error === 'object' && error.suggestion && (
                        <div className="error-suggestion">
                            <strong>💡 Suggestion:</strong> {error.suggestion}
                        </div>
                    )}
                    {typeof error === 'object' && error.originalError && (
                        <details className="error-details">
                            <summary>Technical Details</summary>
                            <pre className="error-trace">{error.originalError}</pre>
                        </details>
                    )}
                </div>
            </div>
        )}

        {showPerformanceStats && (
            <div className="performance-stats">
                <h3>Performance Statistics</h3>
                <div className="stats-grid">
                    {(() => {
                        const stats = neo4jService.getPerformanceStats();
                        return (
                            <>
                                <div className="stat-card">
                                    <h4>Total Queries</h4>
                                    <p>{stats.totalQueries}</p>
                                </div>
                                <div className="stat-card">
                                    <h4>Avg Execution Time</h4>
                                    <p>{stats.avgExecutionTime.toFixed(2)}ms</p>
                                </div>
                                <div className="stat-card">
                                    <h4>Cache Hit Rate</h4>
                                    <p>{stats.cacheHitRate}</p>
                                </div>
                                <div className="stat-card">
                                    <h4>Cache Size</h4>
                                    <p>{stats.cacheSize} entries</p>
                                </div>
                                {stats.slowQueries.length > 0 && (
                                    <div className="stat-card full-width">
                                        <h4>Recent Slow Queries (&gt;1s)</h4>
                                        <div className="slow-queries">
                                            {stats.slowQueries.slice(-3).map((query, index) => (
                                                <div key={index} className="slow-query">
                                                    <code>{query.query}...</code>
                                                    <span className="execution-time">{query.executionTime}ms</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </>
                        );
                    })()}
                </div>
                <div className="stats-actions">
                    <button
                        onClick={() => neo4jService.clearPerformanceStats()}
                        className="clear-stats-button"
                    >
                        Clear Statistics
                    </button>
                </div>
            </div>
        )}

        <div className="query-tabs">
            <button
                className={`tab-button ${activeTab === 'predefined' ? 'active' : ''}`}
                onClick={() => setActiveTab('predefined')}
            >
                Predefined Queries
            </button>
            <button
                className={`tab-button ${activeTab === 'custom' ? 'active' : ''}`}
                onClick={() => setActiveTab('custom')}
            >
                Custom Query
            </button>
            <button
                className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
                onClick={() => setActiveTab('history')}
            >
                📜 History ({queryHistory.length})
            </button>
            <button
                className={`tab-button ${activeTab === 'saved' ? 'active' : ''}`}
                onClick={() => setActiveTab('saved')}
            >
                💾 Saved ({savedQueries.length})
            </button>
            <button
                className={`tab-button ${activeTab === 'bookmarks' ? 'active' : ''}`}
                onClick={() => setActiveTab('bookmarks')}
            >
                🔖 Bookmarks ({queryBookmarks.length})
            </button>
            <button
                className={`tab-button ${activeTab === 'schema' ? 'active' : ''}`}
                onClick={() => setActiveTab('schema')}
            >
                🗂️ Schema
            </button>
            <button
                className={`tab-button ${activeTab === 'agp-settings' ? 'active' : ''}`}
                onClick={() => setActiveTab('agp-settings')}
            >
                AGP Settings
            </button>
            <button
                className={`tab-button ${activeTab === 'ai-control' ? 'active' : ''}`}
                onClick={() => setActiveTab('ai-control')}
            >
                🤖 AI Control
            </button>
        </div>

        <div className="query-content">
            {activeTab === 'predefined' && (
                <div className="predefined-queries">
                    <h3>Healthcare & Research Queries</h3>
                    <div className="query-categories">
                        {(() => {
                            // Group queries by category
                            const categories = {};
                            Object.entries(commonQueries).forEach(([key, queryInfo]) => {
                                const category = queryInfo.category || 'General';
                                if (!categories[category]) {
                                    categories[category] = [];
                                }
                                categories[category].push([key, queryInfo]);
                            });

                            return Object.entries(categories).map(([category, queries]) => (
                                <div key={category} className="query-category">
                                    <h4 className="category-header">{category}</h4>
                                    <div className="query-grid">
                                        {queries.map(([key, queryInfo]) => (
                                            <div
                                                key={key}
                                                className={`query-card ${selectedQueryKey === key ? 'selected' : ''}`}
                                            >
                                                <div className="query-card-content" onClick={() => handleQuerySelect(key)}>
                                                    <h5>{queryInfo.name}</h5>
                                                    <p>{queryInfo.description}</p>
                                                    {key.includes('glucose') && (
                                                        <span className="agp-badge">AGP Enabled</span>
                                                    )}
                                                    {queryInfo.parameters && queryInfo.parameters.length > 0 && (
                                                        <div className="parameters-info">
                                                            <small>Parameters: {queryInfo.parameters.join(', ')}</small>
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="query-card-actions">
                                                    <button
                                                        className="execute-button"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            if (connectionStatus === 'connected') {
                                                                setSelectedQueryKey(key);
                                                                setCustomQuery(queryInfo.query);
                                                                executeQuery(queryInfo.query, queryParameters);
                                                            } else {
                                                                setError('Please connect to Neo4j first');
                                                            }
                                                        }}
                                                        disabled={connectionStatus !== 'connected' || loading}
                                                    >
                                                        {loading && selectedQueryKey === key ? '⏳' : '▶️'} Execute
                                                    </button>
                                                    <button
                                                        className="view-button"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleQuerySelect(key);
                                                            setActiveTab('custom');
                                                        }}
                                                    >
                                                        👁️ View
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ));
                        })()}
                    </div>
                </div>
            )}

            {activeTab === 'agp-settings' && (
                <div className="agp-settings-panel">
                    <h3>AGP Configuration</h3>
                    <div className="agp-settings-grid">
                        <div className="setting-group">
                            <h4>Glucose Thresholds</h4>
                            <div className="threshold-inputs">
                                <div className="threshold-input">
                                    <label>Hypoglycemia Threshold:</label>
                                    <input
                                        type="number"
                                        value={agpSettings.hypoThreshold}
                                        onChange={(e) => setAgpSettings(prev => ({
                                            ...prev,
                                            hypoThreshold: parseInt(e.target.value) || 70
                                        }))}
                                        min="50"
                                        max="100"
                                    />
                                    <span className="unit">mg/dL</span>
                                </div>
                                <div className="threshold-input">
                                    <label>Hyperglycemia Threshold:</label>
                                    <input
                                        type="number"
                                        value={agpSettings.hyperThreshold}
                                        onChange={(e) => setAgpSettings(prev => ({
                                            ...prev,
                                            hyperThreshold: parseInt(e.target.value) || 180
                                        }))}
                                        min="150"
                                        max="250"
                                    />
                                    <span className="unit">mg/dL</span>
                                </div>
                            </div>
                        </div>
                        <div className="setting-group">
                            <h4>Target Range</h4>
                            <div className="threshold-inputs">
                                <div className="threshold-input">
                                    <label>Target Range Min:</label>
                                    <input
                                        type="number"
                                        value={agpSettings.targetMin}
                                        onChange={(e) => setAgpSettings(prev => ({
                                            ...prev,
                                            targetMin: parseInt(e.target.value) || 70
                                        }))}
                                        min="50"
                                        max="120"
                                    />
                                    <span className="unit">mg/dL</span>
                                </div>
                                <div className="threshold-input">
                                    <label>Target Range Max:</label>
                                    <input
                                        type="number"
                                        value={agpSettings.targetMax}
                                        onChange={(e) => setAgpSettings(prev => ({
                                            ...prev,
                                            targetMax: parseInt(e.target.value) || 180
                                        }))}
                                        min="150"
                                        max="250"
                                    />
                                    <span className="unit">mg/dL</span>
                                </div>
                            </div>
                        </div>
                        <div className="setting-group">
                            <h4>Preset Configurations</h4>
                            <div className="preset-buttons">
                                <button
                                    onClick={() => setAgpSettings({
                                        hypoThreshold: 70,
                                        hyperThreshold: 180,
                                        targetMin: 70,
                                        targetMax: 180
                                    })}
                                    className="preset-button"
                                >
                                    Standard Adult (70-180)
                                </button>
                                <button
                                    onClick={() => setAgpSettings({
                                        hypoThreshold: 70,
                                        hyperThreshold: 250,
                                        targetMin: 70,
                                        targetMax: 250
                                    })}
                                    className="preset-button"
                                >
                                    Elderly/High Risk (70-250)
                                </button>
                                <button
                                    onClick={() => setAgpSettings({
                                        hypoThreshold: 63,
                                        hyperThreshold: 140,
                                        targetMin: 63,
                                        targetMax: 140
                                    })}
                                    className="preset-button"
                                >
                                    Pediatric (63-140)
                                </button>
                                <button
                                    onClick={() => setAgpSettings({
                                        hypoThreshold: 63,
                                        hyperThreshold: 160,
                                        targetMin: 63,
                                        targetMax: 160
                                    })}
                                    className="preset-button"
                                >
                                    Pregnancy (63-160)
                                </button>
                            </div>
                        </div>
                    </div>
                    <div className="agp-settings-info">
                        <h4>Clinical Guidelines</h4>
                        <ul>
                            <li><strong>Time in Range Goal:</strong> ≥70% in target range</li>
                            <li><strong>Time Below Range:</strong> &lt;4% below 70 mg/dL, &lt;1% below 54 mg/dL</li>
                            <li><strong>Coefficient of Variation:</strong> ≤36% for stable glucose</li>
                            <li><strong>GMI Target:</strong> &lt;7% for most adults with diabetes</li>
                        </ul>
                    </div>
                </div>
            )}

            {activeTab === 'ai-control' && (
                <AIControlPanel
                    onSettingsChange={handleAISettingsChange}
                    currentSettings={aiSettings}
                />
            )}

            {activeTab === 'history' && (
                <div className="query-history-panel">
                    <h3>Query History</h3>
                    {queryHistory.length === 0 ? (
                        <div className="empty-state">
                            <p>No queries executed yet. Start running some queries to build your history!</p>
                        </div>
                    ) : (
                        <div className="history-list">
                            {queryHistory.map((historyItem) => (
                                <div key={historyItem.id} className="history-item">
                                    <div className="history-header">
                                        <span className="history-timestamp">
                                            {new Date(historyItem.timestamp).toLocaleString()}
                                        </span>
                                        <div className="history-stats">
                                            <span className="execution-time">{historyItem.executionTime}ms</span>
                                            <span className="result-count">{historyItem.resultCount} results</span>
                                        </div>
                                    </div>
                                    <div className="history-query">
                                        <pre>{historyItem.query}</pre>
                                    </div>
                                    <div className="history-actions">
                                        <button
                                            onClick={() => {
                                                setCustomQuery(historyItem.query);
                                                setQueryParameters(historyItem.parameters);
                                                setActiveTab('custom');
                                            }}
                                            className="history-action-btn"
                                        >
                                            📝 Edit
                                        </button>
                                        <button
                                            onClick={() => executeQuery(historyItem.query, historyItem.parameters)}
                                            className="history-action-btn"
                                            disabled={loading || connectionStatus !== 'connected'}
                                        >
                                            ▶️ Re-run
                                        </button>
                                        <button
                                            onClick={async () => {
                                                const name = await showInputModal({
                                                    title: 'Save Query',
                                                    message: 'Enter a name for this saved query:',
                                                    placeholder: 'Query name...',
                                                    required: true
                                                });
                                                if (name) saveQuery(name, historyItem.query, historyItem.parameters);
                                            }}
                                            className="history-action-btn"
                                        >
                                            💾 Save
                                        </button>
                                        <button
                                            onClick={async () => {
                                                const name = await showInputModal({
                                                    title: 'Create Bookmark',
                                                    message: 'Enter a name for this bookmark:',
                                                    placeholder: 'Bookmark name...',
                                                    defaultValue: `Bookmark ${queryBookmarks.length + 1}`
                                                });
                                                if (name) bookmarkQuery(historyItem.query, historyItem.parameters, name);
                                            }}
                                            className="history-action-btn"
                                        >
                                            🔖 Bookmark
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}

            {activeTab === 'saved' && (
                <div className="saved-queries-panel">
                    <h3>Saved Queries</h3>
                    {savedQueries.length === 0 ? (
                        <div className="empty-state">
                            <p>No saved queries yet. Save frequently used queries from your history!</p>
                        </div>
                    ) : (
                        <div className="saved-list">
                            {savedQueries.map((savedQuery) => (
                                <div key={savedQuery.id} className="saved-item">
                                    <div className="saved-header">
                                        <h4 className="saved-name">{savedQuery.name}</h4>
                                        <span className="saved-date">
                                            {new Date(savedQuery.createdAt).toLocaleDateString()}
                                        </span>
                                    </div>
                                    <div className="saved-query">
                                        <pre>{savedQuery.query}</pre>
                                    </div>
                                    <div className="saved-actions">
                                        <button
                                            onClick={() => {
                                                setCustomQuery(savedQuery.query);
                                                setQueryParameters(savedQuery.parameters);
                                                setActiveTab('custom');
                                            }}
                                            className="saved-action-btn"
                                        >
                                            📝 Edit
                                        </button>
                                        <button
                                            onClick={() => executeQuery(savedQuery.query, savedQuery.parameters)}
                                            className="saved-action-btn"
                                            disabled={loading || connectionStatus !== 'connected'}
                                        >
                                            ▶️ Run
                                        </button>
                                        <button
                                            onClick={async () => {
                                                const confirmed = await showConfirmation({
                                                    title: 'Delete Saved Query',
                                                    message: `Are you sure you want to delete "${savedQuery.name}"?`,
                                                    confirmText: 'Delete',
                                                    confirmButtonStyle: 'danger'
                                                });
                                                if (confirmed) {
                                                    deleteQuery(savedQuery.id);
                                                }
                                            }}
                                            className="saved-action-btn delete-btn"
                                        >
                                            🗑️ Delete
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}

            {activeTab === 'bookmarks' && (
                <div className="bookmarks-panel">
                    <h3>Query Bookmarks</h3>
                    {queryBookmarks.length === 0 ? (
                        <div className="empty-state">
                            <p>No bookmarks yet. Bookmark your favorite queries for quick access!</p>
                        </div>
                    ) : (
                        <div className="bookmarks-list">
                            {queryBookmarks.map((bookmark) => (
                                <div key={bookmark.id} className="bookmark-item">
                                    <div className="bookmark-header">
                                        <h4 className="bookmark-name">{bookmark.name}</h4>
                                        <span className="bookmark-date">
                                            {new Date(bookmark.bookmarkedAt).toLocaleDateString()}
                                        </span>
                                    </div>
                                    <div className="bookmark-query">
                                        <pre>{bookmark.query}</pre>
                                    </div>
                                    <div className="bookmark-actions">
                                        <button
                                            onClick={() => {
                                                setCustomQuery(bookmark.query);
                                                setQueryParameters(bookmark.parameters);
                                                setActiveTab('custom');
                                            }}
                                            className="bookmark-action-btn"
                                        >
                                            📝 Edit
                                        </button>
                                        <button
                                            onClick={() => executeQuery(bookmark.query, bookmark.parameters)}
                                            className="bookmark-action-btn"
                                            disabled={loading || connectionStatus !== 'connected'}
                                        >
                                            ▶️ Run
                                        </button>
                                        <button
                                            onClick={async () => {
                                                const confirmed = await showConfirmation({
                                                    title: 'Remove Bookmark',
                                                    message: `Are you sure you want to remove bookmark "${bookmark.name}"?`,
                                                    confirmText: 'Remove',
                                                    confirmButtonStyle: 'danger'
                                                });
                                                if (confirmed) {
                                                    removeBookmark(bookmark.id);
                                                }
                                            }}
                                            className="bookmark-action-btn delete-btn"
                                        >
                                            🗑️ Remove
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            )}

            {activeTab === 'schema' && (
                <div className="schema-explorer">
                    <h3>Database Schema Explorer</h3>
                    <div className="schema-sections">
                        <div className="schema-section">
                            <h4>📋 Node Types</h4>
                            <div className="schema-items">
                                <div className="schema-item">
                                    <h5>👤 Patient</h5>
                                    <p>Core patient records with demographics and medical information</p>
                                    <div className="schema-properties">
                                        <span>patientId</span>
                                        <span>name</span>
                                        <span>age</span>
                                        <span>gender</span>
                                        <span>condition</span>
                                        <span>email</span>
                                        <span>phoneNumber</span>
                                        <span>insurance</span>
                                    </div>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_NODE_QUERIES.patient);
                                            setActiveTab('custom');
                                        }}
                                        className="schema-query-btn"
                                    >
                                        📝 Sample Query
                                    </button>
                                </div>

                                <div className="schema-item">
                                    <h5>📊 GlucoseReading</h5>
                                    <p>Individual glucose measurements with timestamps</p>
                                    <div className="schema-properties">
                                        <span>glucose</span>
                                        <span>timestamp</span>
                                        <span>readingType</span>
                                        <span>deviceId</span>
                                    </div>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_NODE_QUERIES.glucoseReading);
                                            setActiveTab('custom');
                                        }}
                                        className="schema-query-btn"
                                    >
                                        📝 Sample Query
                                    </button>
                                </div>

                                <div className="schema-item">
                                    <h5>💊 Medication</h5>
                                    <p>Prescribed medications and dosing information</p>
                                    <div className="schema-properties">
                                        <span>name</span>
                                        <span>dosage</span>
                                        <span>frequency</span>
                                        <span>startDate</span>
                                        <span>endDate</span>
                                    </div>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_NODE_QUERIES.medication);
                                            setActiveTab('custom');
                                        }}
                                        className="schema-query-btn"
                                    >
                                        📝 Sample Query
                                    </button>
                                </div>

                                <div className="schema-item">
                                    <h5>🏥 Condition</h5>
                                    <p>Medical conditions and diagnoses</p>
                                    <div className="schema-properties">
                                        <span>name</span>
                                        <span>icd10Code</span>
                                        <span>severity</span>
                                        <span>diagnosisDate</span>
                                    </div>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_NODE_QUERIES.condition);
                                            setActiveTab('custom');
                                        }}
                                        className="schema-query-btn"
                                    >
                                        📝 Sample Query
                                    </button>
                                </div>

                                <div className="schema-item">
                                    <h5>📅 Appointment</h5>
                                    <p>Medical appointments and visit records</p>
                                    <div className="schema-properties">
                                        <span>date</span>
                                        <span>type</span>
                                        <span>outcome</span>
                                        <span>notes</span>
                                        <span>provider</span>
                                    </div>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_NODE_QUERIES.appointment);
                                            setActiveTab('custom');
                                        }}
                                        className="schema-query-btn"
                                    >
                                        📝 Sample Query
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="schema-section">
                            <h4>🔗 Relationships</h4>
                            <div className="schema-relationships">
                                <div className="relationship-item">
                                    <span className="rel-source">Patient</span>
                                    <span className="rel-type">HAD_READING</span>
                                    <span className="rel-target">GlucoseReading</span>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_RELATIONSHIP_QUERIES.patientGlucose);
                                            setActiveTab('custom');
                                        }}
                                        className="rel-query-btn"
                                    >
                                        🔍
                                    </button>
                                </div>

                                <div className="relationship-item">
                                    <span className="rel-source">Patient</span>
                                    <span className="rel-type">TAKES_MEDICATION</span>
                                    <span className="rel-target">Medication</span>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_RELATIONSHIP_QUERIES.patientMedication);
                                            setActiveTab('custom');
                                        }}
                                        className="rel-query-btn"
                                    >
                                        🔍
                                    </button>
                                </div>

                                <div className="relationship-item">
                                    <span className="rel-source">Patient</span>
                                    <span className="rel-type">HAS_CONDITION</span>
                                    <span className="rel-target">Condition</span>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_RELATIONSHIP_QUERIES.patientCondition);
                                            setActiveTab('custom');
                                        }}
                                        className="rel-query-btn"
                                    >
                                        🔍
                                    </button>
                                </div>

                                <div className="relationship-item">
                                    <span className="rel-source">Patient</span>
                                    <span className="rel-type">HAD_APPOINTMENT</span>
                                    <span className="rel-target">Appointment</span>
                                    <button
                                        onClick={() => {
                                            setCustomQuery(SCHEMA_RELATIONSHIP_QUERIES.patientAppointment);
                                            setActiveTab('custom');
                                        }}
                                        className="rel-query-btn"
                                    >
                                        🔍
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="schema-section">
                            <h4>💡 Query Templates</h4>
                            <div className="query-templates">
                                {Object.entries(SCHEMA_QUERY_TEMPLATES).map(([key, template]) => (
                                    <div key={key} className="template-item">
                                        <h5>{template.title}</h5>
                                        <p className="template-description">{template.description}</p>
                                        {template.parameters.length > 0 && (
                                            <div className="template-params">
                                                <small>Parameters: {template.parameters.join(', ')}</small>
                                            </div>
                                        )}
                                        <button
                                            onClick={() => {
                                                setCustomQuery(template.query);
                                                setActiveTab('custom');
                                            }}
                                            className="template-btn"
                                        >
                                            📝 Use Template
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="query-editor">
                <h3>Query Editor</h3>
                <div className="parameters-section">
                    <h4>Query Parameters</h4>
                    <div className="parameter-inputs">
                        <div className="parameter-input">
                            <label>Patient ID:</label>
                            <select
                                value={queryParameters.patientId}
                                onChange={(e) => handleParameterChange('patientId', e.target.value)}
                            >
                                <option value="Patient_1">Patient_1 - John Anderson (Type 2 Diabetes)</option>
                                <option value="Patient_2">Patient_2 - Maria Garcia (Type 1 Diabetes)</option>
                                <option value="Patient_3">Patient_3 - Robert Chen (Prediabetes)</option>
                                <option value="Patient_4">Patient_4 - Sarah Johnson (Gestational Diabetes)</option>
                                <option value="Patient_5">Patient_5 - David Williams (Type 2 Diabetes)</option>
                                <option value="Patient_6">Patient_6 - Emily Davis (Type 1 Diabetes)</option>
                                <option value="Patient_7">Patient_7 - Michael Brown (Type 2 Diabetes)</option>
                            </select>
                        </div>
                        <div className="parameter-input">
                            <label>Date Range (days):</label>
                            <input
                                type="number"
                                value={queryParameters.days || 30}
                                onChange={(e) => handleParameterChange('days', parseInt(e.target.value))}
                                placeholder="30"
                                min="1"
                                max="365"
                            />
                        </div>
                        <div className="parameter-input">
                            <label>Limit Results:</label>
                            <input
                                type="number"
                                value={queryParameters.limit || 100}
                                onChange={(e) => handleParameterChange('limit', parseInt(e.target.value))}
                                placeholder="100"
                                min="1"
                                max="1000"
                            />
                        </div>
                    </div>
                </div>

                <div className="query-input-section">
                    <label htmlFor="cypher-query">Cypher Query:</label>
                    <textarea
                        id="cypher-query"
                        value={customQuery}
                        onChange={(e) => setCustomQuery(e.target.value)}
                        placeholder="Enter your Cypher query here..."
                        rows={8}
                        className="query-textarea"
                    />
                </div>

                <div className="query-actions">
                    <button
                        onClick={handleExecuteQuery}
                        disabled={loading || connectionStatus !== 'connected' || !customQuery.trim()}
                        className="execute-button"
                    >
                        {loading ? 'Executing...' : 'Execute Query'}
                    </button>

                    {queryResults.length > 0 && (
                        <div className="export-buttons">
                            <button onClick={() => exportResults('json')} className="export-button">
                                Export JSON
                            </button>
                            <button onClick={() => exportResults('csv')} className="export-button">
                                Export CSV
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>

        {/* Results Section */}
        <div className="results-section">
            {queryStats && (
                <div className="query-stats">
                    <h3>Query Results</h3>
                    <div className="stats-grid">
                        <div className="stat-item">
                            <span className="stat-label">Records:</span>
                            <span className="stat-value">{queryStats.recordCount}</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Execution Time:</span>
                            <span className="stat-value">{queryStats.executionTime}ms</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-label">Executed:</span>
                            <span className="stat-value">{queryStats.timestamp}</span>
                        </div>
                    </div>
                </div>
            )}

            {/* AGP Chart Section */}
            {showAGPChart && agpData && (
                <div className="agp-section">
                    <div className="agp-section-header">
                        <h3>Ambulatory Glucose Profile (AGP)</h3>
                        <div className="agp-export-buttons">
                            <button
                                onClick={() => {
                                    const reportData = generateAGPReport(
                                        queryResults,
                                        { patientId: queryParameters.patientId },
                                        agpSettings
                                    );
                                    if (reportData) exportAGPReportJSON(reportData);
                                }}
                                className="agp-export-button"
                                title="Export comprehensive AGP report as JSON"
                            >
                                📊 Export AGP Report (JSON)
                            </button>
                            <button
                                onClick={() => {
                                    const reportData = generateAGPReport(
                                        queryResults,
                                        { patientId: queryParameters.patientId },
                                        agpSettings
                                    );
                                    if (reportData) exportAGPReportCSV(reportData);
                                }}
                                className="agp-export-button"
                                title="Export AGP summary as CSV"
                            >
                                📈 Export AGP Summary (CSV)
                            </button>
                        </div>
                    </div>

                    <AGPChart
                        agpData={agpData}
                        glucoseData={queryResults}
                        hypoThreshold={agpSettings.hypoThreshold}
                        hyperThreshold={agpSettings.hyperThreshold}
                        className="agp-chart"
                    />

                    {/* AGP Statistics - Complete Feature Set */}
                    <AGPStatistics
                        glucoseData={queryResults}
                        customRanges={{
                            veryLow: { min: 0, max: 54, label: 'Very Low (<54)', color: '#dc2626' },
                            low: { min: 54, max: agpSettings.hypoThreshold, label: `Low (54-${agpSettings.hypoThreshold})`, color: '#f59e0b' },
                            target: { min: agpSettings.targetMin, max: agpSettings.targetMax, label: `Target Range (${agpSettings.targetMin}-${agpSettings.targetMax})`, color: '#10b981' },
                            high: { min: agpSettings.hyperThreshold, max: 250, label: `High (${agpSettings.hyperThreshold}-250)`, color: '#f59e0b' },
                            veryHigh: { min: 250, max: 400, label: 'Very High (>250)', color: '#dc2626' }
                        }}
                        className="agp-statistics"
                    />

                    {/* AI Recommendations Section */}
                    {showAIRecommendations && (aiRecommendations || aiInsights) && (
                        <div className="ai-recommendations-section">
                            <div className="ai-section-header">
                                <h3>🤖 AI Clinical Recommendations</h3>
                                <div className="ai-controls">
                                    <button
                                        className="refresh-ai-btn"
                                        onClick={refreshAIRecommendations}
                                        disabled={aiLoading}
                                    >
                                        {aiLoading ? '🔄 Generating...' : '🔄 Refresh AI'}
                                    </button>
                                    <button
                                        className="toggle-ai-btn"
                                        onClick={toggleAIRecommendations}
                                    >
                                        {showAIRecommendations ? '👁️ Hide AI' : '👁️ Show AI'}
                                    </button>
                                </div>
                            </div>

                            {aiLoading && (
                                <div className="ai-loading">
                                    <div className="loading-spinner"></div>
                                    <p>Generating AI recommendations...</p>
                                </div>
                            )}

                            {aiRecommendations && !aiLoading && (
                                <div className="ai-agp-recommendations">
                                    <h4>📊 AGP Analysis Recommendations</h4>
                                    {aiRecommendations.error ? (
                                        <div className="ai-error">
                                            <p>❌ {aiRecommendations.message}</p>
                                            {aiRecommendations.fallback && (
                                                <p>✅ Fallback mode enabled - Enhanced rule-based recommendations available.</p>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="recommendations-grid">
                                            {aiRecommendations.recommendations?.map((rec, index) => (
                                                <div key={index} className={`recommendation-card ${rec.level} ${rec.priority}-priority`}>
                                                    <div className="recommendation-header">
                                                        <span className="recommendation-category">{rec.category}</span>
                                                        <span className={`recommendation-level ${rec.level}`}>
                                                            {rec.level === 'critical' && '🚨'}
                                                            {rec.level === 'high' && '⚠️'}
                                                            {rec.level === 'medium' && '📋'}
                                                            {rec.level === 'excellent' && '✅'}
                                                            {rec.level === 'good' && '👍'}
                                                            {rec.level}
                                                        </span>
                                                    </div>
                                                    <p className="recommendation-message">{rec.message}</p>
                                                    <p className="recommendation-action">{rec.recommendation}</p>
                                                    <div className="recommendation-meta">
                                                        <span className="evidence-level">Evidence: {rec.evidence_level}</span>
                                                        <span className="ai-source">Source: {rec.source}</span>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}

                                    {aiRecommendations.model_used && (
                                        <div className="ai-meta-info">
                                            <p><strong>AI Model:</strong> {aiRecommendations.model_used}</p>
                                            <p><strong>Analysis Type:</strong> {aiRecommendations.analysis_type}</p>
                                            <p><strong>Generated:</strong> {new Date(aiRecommendations.generated_at).toLocaleString()}</p>
                                            {aiRecommendations.ai_confidence && (
                                                <p><strong>Confidence:</strong> {aiRecommendations.ai_confidence}</p>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}

                            {aiInsights && !aiLoading && !aiRecommendations && (
                                <div className="ai-clinical-insights">
                                    <h4>🔍 Clinical Insights</h4>
                                    {aiInsights.error ? (
                                        <div className="ai-error">
                                            <p>❌ {aiInsights.message}</p>
                                        </div>
                                    ) : (
                                        <div className="insights-grid">
                                            {aiInsights.insights?.map((insight, index) => (
                                                <div key={index} className={`insight-card ${insight.level}`}>
                                                    <div className="insight-header">
                                                        <span className="insight-category">{insight.category}</span>
                                                        <span className={`insight-level ${insight.level}`}>{insight.level}</span>
                                                    </div>
                                                    <p className="insight-message">{insight.message}</p>
                                                    {insight.recommendation && (
                                                        <p className="insight-recommendation">{insight.recommendation}</p>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            )}

                            <div className="ai-disclaimer">
                                <p><strong>⚠️ Medical Disclaimer:</strong> AI recommendations are for informational purposes only and should not replace professional medical advice. Always consult with healthcare providers for treatment decisions.</p>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Enhanced Results */}
            {queryResults.length > 0 && (
                <div className="enhanced-results">
                    <div className="results-header">
                        <h3>Query Results ({filteredResults.length} records)</h3>
                        <div className="results-controls">
                            <div className="search-control">
                                <input
                                    type="text"
                                    placeholder="Search results..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearch(e.target.value)}
                                    className="search-input"
                                />
                            </div>
                            <div className="page-size-control">
                                <label>Per page:</label>
                                <select
                                    value={pageSize}
                                    onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                                    className="page-size-select"
                                >
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                </select>
                            </div>
                            <div className="export-controls">
                                <button onClick={() => exportResults('csv')} className="export-btn">
                                    📊 Export CSV
                                </button>
                                <button onClick={() => exportResults('json')} className="export-btn">
                                    📄 Export JSON
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="results-table-container">
                        <table className="enhanced-results-table">
                            <thead>
                                <tr>
                                    {Object.keys(queryResults[0] || {}).map(key => (
                                        <th key={key}
                                            className={`sortable ${sortColumn === key ? `sorted-${sortDirection}` : ''}`}
                                            onClick={() => handleSort(key)}
                                        >
                                            {key}
                                            <span className="sort-indicator">
                                                {sortColumn === key ?
                                                    (sortDirection === 'asc' ? ' ↑' : ' ↓') :
                                                    ' ↕'
                                                }
                                            </span>
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {(() => {
                                    const startIndex = (currentPage - 1) * pageSize;
                                    const endIndex = startIndex + pageSize;
                                    return filteredResults.slice(startIndex, endIndex).map((row, index) => (
                                        <tr key={startIndex + index}>
                                            {Object.values(row).map((value, cellIndex) => {
                                                const key = Object.keys(row)[cellIndex];
                                                return (
                                                    <td key={cellIndex} className={`cell-${typeof value}`}>
                                                        {(() => {
                                                            if (value === null || value === undefined) {
                                                                return <span className="null-value">NULL</span>;
                                                            }

                                                            if (typeof value === 'object') {
                                                                return (
                                                                    <details className="json-object">
                                                                        <summary>Object</summary>
                                                                        <pre>{JSON.stringify(value, null, 2)}</pre>
                                                                    </details>
                                                                );
                                                            }

                                                            if (typeof value === 'boolean') {
                                                                return (
                                                                    <span className={`boolean-value ${value}`}>
                                                                        {value ? '✓' : '✗'} {String(value)}
                                                                    </span>
                                                                );
                                                            }

                                                            if (typeof value === 'number') {
                                                                return (
                                                                    <span className="number-value">
                                                                        {Number(value).toLocaleString()}
                                                                    </span>
                                                                );
                                                            }

                                                            // Check if it's a datetime string
                                                            if (key.toLowerCase().includes('timestamp') ||
                                                                key.toLowerCase().includes('date') ||
                                                                key.toLowerCase().includes('time')) {
                                                                try {
                                                                    const date = new Date(value);
                                                                    if (!isNaN(date.getTime())) {
                                                                        return (
                                                                            <span className="datetime-value" title={value}>
                                                                                {date.toLocaleString()}
                                                                            </span>
                                                                        );
                                                                    }
                                                                } catch (e) {
                                                                    // Fall through to string handling
                                                                }
                                                            }

                                                            const strValue = String(value);
                                                            if (strValue.length > 100) {
                                                                return (
                                                                    <details className="long-text">
                                                                        <summary>{strValue.substring(0, 50)}...</summary>
                                                                        <div className="full-text">{strValue}</div>
                                                                    </details>
                                                                );
                                                            }

                                                            return strValue;
                                                        })()}
                                                    </td>
                                                );
                                            })}
                                        </tr>
                                    ));
                                })()}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    {filteredResults.length > pageSize && (
                        <div className="pagination">
                            <div className="pagination-info">
                                Showing {Math.min(((currentPage - 1) * pageSize) + 1, filteredResults.length)} to {Math.min(currentPage * pageSize, filteredResults.length)} of {filteredResults.length} results
                            </div>
                            <div className="pagination-controls">
                                <button
                                    onClick={() => setCurrentPage(1)}
                                    disabled={currentPage === 1}
                                    className="pagination-btn"
                                >
                                    ««
                                </button>
                                <button
                                    onClick={() => setCurrentPage(currentPage - 1)}
                                    disabled={currentPage === 1}
                                    className="pagination-btn"
                                >
                                    ‹
                                </button>
                                <span className="page-info">
                                    Page {currentPage} of {Math.ceil(filteredResults.length / pageSize)}
                                </span>
                                <button
                                    onClick={() => setCurrentPage(currentPage + 1)}
                                    disabled={currentPage >= Math.ceil(filteredResults.length / pageSize)}
                                    className="pagination-btn"
                                >
                                    ›
                                </button>
                                <button
                                    onClick={() => setCurrentPage(Math.ceil(filteredResults.length / pageSize))}
                                    disabled={currentPage >= Math.ceil(filteredResults.length / pageSize)}
                                    className="pagination-btn"
                                >
                                    »»
                                </button>
                            </div>
                        </div>
                    )}

                    {/* Results Summary */}
                    <div className="results-summary">
                        <div className="summary-item">
                            <strong>Total Records:</strong> {queryResults.length}
                        </div>
                        <div className="summary-item">
                            <strong>Filtered Records:</strong> {filteredResults.length}
                        </div>
                        <div className="summary-item">
                            <strong>Columns:</strong> {Object.keys(queryResults[0] || {}).length}
                        </div>
                    </div>
                </div>
            )}

            {queryResults.length === 0 && queryStats && (
                <div className="no-results">
                    No results returned from the query.
                </div>
            )}
        </div>
        
        {/* Modal Components */}
        <ConfirmationModal
            isOpen={confirmationState.isOpen}
            title={confirmationState.title}
            message={confirmationState.message}
            confirmText={confirmationState.confirmText}
            cancelText={confirmationState.cancelText}
            confirmButtonStyle={confirmationState.confirmButtonStyle}
            onConfirm={confirmationState.onConfirm}
            onCancel={confirmationState.onCancel}
        />
        
        <InputModal
            isOpen={inputModalState.isOpen}
            title={inputModalState.title}
            message={inputModalState.message}
            placeholder={inputModalState.placeholder}
            defaultValue={inputModalState.defaultValue}
            confirmText={inputModalState.confirmText}
            cancelText={inputModalState.cancelText}
            required={inputModalState.required}
            onConfirm={inputModalState.onConfirm}
            onCancel={inputModalState.onCancel}
        />
    </div>
);
}

export default QueryRunner;
