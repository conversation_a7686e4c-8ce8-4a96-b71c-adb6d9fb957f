import dotenv from 'dotenv';
import neo4j from 'neo4j-driver';

dotenv.config();

console.log('🔍 Testing Neo4j connection for D1NAMO import...');

const { VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD } = process.env;
if (!VITE_NEO4J_URI || !VITE_NEO4J_USERNAME || !VITE_NEO4J_PASSWORD) {
  console.error('Missing Neo4j env vars. Set VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD.');
  process.exit(1);
}

const driver = neo4j.driver(
  VITE_NEO4J_URI,
  neo4j.auth.basic(
    VITE_NEO4J_USERNAME,
    VITE_NEO4J_PASSWORD
  )
);

async function testConnection() {
  const session = driver.session();
  try {
    console.log('🔗 Attempting connection to remote AuraDB...');
    const result = await session.run('RETURN "Hello from AuraDB!" as message');
    const message = result.records[0].get('message');
    console.log('✅ Connection successful:', message);

    // Test a simple query
    const countResult = await session.run('MATCH (n) RETURN count(n) as nodeCount');
    const nodeCount = countResult.records[0].get('nodeCount');
    console.log(`📊 Current nodes in database: ${nodeCount}`);

    return true;
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    return false;
  } finally {
    await session.close();
  }
}

// Test and exit
testConnection()
  .then((success) => {
    if (success) {
      console.log('✅ Remote connection test passed - ready for import');
    } else {
      console.log('❌ Remote connection test failed');
    }
    process.exit(success ? 0 : 1);
  })
  .finally(() => {
    driver.close();
  });
