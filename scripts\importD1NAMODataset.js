// D1NAMO Dataset Import Script
// Processes real D1NAMO CSV files and imports into Neo4j
// Handles ECG signals, glucose measurements, and patient metadata

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const neo4j = require('neo4j-driver');
require('dotenv').config();

// Database configuration
const uri = process.env.VITE_NEO4J_URI;
const username = process.env.VITE_NEO4J_USERNAME;
const password = process.env.VITE_NEO4J_PASSWORD;

if (!uri || !username || !password) {
  console.error('Missing Neo4j env vars. Please set VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD.');
  process.exit(1);
}

const driver = neo4j.driver(uri, neo4j.auth.basic(username, password));

// Configuration
const CONFIG = {
  dataDirectory: './data/d1namo', // Directory containing D1NAMO CSV files
  batchSize: 1000,
  maxRecordsPerFile: 10000, // Limit for testing
  samplingRateReduction: 10, // Reduce ECG sampling to 100Hz for storage efficiency
  logLevel: 'info'
};

// Expected D1NAMO file structure (adjust based on actual dataset)
const FILE_PATTERNS = {
  patients: /patients?\.csv$/i,
  glucose: /glucose.*\.csv$/i,
  ecg: /ecg.*\.csv$/i,
  metadata: /metadata.*\.csv$/i,
  events: /events?.*\.csv$/i
};

// Logging utility
function log(level, message, data = null) {
  if (CONFIG.logLevel === 'debug' || (CONFIG.logLevel === 'info' && level !== 'debug')) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    if (data && CONFIG.logLevel === 'debug') {
      console.log(JSON.stringify(data, null, 2));
    }
  }
}

// File discovery and validation
async function discoverDataFiles() {
  log('info', `Scanning directory: ${CONFIG.dataDirectory}`);

  if (!fs.existsSync(CONFIG.dataDirectory)) {
    throw new Error(`Data directory not found: ${CONFIG.dataDirectory}`);
  }

  const files = fs.readdirSync(CONFIG.dataDirectory);
  const dataFiles = {
    patients: [],
    glucose: [],
    ecg: [],
    metadata: [],
    events: [],
    unknown: []
  };

  files.forEach(file => {
    const filePath = path.join(CONFIG.dataDirectory, file);
    const stats = fs.statSync(filePath);

    if (stats.isFile() && file.endsWith('.csv')) {
      let categorized = false;

      for (const [category, pattern] of Object.entries(FILE_PATTERNS)) {
        if (pattern.test(file)) {
          dataFiles[category].push({
            name: file,
            path: filePath,
            size: stats.size,
            modified: stats.mtime
          });
          categorized = true;
          break;
        }
      }

      if (!categorized) {
        dataFiles.unknown.push({
          name: file,
          path: filePath,
          size: stats.size
        });
      }
    }
  });

  log('info', 'Discovered files:', dataFiles);
  return dataFiles;
}

// CSV parsing utilities
function parseCSVFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    let rowCount = 0;

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => {
        if (rowCount < CONFIG.maxRecordsPerFile) {
          results.push(data);
          rowCount++;
        }
      })
      .on('end', () => {
        log('info', `Parsed ${results.length} rows from ${path.basename(filePath)}`);
        resolve(results);
      })
      .on('error', reject);
  });
}

// Data validation and cleaning
function validatePatientRecord(record) {
  const required = ['patient_id', 'age', 'gender'];
  const missing = required.filter(field => !record[field] || record[field].trim() === '');

  if (missing.length > 0) {
    log('debug', `Invalid patient record - missing fields: ${missing.join(', ')}`, record);
    return false;
  }

  // Basic validation
  const age = parseInt(record.age);
  if (isNaN(age) || age < 18 || age > 100) {
    log('debug', `Invalid age: ${record.age}`, record);
    return false;
  }

  return true;
}

function validateGlucoseRecord(record) {
  const required = ['patient_id', 'timestamp', 'glucose'];
  const missing = required.filter(field => !record[field]);

  if (missing.length > 0) {
    return false;
  }

  const glucose = parseFloat(record.glucose);
  if (isNaN(glucose) || glucose < 20 || glucose > 600) {
    return false;
  }

  return true;
}

function validateECGRecord(record) {
  const required = ['patient_id', 'timestamp'];
  const missing = required.filter(field => !record[field]);

  return missing.length === 0;
}

// Neo4j import functions
async function importPatients(session, patientData) {
  log('info', `Importing ${patientData.length} patient records`);

  let imported = 0;
  let skipped = 0;

  for (const record of patientData) {
    if (!validatePatientRecord(record)) {
      skipped++;
      continue;
    }

    try {
      await session.run(`
        MERGE (p:Patient:D1NAMOSubject {patientId: $patientId})
        SET p.name = COALESCE($name, 'Patient ' + $patientId),
            p.age = toInteger($age),
            p.gender = $gender,
            p.condition = COALESCE($condition, 'Type 1 Diabetes'),
            p.diagnosis_date = COALESCE($diagnosis_date, '2020-01-01'),
            p.weight = toFloat($weight),
            p.height = toFloat($height),
            p.bmi = CASE WHEN $weight IS NOT NULL AND $height IS NOT NULL
                      THEN toFloat($weight) / (toFloat($height) * toFloat($height))
                      ELSE NULL END,
            p.diabetes_duration = toInteger($diabetes_duration),
            p.baseline_hba1c = toFloat($hba1c),
            p.cgm_model = COALESCE($cgm_model, 'Unknown'),
            p.ecg_device = COALESCE($ecg_device, 'Holter Monitor'),
            p.study_phase = COALESCE($study_phase, 'Phase 1'),
            p.enrollment_date = COALESCE($enrollment_date, date()),
            p.created_at = datetime(),
            p.data_source = 'D1NAMO Dataset'
      `, {
        patientId: record.patient_id,
        name: record.name || record.patient_name,
        age: record.age,
        gender: record.gender || record.sex,
        condition: record.condition || record.diabetes_type,
        diagnosis_date: record.diagnosis_date,
        weight: record.weight,
        height: record.height,
        diabetes_duration: record.diabetes_duration || record.duration,
        hba1c: record.hba1c || record.baseline_hba1c,
        cgm_model: record.cgm_model || record.cgm_device,
        ecg_device: record.ecg_device,
        study_phase: record.study_phase || record.phase,
        enrollment_date: record.enrollment_date
      });

      imported++;
    } catch (error) {
      log('debug', `Error importing patient ${record.patient_id}: ${error.message}`);
      skipped++;
    }
  }

  log('info', `Patient import complete: ${imported} imported, ${skipped} skipped`);
  return { imported, skipped };
}

async function importGlucoseReadings(session, glucoseData) {
  log('info', `Importing ${glucoseData.length} glucose readings`);

  let imported = 0;
  let skipped = 0;
  const batchSize = CONFIG.batchSize;

  for (let i = 0; i < glucoseData.length; i += batchSize) {
    const batch = glucoseData.slice(i, i + batchSize);
    const validRecords = batch.filter(validateGlucoseRecord);

    if (validRecords.length === 0) {
      skipped += batch.length;
      continue;
    }

    try {
      await session.run(`
        UNWIND $records AS record
        MATCH (p:Patient {patientId: record.patient_id})
        CREATE (g:GlucoseReading:D1NAMOReading {
          glucose: toFloat(record.glucose),
          timestamp: datetime(record.timestamp),
          readingType: COALESCE(record.reading_type, 'Continuous'),
          device: COALESCE(record.device, 'CGM'),
          quality: COALESCE(record.quality, 'Good'),
          trend_arrow: record.trend_arrow,
          rate_of_change: toFloat(record.rate_of_change),
          created_at: datetime(),
          data_source: 'D1NAMO Dataset'
        })
        CREATE (p)-[:HAD_READING]->(g)
      `, {
        records: validRecords.map(record => ({
          patient_id: record.patient_id,
          glucose: record.glucose,
          timestamp: record.timestamp || record.datetime,
          reading_type: record.reading_type || record.type,
          device: record.device || record.sensor,
          quality: record.quality || record.signal_quality,
          trend_arrow: record.trend_arrow,
          rate_of_change: record.rate_of_change || record.roc
        }))
      });

      imported += validRecords.length;
      skipped += (batch.length - validRecords.length);

      if (i % (batchSize * 10) === 0) {
        log('info', `Glucose import progress: ${imported} imported, ${skipped} skipped`);
      }
    } catch (error) {
      log('debug', `Error importing glucose batch: ${error.message}`);
      skipped += batch.length;
    }
  }

  log('info', `Glucose import complete: ${imported} imported, ${skipped} skipped`);
  return { imported, skipped };
}

async function importECGReadings(session, ecgData) {
  log('info', `Importing ${ecgData.length} ECG readings`);

  let imported = 0;
  let skipped = 0;
  const batchSize = Math.floor(CONFIG.batchSize / 10); // Smaller batches for ECG data

  for (let i = 0; i < ecgData.length; i += batchSize) {
    const batch = ecgData.slice(i, i + batchSize);
    const validRecords = batch.filter(validateECGRecord);

    if (validRecords.length === 0) {
      skipped += batch.length;
      continue;
    }

    try {
      for (const record of validRecords) {
        // Import ECG reading
        await session.run(`
          MATCH (p:Patient {patientId: $patientId})
          CREATE (e:ECGReading:D1NAMOReading {
            timestamp: datetime($timestamp),
            duration: toFloat($duration),
            sampling_rate: toInteger($sampling_rate),
            device_id: COALESCE($device_id, 'ECG_DEVICE'),
            signal_quality: COALESCE($signal_quality, 'Good'),
            heart_rate: toInteger($heart_rate),
            created_at: datetime(),
            data_source: 'D1NAMO Dataset'
          })
          CREATE (p)-[:HAD_ECG]->(e)

          // Try to link with glucose reading at same timestamp
          OPTIONAL MATCH (g:GlucoseReading)
          WHERE g.timestamp = datetime($timestamp) AND (g)<-[:HAD_READING]-(p)
          FOREACH(glucose IN CASE WHEN g IS NOT NULL THEN [g] ELSE [] END |
            CREATE (e)-[:SYNCHRONIZED_WITH]->(glucose)
          )
        `, {
          patientId: record.patient_id,
          timestamp: record.timestamp || record.datetime,
          duration: record.duration || 10,
          sampling_rate: record.sampling_rate || 1000,
          device_id: record.device_id || record.device,
          signal_quality: record.signal_quality || record.quality,
          heart_rate: record.heart_rate || record.hr
        });

        // Import ECG features if available
        const features = extractECGFeatures(record);
        if (Object.keys(features).length > 0) {
          await session.run(`
            MATCH (e:ECGReading {timestamp: datetime($timestamp)})
            WHERE (e)<-[:HAD_ECG]-(:Patient {patientId: $patientId})
            CREATE (f:ECGFeatures {
              heart_rate: toInteger($heart_rate),
              rr_interval: toFloat($rr_interval),
              pr_interval: toFloat($pr_interval),
              qrs_duration: toFloat($qrs_duration),
              qt_interval: toFloat($qt_interval),
              qtc_interval: toFloat($qtc_interval),
              hrv_rmssd: toFloat($hrv_rmssd),
              created_at: datetime()
            })
            CREATE (e)-[:HAS_FEATURES]->(f)
          `, {
            patientId: record.patient_id,
            timestamp: record.timestamp || record.datetime,
            ...features
          });
        }

        // Import lead data if available (store sample of waveform)
        const leadData = extractLeadData(record);
        for (const [leadName, samples] of Object.entries(leadData)) {
          if (samples && samples.length > 0) {
            await session.run(`
              MATCH (e:ECGReading {timestamp: datetime($timestamp)})
              WHERE (e)<-[:HAD_ECG]-(:Patient {patientId: $patientId})
              CREATE (l:ECGLead {
                lead_name: $lead_name,
                samples: $samples,
                sample_count: $sample_count,
                sampling_rate: toInteger($sampling_rate),
                created_at: datetime()
              })
              CREATE (e)-[:HAS_LEAD]->(l)
            `, {
              patientId: record.patient_id,
              timestamp: record.timestamp || record.datetime,
              lead_name: leadName,
              samples: samples.slice(0, 500), // Store first 500 samples
              sample_count: samples.length,
              sampling_rate: record.sampling_rate || 1000
            });
          }
        }

        imported++;
      }

      skipped += (batch.length - validRecords.length);

      if (i % (batchSize * 5) === 0) {
        log('info', `ECG import progress: ${imported} imported, ${skipped} skipped`);
      }
    } catch (error) {
      log('debug', `Error importing ECG batch: ${error.message}`);
      skipped += batch.length;
    }
  }

  log('info', `ECG import complete: ${imported} imported, ${skipped} skipped`);
  return { imported, skipped };
}

// Feature extraction utilities
function extractECGFeatures(record) {
  const features = {};

  // Map common ECG feature field names
  const fieldMapping = {
    heart_rate: ['heart_rate', 'hr', 'bpm'],
    rr_interval: ['rr_interval', 'rr', 'rr_ms'],
    pr_interval: ['pr_interval', 'pr', 'pr_ms'],
    qrs_duration: ['qrs_duration', 'qrs', 'qrs_ms'],
    qt_interval: ['qt_interval', 'qt', 'qt_ms'],
    qtc_interval: ['qtc_interval', 'qtc', 'qtc_ms'],
    hrv_rmssd: ['hrv_rmssd', 'rmssd', 'hrv']
  };

  for (const [feature, possibleFields] of Object.entries(fieldMapping)) {
    for (const field of possibleFields) {
      if (record[field] !== undefined && record[field] !== null && record[field] !== '') {
        const value = parseFloat(record[field]);
        if (!isNaN(value)) {
          features[feature] = value;
          break;
        }
      }
    }
  }

  return features;
}

function extractLeadData(record) {
  const leadData = {};
  const commonLeads = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6'];

  for (const lead of commonLeads) {
    // Look for lead data in various formats
    const possibleFields = [
      lead,
      `lead_${lead}`,
      `${lead}_data`,
      `ecg_${lead}`,
      lead.toLowerCase(),
      `lead${lead}`
    ];

    for (const field of possibleFields) {
      if (record[field]) {
        try {
          let samples;

          // Handle different data formats
          if (typeof record[field] === 'string') {
            // Parse comma-separated values
            samples = record[field].split(',').map(v => parseFloat(v.trim())).filter(v => !isNaN(v));
          } else if (Array.isArray(record[field])) {
            samples = record[field].map(v => parseFloat(v)).filter(v => !isNaN(v));
          } else {
            continue;
          }

          // Apply sampling rate reduction if needed
          if (samples.length > CONFIG.samplingRateReduction) {
            const step = Math.floor(samples.length / (samples.length / CONFIG.samplingRateReduction));
            samples = samples.filter((_, index) => index % step === 0);
          }

          if (samples.length > 0) {
            leadData[lead] = samples;
            break;
          }
        } catch (error) {
          log('debug', `Error parsing lead data for ${lead}: ${error.message}`);
        }
      }
    }
  }

  return leadData;
}

// Study metadata import
async function importStudyMetadata(session) {
  log('info', 'Creating D1NAMO study metadata');

  await session.run(`
    MERGE (s:Study:D1NAMOStudy {studyId: 'D1NAMO-IMPORT-2024'})
    SET s.title = 'D1NAMO Dataset Import',
        s.description = 'Imported D1NAMO ECG and glucose monitoring dataset',
        s.import_date = datetime(),
        s.data_source = 'Kaggle D1NAMO Dataset',
        s.created_at = datetime()
  `);

  // Link all D1NAMO patients to the study
  await session.run(`
    MATCH (p:D1NAMOSubject)
    MATCH (s:Study {studyId: 'D1NAMO-IMPORT-2024'})
    MERGE (p)-[:ENROLLED_IN {
      enrollment_date: COALESCE(p.enrollment_date, date()),
      data_imported: true
    }]->(s)
  `);

  log('info', 'Study metadata created');
}

// Main import function
async function importD1NAMODataset() {
  const session = driver.session();
  const startTime = Date.now();

  try {
    log('info', 'Starting D1NAMO dataset import...');

    // Discover data files
    const dataFiles = await discoverDataFiles();

    // Import in order: patients -> glucose -> ecg -> metadata
    const results = {
      patients: { imported: 0, skipped: 0 },
      glucose: { imported: 0, skipped: 0 },
      ecg: { imported: 0, skipped: 0 }
    };

    // Import patients
    if (dataFiles.patients.length > 0) {
      for (const file of dataFiles.patients) {
        log('info', `Processing patient file: ${file.name}`);
        const patientData = await parseCSVFile(file.path);
        const result = await importPatients(session, patientData);
        results.patients.imported += result.imported;
        results.patients.skipped += result.skipped;
      }
    }

    // Import glucose readings
    if (dataFiles.glucose.length > 0) {
      for (const file of dataFiles.glucose) {
        log('info', `Processing glucose file: ${file.name}`);
        const glucoseData = await parseCSVFile(file.path);
        const result = await importGlucoseReadings(session, glucoseData);
        results.glucose.imported += result.imported;
        results.glucose.skipped += result.skipped;
      }
    }

    // Import ECG readings
    if (dataFiles.ecg.length > 0) {
      for (const file of dataFiles.ecg) {
        log('info', `Processing ECG file: ${file.name}`);
        const ecgData = await parseCSVFile(file.path);
        const result = await importECGReadings(session, ecgData);
        results.ecg.imported += result.imported;
        results.ecg.skipped += result.skipped;
      }
    }

    // Create study metadata
    await importStudyMetadata(session);

    // Create indexes for performance
    log('info', 'Creating database indexes...');
    const indexes = [
      'CREATE INDEX d1namo_patient_idx IF NOT EXISTS FOR (p:D1NAMOSubject) ON (p.patientId)',
      'CREATE INDEX d1namo_glucose_timestamp IF NOT EXISTS FOR (g:D1NAMOReading) ON (g.timestamp)',
      'CREATE INDEX d1namo_ecg_timestamp IF NOT EXISTS FOR (e:ECGReading) ON (e.timestamp)',
    ];

    for (const index of indexes) {
      try {
        await session.run(index);
      } catch (error) {
        log('debug', `Index creation: ${error.message}`);
      }
    }

    const duration = (Date.now() - startTime) / 1000;

    log('info', '✅ D1NAMO dataset import completed successfully!');
    log('info', `Total import time: ${duration.toFixed(2)} seconds`);
    log('info', 'Import Summary:');
    log('info', `- Patients: ${results.patients.imported} imported, ${results.patients.skipped} skipped`);
    log('info', `- Glucose readings: ${results.glucose.imported} imported, ${results.glucose.skipped} skipped`);
    log('info', `- ECG readings: ${results.ecg.imported} imported, ${results.ecg.skipped} skipped`);

    return results;

  } catch (error) {
    log('error', 'Import failed:', error);
    throw error;
  } finally {
    await session.close();
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);

  // Parse CLI arguments
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
D1NAMO Dataset Import Tool

Usage: node importD1NAMODataset.js [options]

Options:
  --data-dir <path>     Directory containing D1NAMO CSV files (default: ./data/d1namo)
  --batch-size <size>   Batch size for imports (default: 1000)
  --max-records <num>   Maximum records per file for testing (default: 10000)
  --log-level <level>   Logging level: info, debug (default: info)
  --help, -h           Show this help message

Example:
  node importD1NAMODataset.js --data-dir ./kaggle-data --batch-size 500 --log-level debug
    `);
    process.exit(0);
  }

  // Update configuration from CLI args
  const dataDir = args[args.indexOf('--data-dir') + 1];
  if (dataDir) CONFIG.dataDirectory = dataDir;

  const batchSize = parseInt(args[args.indexOf('--batch-size') + 1]);
  if (batchSize) CONFIG.batchSize = batchSize;

  const maxRecords = parseInt(args[args.indexOf('--max-records') + 1]);
  if (maxRecords) CONFIG.maxRecordsPerFile = maxRecords;

  const logLevel = args[args.indexOf('--log-level') + 1];
  if (logLevel) CONFIG.logLevel = logLevel;

  log('info', 'Starting D1NAMO import with configuration:', CONFIG);

  importD1NAMODataset()
    .then((results) => {
      log('info', 'Import completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      log('error', 'Import failed:', error);
      process.exit(1);
    })
    .finally(() => {
      driver.close();
    });
}

module.exports = {
  importD1NAMODataset,
  discoverDataFiles,
  CONFIG
};
