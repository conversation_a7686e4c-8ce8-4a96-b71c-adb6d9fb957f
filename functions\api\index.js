/**
 * Main API Router
 * Available at: /api/index
 *
 * Central API endpoint that routes requests to appropriate services
 * Provides API documentation and service discovery
 */

// CORS headers for browser requests
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Content-Type': 'application/json'
};

export async function onRequest(context) {
  const { request } = context;

  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: CORS_HEADERS });
  }

  if (request.method === 'GET') {
    return handleApiDocumentation(context);
  }

  return new Response(JSON.stringify({
    error: 'Method not allowed',
    message: 'Only GET requests are supported on this endpoint',
    availableEndpoints: {
      '/api/health': 'System health check',
      '/api/neo4j': 'Neo4j database operations',
      '/api/ai': 'AI services and recommendations',
      '/api': 'This API documentation'
    }
  }), {
    status: 405,
    headers: CORS_HEADERS
  });
}

function handleApiDocumentation(context) {
  const { env } = context;

  const apiDoc = {
    name: "HealthHub Research Platform API",
    version: "1.0.0",
    description: "Secure backend API for healthcare analytics and AI-powered insights",
    timestamp: new Date().toISOString(),

    endpoints: {
      "/api/health": {
        methods: ["GET"],
        description: "System health check and service status",
        example: "GET /api/health"
      },

      "/api/neo4j": {
        methods: ["GET", "POST"],
        description: "Neo4j database operations",
        authentication: "Optional (uses mock data if not configured)",
        rateLimit: "30 requests per minute",
        examples: {
          healthCheck: "GET /api/neo4j",
          connect: "POST /api/neo4j { \"action\": \"connect\" }",
          query: "POST /api/neo4j { \"action\": \"query\", \"query\": \"MATCH (p:Patient) RETURN p LIMIT 5\", \"parameters\": {} }"
        },
        security: {
          readOnly: "Only read operations (MATCH, RETURN) are allowed",
          forbidden: "CREATE, DELETE, SET, DROP operations are blocked",
          sanitization: "All queries are validated against security patterns"
        }
      },

      "/api/ai": {
        methods: ["GET", "POST"],
        description: "AI services for clinical insights and recommendations",
        authentication: "Optional (uses mock data if OpenAI not configured)",
        rateLimit: "10 requests per 5 minutes",
        examples: {
          healthCheck: "GET /api/ai",
          agpRecommendations: "POST /api/ai { \"action\": \"agp-recommendations\", \"data\": {...}, \"context\": {} }",
          clinicalInsights: "POST /api/ai { \"action\": \"clinical-insights\", \"data\": [...], \"context\": {} }"
        }
      }
    },

    configuration: {
      neo4j: {
        enabled: !!env.NEO4J_PASSWORD,
        mockMode: !env.NEO4J_PASSWORD,
        variables: ["NEO4J_URI", "NEO4J_USERNAME", "NEO4J_PASSWORD", "NEO4J_DATABASE"]
      },

      ai: {
        enabled: !!env.OPENAI_API_KEY,
        mockMode: !env.OPENAI_API_KEY,
        variables: ["OPENAI_API_KEY"]
      }
    },

    features: {
      rateLimiting: "IP-based rate limiting on all endpoints",
      cors: "CORS enabled for all origins",
      security: "Query validation and sanitization",
      fallback: "Graceful fallback to mock data on service failures",
      monitoring: "Request logging and error tracking"
    },

    responses: {
      success: {
        status: 200,
        structure: {
          data: "Response data",
          mockMode: "boolean - true if using mock data",
          timestamp: "ISO timestamp"
        }
      },

      error: {
        status: "4xx or 5xx",
        structure: {
          error: true,
          message: "Error description",
          timestamp: "ISO timestamp"
        }
      },

      rateLimit: {
        status: 429,
        headers: {
          "Retry-After": "Seconds to wait before retry"
        }
      }
    },

    examples: {
      patientQuery: {
        endpoint: "/api/neo4j",
        method: "POST",
        body: {
          action: "query",
          query: "MATCH (p:Patient) RETURN p.name, p.condition, p.age ORDER BY p.name LIMIT 10",
          parameters: {}
        }
      },

      glucoseAnalysis: {
        endpoint: "/api/neo4j",
        method: "POST",
        body: {
          action: "query",
          query: "MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading) WHERE g.timestamp >= datetime() - duration({days: $days}) RETURN g.timestamp, g.glucose ORDER BY g.timestamp LIMIT $limit",
          parameters: {
            patientId: "Patient_1",
            days: 30,
            limit: 1000
          }
        }
      },

      agpRecommendations: {
        endpoint: "/api/ai",
        method: "POST",
        body: {
          action: "agp-recommendations",
          data: {
            totalReadings: 168,
            percentiles: "/* AGP percentile data */",
            dateRange: {
              start: "2025-08-01T00:00:00Z",
              end: "2025-09-01T00:00:00Z"
            }
          },
          context: {
            patientId: "Patient_1"
          }
        }
      }
    },

    deployment: {
      platform: "Cloudflare Pages Functions",
      region: "Global Edge Network",
      runtime: "V8 JavaScript Engine",
      coldStart: "< 100ms typical"
    }
  };

  return new Response(JSON.stringify(apiDoc, null, 2), {
    status: 200,
    headers: CORS_HEADERS
  });
}
