import { useEffect, useState } from 'react'
import Neo4jService from '../services/neo4jService'
import AGPChart from './AGPChart'
import './AGPChart.css'
import Graph3DViewer from './Graph3DViewer'
import './Graph3DViewer.css'
import './Neo4jPathViewer.css'

function Neo4jPathViewer() {
    const [connectionStatus, setConnectionStatus] = useState('disconnected')
    const [query, setQuery] = useState('MATCH p = (n)-[r*1..2]-(m) RETURN p LIMIT 10')
    const [paths, setPaths] = useState([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)
    const [neo4jService] = useState(() => new Neo4jService())
    const [selectedQueryKey, setSelectedQueryKey] = useState('')
    const [viewMode, setViewMode] = useState('2d') // '2d', '3d', 'agp'
    const [queryParameters, setQueryParameters] = useState({ patientId: 'Patient_1' })
    const [glucoseData, setGlucoseData] = useState([])
    const [patientInfo, setPatientInfo] = useState(null)
    const [connectionConfig, setConnectionConfig] = useState({
        uri: import.meta.env.VITE_NEO4J_URI || 'bolt://localhost:7687',
        username: import.meta.env.VITE_NEO4J_USERNAME || 'neo4j',
        password: import.meta.env.VITE_NEO4J_PASSWORD || '',
        database: import.meta.env.VITE_NEO4J_DATABASE || 'neo4j'
    })

    const commonQueries = Neo4jService.getCommonQueries()

    useEffect(() => {
        // Auto-load environment variables on component mount
        if (connectionConfig.password) {
            handleConnect()
        }
    }, [])

    const handleConnect = async () => {
        setLoading(true)
        setError(null)

        try {
            await neo4jService.connect(connectionConfig)
            setConnectionStatus('connected')

            // Auto-execute default query on successful connection
            if (paths.length === 0) {
                await handleExecuteQuery()
            }

        } catch (err) {
            setError(err.message)
            setConnectionStatus('error')
        } finally {
            setLoading(false)
        }
    }

    const handleExecuteQuery = async () => {
        if (connectionStatus !== 'connected') {
            setError('Please connect to Neo4j first')
            return
        }

        setLoading(true)
        setError(null)

        try {
            // Enforce visual-only safety: disallow any write/mutation operations
            const forbidden = /\b(CREATE|MERGE|DELETE|DETACH\s+DELETE|SET\s+|REMOVE\s+|CALL\s+db\.|CALL\s+\w+\([^)]*\)\s+YIELD\s+\w+\s+AS\s+\w+\s+CALL|USE\s+|LOAD\s+CSV|APOC\.\w+\.(?:(?!(toJSON|convert|map|coll|text|math|date|temporal)).)*)\b/i
            if (forbidden.test(query)) {
                throw new Error('Write or administrative operations are not allowed. This view is strictly read-only for visualization of existing nodes and relationships.')
            }

            // Use parameters for parameterized queries
            const results = await neo4jService.executeQuery(query, queryParameters, { disallowMock: true })
            const formattedPaths = formatResultsToPaths(results)
            setPaths(formattedPaths)

            // Check if this is glucose data and process it for AGP
            if (query.toLowerCase().includes('glucose') || selectedQueryKey.includes('glucose')) {
                const glucoseReadings = extractGlucoseData(results)
                setGlucoseData(glucoseReadings)

                // Extract patient info if available
                const patientData = extractPatientInfo(results, queryParameters.patientId)
                setPatientInfo(patientData)
            } else {
                setGlucoseData([])
                setPatientInfo(null)
            }

        } catch (err) {
            // Provide clearer messaging when mock mode is disallowed
            if (err.code === 'MOCK_DISALLOWED') {
                setError('A live Neo4j connection is required to display paths. Please check your connection settings and try again.')
            } else {
                setError(err.message)
            }
        } finally {
            setLoading(false)
        }
    }

    // Extract glucose data from query results
    const extractGlucoseData = (results) => {
        if (!results || results.length === 0) return []

        const glucoseReadings = []

        results.forEach(result => {
            // Handle different glucose data formats
            if (result['g.timestamp'] && result['g.glucose']) {
                glucoseReadings.push({
                    timestamp: result['g.timestamp'],
                    glucose: result['g.glucose'],
                    readingType: result['g.readingType'] || 'Unknown'
                })
            } else if (result.g) {
                glucoseReadings.push({
                    timestamp: result.g.timestamp || result.g['timestamp'],
                    glucose: result.g.glucose || result.g['glucose'],
                    readingType: result.g.readingType || result.g['readingType'] || 'Unknown'
                })
            }
        })

        return glucoseReadings.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
    }

    // Extract patient information
    const extractPatientInfo = (results, patientId) => {
        if (!results || results.length === 0) return null

        // Look for patient data in results
        for (const result of results) {
            if (result.p) {
                return {
                    patientId: result.p.patientId || patientId,
                    name: result.p.name,
                    age: result.p.age,
                    condition: result.p.condition,
                    gender: result.p.gender
                }
            } else if (result.patientName) {
                return {
                    patientId: patientId,
                    name: result.patientName,
                    condition: result.patientCondition
                }
            }
        }

        return { patientId: patientId }
    }

    const handleQuerySelect = (queryKey) => {
        const selectedQuery = commonQueries[queryKey]
        if (selectedQuery) {
            setQuery(selectedQuery.query)
            setSelectedQueryKey(queryKey)
        }
    }

    const handleParameterChange = (key, value) => {
        setQueryParameters(prev => ({
            ...prev,
            [key]: value
        }))
    }

    const formatResultsToPaths = (results) => {
        return results.map((result, index) => {
            if (result.path) {
                // Handle path results
                return {
                    id: index + 1,
                    nodes: extractNodesFromPath(result.path),
                    relationships: extractRelationshipsFromPath(result.path),
                    pathString: generatePathString(result.path)
                }
            } else {
                // Handle other result types - convert to simple paths
                const nodes = Object.values(result).filter(value =>
                    value && typeof value === 'object' && value.labels
                )

                return {
                    id: index + 1,
                    nodes: nodes.map(node => ({
                        id: node.id || `n${index}`,
                        labels: node.labels || ['Unknown'],
                        properties: node.properties || {}
                    })),
                    relationships: [],
                    pathString: nodes.map(node => `(${node.labels?.[0] || 'Unknown'})`).join('-')
                }
            }
        })
    }

    const extractNodesFromPath = (path) => {
        const nodes = [path.start]
        path.segments?.forEach(segment => {
            if (segment.end && !nodes.find(n => n.id === segment.end.id)) {
                nodes.push(segment.end)
            }
        })
        return nodes
    }

    const extractRelationshipsFromPath = (path) => {
        return path.segments?.map(segment => ({
            type: segment.relationship?.type || 'UNKNOWN',
            properties: segment.relationship?.properties || {}
        })) || []
    }

    const generatePathString = (path) => {
        if (!path.segments) return `(${path.start?.labels?.[0] || 'Node'})`

        let pathStr = `(${path.start?.labels?.[0] || 'Node'})`
        path.segments.forEach(segment => {
            pathStr += `-[${segment.relationship?.type || 'REL'}]->(${segment.end?.labels?.[0] || 'Node'})`
        })
        return pathStr
    }

    const renderNode = (node) => (
        <div key={node.id} className="node">
            <div className="node-header">
                <span className="node-labels">{node.labels.join(', ')}</span>
                <span className="node-id">ID: {node.id}</span>
            </div>
            <div className="node-properties">
                {Object.entries(node.properties).map(([key, value]) => (
                    <div key={key} className="property">
                        <span className="property-key">{key}:</span>
                        <span className="property-value">{value}</span>
                    </div>
                ))}
            </div>
        </div>
    )

    const renderRelationship = (relationship, index) => (
        <div key={index} className="relationship">
            <div className="relationship-type">{relationship.type}</div>
            {Object.keys(relationship.properties).length > 0 && (
                <div className="relationship-properties">
                    {Object.entries(relationship.properties).map(([key, value]) => (
                        <div key={key} className="property">
                            <span className="property-key">{key}:</span>
                            <span className="property-value">{value}</span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    )

    return (
        <div className="neo4j-path-viewer">
            <div className="viewer-header">
                <h2>Neo4j Path Viewer</h2>
                <div className="header-controls">
                    <div className="view-mode-toggle">
                        <button
                            className={`view-button ${viewMode === '2d' ? 'active' : ''}`}
                            onClick={() => setViewMode('2d')}
                        >
                            📋 2D View
                        </button>
                        <button
                            className={`view-button ${viewMode === '3d' ? 'active' : ''}`}
                            onClick={() => setViewMode('3d')}
                        >
                            🌐 3D View
                        </button>
                        <button
                            className={`view-button ${viewMode === 'agp' ? 'active' : ''}`}
                            onClick={() => setViewMode('agp')}
                            disabled={glucoseData.length === 0}
                            title={glucoseData.length === 0 ? "No glucose data available" : "View AGP Chart"}
                        >
                            📊 AGP Chart
                        </button>
                    </div>
                    <div className={`connection-status ${connectionStatus}`}>
                        <span className="status-indicator"></span>
                        Status: {connectionStatus}
                    </div>
                </div>
            </div>

            <div className="connection-panel">
                <h3>Database Connection</h3>
                <div className="connection-form">
                    <div className="form-row">
                        <div className="form-group">
                            <label>URI:</label>
                            <input
                                type="text"
                                value={connectionConfig.uri}
                                onChange={(e) => setConnectionConfig(prev => ({ ...prev, uri: e.target.value }))}
                                placeholder="bolt://localhost:7687"
                            />
                        </div>
                        <div className="form-group">
                            <label>Username:</label>
                            <input
                                type="text"
                                value={connectionConfig.username}
                                onChange={(e) => setConnectionConfig(prev => ({ ...prev, username: e.target.value }))}
                                placeholder="neo4j"
                            />
                        </div>
                        <div className="form-group">
                            <label>Password:</label>
                            <input
                                type="password"
                                value={connectionConfig.password}
                                onChange={(e) => setConnectionConfig(prev => ({ ...prev, password: e.target.value }))}
                                placeholder="Enter password"
                            />
                        </div>
                    </div>
                    <button
                        onClick={handleConnect}
                        disabled={loading}
                        className="connect-button"
                    >
                        {loading ? 'Connecting...' : 'Connect'}
                    </button>
                </div>
            </div>

            <div className="query-panel">
                <h3>Query Selection & Execution</h3>

                <div className="query-selector">
                    <h4>Common Queries</h4>
                    <div className="query-buttons">
                        {Object.entries(commonQueries).map(([key, queryInfo]) => (
                            <button
                                key={key}
                                onClick={() => handleQuerySelect(key)}
                                className={`query-button ${selectedQueryKey === key ? 'active' : ''}`}
                                title={queryInfo.description}
                            >
                                {queryInfo.name}
                            </button>
                        ))}
                    </div>
                </div>

                <div className="query-parameters">
                    <h4>Query Parameters</h4>
                    <div className="parameter-inputs">
                        <div className="parameter-input">
                            <label>Patient ID:</label>
                            <select
                                value={queryParameters.patientId}
                                onChange={(e) => handleParameterChange('patientId', e.target.value)}
                            >
                                <option value="Patient_1">Patient_1 - John Anderson</option>
                                <option value="Patient_2">Patient_2 - Maria Garcia</option>
                                <option value="Patient_3">Patient_3 - Robert Chen</option>
                                <option value="Patient_4">Patient_4 - Sarah Johnson</option>
                                <option value="Patient_5">Patient_5 - David Williams</option>
                                <option value="Patient_6">Patient_6 - Emily Davis</option>
                                <option value="Patient_7">Patient_7 - Michael Brown</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div className="query-form">
                    <label htmlFor="cypher-query">Custom Cypher Query:</label>
                    <textarea
                        id="cypher-query"
                        value={query}
                        onChange={(e) => setQuery(e.target.value)}
                        placeholder="Enter your Cypher query here..."
                        rows={4}
                        className="query-input"
                    />
                    <button
                        onClick={handleExecuteQuery}
                        disabled={loading || connectionStatus !== 'connected'}
                        className="execute-button"
                    >
                        {loading ? 'Executing...' : 'Execute Query'}
                    </button>
                </div>
            </div>

            {error && (
                <div className="error-message">
                    <strong>Error:</strong> {error}
                </div>
            )}

            <div className="results-panel">
                <h3>Query Results ({paths.length} paths)</h3>
                {paths.length === 0 ? (
                    <div className="no-results">
                        No paths found. Execute a query to see results.
                    </div>
                ) : (
                    <>
                        {viewMode === '3d' ? (
                            <Graph3DViewer
                                paths={paths}
                                className="graph-3d-view"
                            />
                        ) : viewMode === 'agp' ? (
                            <AGPChart
                                glucoseData={glucoseData}
                                patientInfo={patientInfo}
                                className="agp-chart-view"
                                width={900}
                                height={500}
                                onDataPointClick={(dataPoint) => {
                                    console.log('Glucose data point clicked:', dataPoint)
                                }}
                            />
                        ) : (
                            <div className="paths-container">
                                {paths.map((path) => (
                                    <div key={path.id} className="path-item">
                                        <div className="path-header">
                                            <h4>Path {path.id}</h4>
                                            <code className="path-string">{path.pathString}</code>
                                        </div>
                                        <div className="path-visualization">
                                            {path.nodes.map((node, index) => (
                                                <div key={node.id} className="path-segment">
                                                    {renderNode(node)}
                                                    {index < path.relationships.length && (
                                                        <>
                                                            <div className="arrow">→</div>
                                                            {renderRelationship(path.relationships[index], index)}
                                                            <div className="arrow">→</div>
                                                        </>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </>
                )}
            </div>

            {/* Enhanced Data Summary */}
            {(glucoseData.length > 0 || paths.length > 0) && (
                <div className="data-summary">
                    <h4>📊 Query Summary</h4>
                    <div className="summary-stats">
                        <div className="summary-item">
                            <span className="summary-label">Paths Found:</span>
                            <span className="summary-value">{paths.length}</span>
                        </div>
                        {glucoseData.length > 0 && (
                            <div className="summary-item">
                                <span className="summary-label">Glucose Readings:</span>
                                <span className="summary-value">{glucoseData.length}</span>
                            </div>
                        )}
                        {patientInfo && (
                            <div className="summary-item">
                                <span className="summary-label">Patient:</span>
                                <span className="summary-value">{patientInfo.name || patientInfo.patientId}</span>
                            </div>
                        )}
                        <div className="summary-item">
                            <span className="summary-label">Query Type:</span>
                            <span className="summary-value">
                                {selectedQueryKey ? commonQueries[selectedQueryKey]?.name : 'Custom Query'}
                            </span>
                        </div>
                    </div>

                    {glucoseData.length > 0 && (
                        <div className="quick-glucose-stats">
                            <h5>🩺 Quick Glucose Overview</h5>
                            <div className="glucose-summary">
                                <span>Average: {(glucoseData.reduce((sum, r) => sum + r.glucose, 0) / glucoseData.length).toFixed(1)} mg/dL</span>
                                <span>Range: {Math.min(...glucoseData.map(r => r.glucose))} - {Math.max(...glucoseData.map(r => r.glucose))} mg/dL</span>
                                <span>Latest: {glucoseData[glucoseData.length - 1]?.glucose} mg/dL</span>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </div>
    )
}

export default Neo4jPathViewer
