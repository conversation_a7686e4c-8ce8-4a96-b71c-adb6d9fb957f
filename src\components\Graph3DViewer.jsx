import { Html, Line, OrbitControls, Text } from '@react-three/drei'
import { Canvas, useFrame, useThree } from '@react-three/fiber'
import { useEffect, useMemo, useRef, useState } from 'react'
import * as THREE from 'three'

// Enhanced 3D Node Component with animations and interactions
function Node3D({ position, node, onClick, isSelected, isHovered, onHover, isConnected, animationSpeed = 1 }) {
    const meshRef = useRef()
    const ringRef = useRef()
    const particlesRef = useRef()
    const [scale, setScale] = useState(1)
    const [pulseIntensity, setPulseIntensity] = useState(0)

    // Get color based on node labels with improved color mapping
    const getNodeColor = (labels) => {
        const colorMap = {
            'Patient': '#ff6b6b',        // Red - High importance
            'Condition': '#4ecdc4',      // Teal - Medical condition
            'Treatment': '#45b7d1',      // Blue - Treatment
            'Medication': '#96ceb4',     // Green - Medication
            'Researcher': '#ffeaa7',     // Yellow - Research
            'Study': '#dda0dd',          // Purple - Study
            'Publication': '#98fb98',    // Light green - Publication
            'Hospital': '#f4a261',       // Orange - Institution
            'Department': '#2a9d8f',     // Dark teal - Department
            'Equipment': '#e76f51',      // Dark orange - Equipment
            'Diagnosis': '#e9c46a',      // Gold - Diagnosis
            'Doctor': '#264653',         // Dark green - Doctor
            'GlucoseReading': '#ff9ff3', // Pink - Glucose data
            'LabResult': '#54a0ff',      // Light blue - Lab results
            'Prescription': '#5f27cd',   // Purple - Prescriptions
            'WeightMeasurement': '#00d2d3' // Cyan - Weight data
        }

        const primaryLabel = labels[0]
        return colorMap[primaryLabel] || '#888888'
    }

    // Get node importance based on type
    const getNodeImportance = (labels) => {
        const importanceMap = {
            'Patient': 1.0,
            'Doctor': 0.9,
            'Condition': 0.8,
            'Treatment': 0.7,
            'Medication': 0.6,
            'GlucoseReading': 0.5,
            'LabResult': 0.4,
            'default': 0.3
        }
        return importanceMap[labels[0]] || importanceMap.default
    }

    const nodeColor = getNodeColor(node.labels)
    const importance = getNodeImportance(node.labels)
    const baseSize = 0.3 + (importance * 0.4) // Size based on importance
    const nodeSize = isSelected ? baseSize * 2 : isHovered ? baseSize * 1.5 : baseSize

    // Animation loop for enhanced visual effects
    useFrame((state) => {
        if (meshRef.current) {
            // Smooth scaling animation
            meshRef.current.scale.setScalar(
                THREE.MathUtils.lerp(meshRef.current.scale.x, nodeSize, 0.1 * animationSpeed)
            )

            // Rotation based on node type and selection state
            if (isSelected) {
                meshRef.current.rotation.y += 0.02 * animationSpeed
                meshRef.current.rotation.x += 0.01 * animationSpeed
            } else {
                meshRef.current.rotation.y += 0.005 * animationSpeed
            }
        }

        // Animated selection ring
        if (ringRef.current && isSelected) {
            ringRef.current.rotation.z += 0.03 * animationSpeed
            const scale = 1 + Math.sin(state.clock.elapsedTime * 3) * 0.1
            ringRef.current.scale.setScalar(scale)
        }

        // Pulse effect for connected nodes
        if (isConnected && meshRef.current) {
            const pulse = 1 + Math.sin(state.clock.elapsedTime * 2) * 0.1
            meshRef.current.material.emissiveIntensity = pulse * 0.2
        }
    })

    return (
        <group position={position}>
            {/* Main Node Sphere */}
            <mesh
                ref={meshRef}
                onClick={(e) => {
                    e.stopPropagation()
                    onClick?.(node)
                }}
                onPointerOver={(e) => {
                    e.stopPropagation()
                    onHover?.(node, true)
                    document.body.style.cursor = 'pointer'
                }}
                onPointerOut={(e) => {
                    e.stopPropagation()
                    onHover?.(node, false)
                    document.body.style.cursor = 'default'
                }}
                castShadow
                receiveShadow
            >
                <sphereGeometry args={[0.5, 20, 20]} />
                <meshStandardMaterial
                    color={nodeColor}
                    emissive={isSelected || isConnected ? nodeColor : '#000000'}
                    emissiveIntensity={isSelected ? 0.4 : isConnected ? 0.2 : 0}
                    metalness={0.2}
                    roughness={0.3}
                    transparent={true}
                    opacity={isHovered ? 1.0 : 0.9}
                />
            </mesh>

            {/* Selection Ring for selected nodes */}
            {isSelected && (
                <mesh ref={ringRef} position={[0, 0, 0]}>
                    <ringGeometry args={[0.8, 1.0, 32]} />
                    <meshBasicMaterial
                        color={nodeColor}
                        transparent={true}
                        opacity={0.6}
                        side={THREE.DoubleSide}
                    />
                </mesh>
            )}

            {/* Node Type Indicator */}
            <mesh position={[0, 0.7, 0]}>
                <sphereGeometry args={[0.1, 8, 8]} />
                <meshBasicMaterial color={nodeColor} />
            </mesh>

            {/* Node Label with improved styling */}
            <Text
                position={[0, 1.0, 0]}
                fontSize={0.25}
                color="#333333"
                anchorX="center"
                anchorY="middle"
                maxWidth={4}
                textAlign="center"
                font="https://fonts.googleapis.com/css2?family=Inter:wght@600&display=swap"
            >
                {node.labels[0]}
            </Text>

            {/* Node Name/ID Label */}
            <Text
                position={[0, -0.8, 0]}
                fontSize={0.15}
                color="#666666"
                anchorX="center"
                anchorY="middle"
                maxWidth={6}
                textAlign="center"
            >
                {node.properties.name || node.properties.patientId || `ID: ${node.id}`}
            </Text>

            {/* Enhanced Node Info Tooltip */}
            {isHovered && (
                <Html position={[0, 1.5, 0]} center>
                    <div className="node-tooltip enhanced">
                        <div className="tooltip-header">
                            <strong style={{ color: nodeColor }}>{node.labels.join(', ')}</strong>
                            <span className="tooltip-id">#{node.id}</span>
                        </div>
                        <div className="tooltip-body">
                            {Object.entries(node.properties).slice(0, 4).map(([key, value]) => (
                                <div key={key} className="tooltip-property">
                                    <span className="tooltip-key">{key}:</span>
                                    <span className="tooltip-value">{String(value)}</span>
                                </div>
                            ))}
                            {Object.keys(node.properties).length > 4 && (
                                <div className="tooltip-more">
                                    +{Object.keys(node.properties).length - 4} more properties...
                                </div>
                            )}
                        </div>
                    </div>
                </Html>
            )}
        </group>
    )
}

// Enhanced 3D Relationship Component with animations and styling
function Relationship3D({ start, end, relationship, isHighlighted, isSelected, animationSpeed = 1 }) {
    const lineRef = useRef()
    const arrowRef = useRef()

    const points = useMemo(() => [
        new THREE.Vector3(...start),
        new THREE.Vector3(...end)
    ], [start, end])

    const midpoint = useMemo(() => [
        (start[0] + end[0]) / 2,
        (start[1] + end[1]) / 2 + 0.2, // Slightly elevated for better visibility
        (start[2] + end[2]) / 2
    ], [start, end])

    // Calculate arrow position and orientation
    const arrowPosition = useMemo(() => {
        const direction = new THREE.Vector3(...end).sub(new THREE.Vector3(...start))
        const length = direction.length()
        direction.normalize()
        const arrowPos = new THREE.Vector3(...start).add(direction.multiplyScalar(length * 0.8))
        return [arrowPos.x, arrowPos.y, arrowPos.z]
    }, [start, end])

    // Animation for highlighted relationships
    useFrame((state) => {
        if (isHighlighted && lineRef.current) {
            const pulse = 1 + Math.sin(state.clock.elapsedTime * 4) * 0.2
            // Note: Line component doesn't have scale, so we'll use opacity changes
        }

        if (arrowRef.current && (isHighlighted || isSelected)) {
            arrowRef.current.rotation.y += 0.03 * animationSpeed
        }
    })

    // Get relationship color based on type
    const getRelationshipColor = (type) => {
        const colorMap = {
            'HAS_CONDITION': '#ff6b6b',
            'REQUIRES_TREATMENT': '#45b7d1',
            'PRESCRIBED': '#96ceb4',
            'HAD_READING': '#ff9ff3',
            'HAD_LAB_RESULT': '#54a0ff',
            'TREATED_BY': '#264653',
            'WORKS_IN': '#f4a261',
            'CONDUCTS_RESEARCH': '#ffeaa7',
            'default': '#666666'
        }
        return colorMap[type] || colorMap.default
    }

    const relationshipColor = getRelationshipColor(relationship.type)
    const lineWidth = isHighlighted ? 4 : isSelected ? 3 : 2

    return (
        <group>
            {/* Relationship Line */}
            <Line
                ref={lineRef}
                points={points}
                color={relationshipColor}
                lineWidth={lineWidth}
                transparent={true}
                opacity={isHighlighted ? 0.9 : 0.7}
            />

            {/* Relationship Label with background */}
            <Html position={midpoint} center>
                <div className={`relationship-label ${isHighlighted ? 'highlighted' : ''}`}>
                    <span className="rel-type" style={{ borderColor: relationshipColor }}>
                        {relationship.type.replace('_', ' ')}
                    </span>
                </div>
            </Html>

            {/* Arrow indicating direction */}
            <mesh ref={arrowRef} position={arrowPosition}>
                <coneGeometry args={[0.08, 0.15, 8]} />
                <meshStandardMaterial
                    color={relationshipColor}
                    emissive={isHighlighted ? relationshipColor : '#000000'}
                    emissiveIntensity={isHighlighted ? 0.3 : 0}
                />
            </mesh>
        </group>
    )
}

// Camera Controller
function CameraController({ selectedNode }) {
    const { camera } = useThree()

    useEffect(() => {
        if (selectedNode && selectedNode.position) {
            const targetPosition = new THREE.Vector3(...selectedNode.position)
            targetPosition.add(new THREE.Vector3(0, 0, 5))

            // Smooth camera movement
            const startPosition = camera.position.clone()
            let progress = 0

            const animate = () => {
                progress += 0.05
                if (progress < 1) {
                    camera.position.lerpVectors(startPosition, targetPosition, progress)
                    requestAnimationFrame(animate)
                }
            }
            animate()
        }
    }, [selectedNode, camera])

    return null
}

// Enhanced Main 3D Graph Scene with improved layouts and interactions
function GraphScene({
    paths,
    onNodeClick,
    selectedNodeId,
    onNodeHover,
    hoveredNodeId,
    layoutType = 'spiral',
    animationSpeed = 1,
    showGrid = true,
    autoRotate = false,
    showStats = false
}) {
    const [nodes, setNodes] = useState([])
    const [relationships, setRelationships] = useState([])
    const [connectedNodes, setConnectedNodes] = useState(new Set())

    // Enhanced layout algorithms
    const calculateNodePosition = (nodeIndex, totalNodes, layoutType) => {
        switch (layoutType) {
            case 'circular': {
                const angle = (nodeIndex * 2 * Math.PI) / totalNodes
                const circularRadius = 4 + Math.floor(nodeIndex / 12) * 2
                return [
                    Math.cos(angle) * circularRadius,
                    Math.sin(nodeIndex * 0.3) * 2,
                    Math.sin(angle) * circularRadius
                ]
            }

            case 'grid': {
                const gridSize = Math.ceil(Math.sqrt(totalNodes))
                const x = (nodeIndex % gridSize - gridSize / 2) * 2
                const z = (Math.floor(nodeIndex / gridSize) - gridSize / 2) * 2
                const y = Math.sin(x * 0.3) * Math.cos(z * 0.3) * 1.5
                return [x, y, z]
            }

            case 'sphere': {
                const phi = Math.acos(-1 + (2 * nodeIndex) / totalNodes)
                const theta = Math.sqrt(totalNodes * Math.PI) * phi
                const sphereRadius = 5
                return [
                    sphereRadius * Math.cos(theta) * Math.sin(phi),
                    sphereRadius * Math.cos(phi),
                    sphereRadius * Math.sin(theta) * Math.sin(phi)
                ]
            }

            case 'hierarchical': {
                const level = Math.floor(nodeIndex / 8)
                const posInLevel = nodeIndex % 8
                const angleInLevel = (posInLevel * 2 * Math.PI) / 8
                const levelRadius = 2 + level * 2.5
                return [
                    Math.cos(angleInLevel) * levelRadius,
                    level * 2 - 3,
                    Math.sin(angleInLevel) * levelRadius
                ]
            }

            default: { // spiral
                const spiralAngle = (nodeIndex * 2.4) % (Math.PI * 2)
                const spiralRadius = 3 + Math.floor(nodeIndex / 8) * 2
                const spiralHeight = (Math.floor(nodeIndex / 8) - 1) * 2
                return [
                    Math.cos(spiralAngle) * spiralRadius,
                    spiralHeight + Math.sin(nodeIndex * 0.5) * 0.5,
                    Math.sin(spiralAngle) * spiralRadius
                ]
            }
        }
    }

    // Convert paths to 3D positioned nodes and relationships with enhanced layout
    useEffect(() => {
        if (!paths || paths.length === 0) return

        const allNodes = new Map()
        const allRelationships = []
        const nodeConnections = new Set()

        paths.forEach((path, pathIndex) => {
            path.nodes.forEach((node, nodeIndex) => {
                if (!allNodes.has(node.id)) {
                    // Use selected layout algorithm for positioning
                    const position = calculateNodePosition(allNodes.size, paths.reduce((sum, p) => sum + p.nodes.length, 0), layoutType)

                    allNodes.set(node.id, {
                        ...node,
                        position: position,
                        pathCount: 1 // Track how many paths this node appears in
                    })
                } else {
                    // Increment path count for nodes that appear in multiple paths
                    const existingNode = allNodes.get(node.id)
                    existingNode.pathCount = (existingNode.pathCount || 1) + 1
                }
            })

            // Create relationships between consecutive nodes in the path
            for (let i = 0; i < path.nodes.length - 1; i++) {
                const startNode = allNodes.get(path.nodes[i].id)
                const endNode = allNodes.get(path.nodes[i + 1].id)
                const relationship = path.relationships[i]

                if (startNode && endNode && relationship) {
                    allRelationships.push({
                        id: `${path.id}-${i}`,
                        start: startNode.position,
                        end: endNode.position,
                        relationship: relationship,
                        startNodeId: startNode.id,
                        endNodeId: endNode.id,
                        pathId: path.id
                    })

                    // Track connected nodes
                    nodeConnections.add(startNode.id)
                    nodeConnections.add(endNode.id)
                }
            }
        })

        setNodes(Array.from(allNodes.values()))
        setRelationships(allRelationships)
        setConnectedNodes(nodeConnections)
    }, [paths, layoutType])

    return (
        <>
            {/* Enhanced Lighting Setup */}
            <ambientLight intensity={0.4} />
            <pointLight position={[10, 10, 10]} intensity={0.8} castShadow />
            <pointLight position={[-10, -10, -10]} intensity={0.4} />
            <pointLight position={[0, 10, 0]} intensity={0.3} />
            <directionalLight position={[5, 5, 5]} intensity={0.2} />

            {/* Camera Controller */}
            <CameraController selectedNode={nodes.find(n => n.id === selectedNodeId)} />

            {/* Render Nodes with enhanced interactivity */}
            {nodes.map((node) => (
                <Node3D
                    key={node.id}
                    position={node.position}
                    node={node}
                    onClick={onNodeClick}
                    isSelected={selectedNodeId === node.id}
                    isHovered={hoveredNodeId === node.id}
                    onHover={onNodeHover}
                    isConnected={connectedNodes.has(node.id)}
                    animationSpeed={animationSpeed}
                />
            ))}

            {/* Render Relationships with enhanced styling */}
            {relationships.map((rel) => (
                <Relationship3D
                    key={rel.id}
                    start={rel.start}
                    end={rel.end}
                    relationship={rel.relationship}
                    isHighlighted={
                        selectedNodeId === rel.startNodeId ||
                        selectedNodeId === rel.endNodeId ||
                        hoveredNodeId === rel.startNodeId ||
                        hoveredNodeId === rel.endNodeId
                    }
                    isSelected={selectedNodeId === rel.startNodeId || selectedNodeId === rel.endNodeId}
                    animationSpeed={animationSpeed}
                />
            ))}

            {/* Optional Grid Helper */}
            {showGrid && (
                <gridHelper args={[30, 30, '#444444', '#666666']} position={[0, -5, 0]} />
            )}

            {/* Enhanced Orbit Controls */}
            <OrbitControls
                enablePan={true}
                enableZoom={true}
                enableRotate={true}
                minDistance={3}
                maxDistance={50}
                autoRotate={autoRotate}
                autoRotateSpeed={0.3}
                panSpeed={0.8}
                rotateSpeed={0.5}
                zoomSpeed={0.6}
                minPolarAngle={0}
                maxPolarAngle={Math.PI}
            />
        </>
    )
}

// Enhanced Main 3D Graph Viewer Component with comprehensive controls
function Graph3DViewer({ paths, className = "" }) {
    const [selectedNodeId, setSelectedNodeId] = useState(null)
    const [hoveredNodeId, setHoveredNodeId] = useState(null)
    const [selectedNodeInfo, setSelectedNodeInfo] = useState(null)
    const [layoutType, setLayoutType] = useState('spiral')
    const [animationSpeed, setAnimationSpeed] = useState(1)
    const [showGrid, setShowGrid] = useState(true)
    const [autoRotate, setAutoRotate] = useState(false)
    const [filterNodeType, setFilterNodeType] = useState('all')
    const [searchQuery, setSearchQuery] = useState('')
    const [filteredPaths, setFilteredPaths] = useState(paths || [])

    // Filter and search functionality
    useEffect(() => {
        if (!paths) {
            setFilteredPaths([])
            return
        }

        let filtered = paths

        // Filter by node type
        if (filterNodeType !== 'all') {
            filtered = filtered.filter(path =>
                path.nodes.some(node => node.labels.includes(filterNodeType))
            )
        }

        // Filter by search query
        if (searchQuery) {
            const query = searchQuery.toLowerCase()
            filtered = filtered.filter(path =>
                path.nodes.some(node =>
                    node.labels.some(label => label.toLowerCase().includes(query)) ||
                    Object.values(node.properties).some(prop =>
                        String(prop).toLowerCase().includes(query)
                    )
                )
            )
        }

        setFilteredPaths(filtered)
    }, [paths, filterNodeType, searchQuery])

    // Get unique node types for filtering
    const getUniqueNodeTypes = () => {
        if (!paths) return []
        const types = new Set()
        paths.forEach(path => {
            path.nodes.forEach(node => {
                node.labels.forEach(label => types.add(label))
            })
        })
        return Array.from(types).sort()
    }

    const handleNodeClick = (node) => {
        setSelectedNodeId(node.id)
        setSelectedNodeInfo(node)
    }

    const handleNodeHover = (node, isHovering) => {
        setHoveredNodeId(isHovering ? node.id : null)
    }

    const resetView = () => {
        setSelectedNodeId(null)
        setSelectedNodeInfo(null)
        setHoveredNodeId(null)
        setSearchQuery('')
        setFilterNodeType('all')
    }

    const exportGraphData = () => {
        const graphData = {
            nodes: filteredPaths.reduce((acc, path) => {
                path.nodes.forEach(node => {
                    if (!acc.find(n => n.id === node.id)) {
                        acc.push(node)
                    }
                })
                return acc
            }, []),
            relationships: filteredPaths.reduce((acc, path) => {
                path.relationships.forEach((rel, index) => {
                    acc.push({
                        ...rel,
                        pathId: path.id,
                        index
                    })
                })
                return acc
            }, []),
            metadata: {
                totalPaths: filteredPaths.length,
                layout: layoutType,
                exportDate: new Date().toISOString()
            }
        }

        const blob = new Blob([JSON.stringify(graphData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `graph-data-${Date.now()}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
    }

    if (!paths || paths.length === 0) {
        return (
            <div className={`graph-3d-container ${className}`}>
                <div className="no-data-message">
                    <h3>No Path Data Available</h3>
                    <p>Execute a query to visualize healthcare data paths in interactive 3D</p>
                    <div className="getting-started-tips">
                        <h4>💡 Getting Started:</h4>
                        <ul>
                            <li>Try a patient query to see medical connections</li>
                            <li>Use glucose data queries for AGP visualization</li>
                            <li>Explore doctor-patient relationships</li>
                        </ul>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className={`graph-3d-container ${className}`}>
            {/* Enhanced 3D Controls Panel */}
            <div className="graph-controls-panel">
                <div className="controls-section">
                    <div className="basic-controls">
                        <button onClick={resetView} className="control-button primary" title="Reset View">
                            🔄 Reset View
                        </button>
                        <button onClick={exportGraphData} className="control-button secondary" title="Export Data">
                            📊 Export Data
                        </button>
                    </div>

                    <div className="layout-controls">
                        <label htmlFor="layout-select">Layout:</label>
                        <select
                            id="layout-select"
                            value={layoutType}
                            onChange={(e) => setLayoutType(e.target.value)}
                            className="control-select"
                        >
                            <option value="spiral">Spiral</option>
                            <option value="circular">Circular</option>
                            <option value="grid">Grid</option>
                            <option value="sphere">Sphere</option>
                            <option value="hierarchical">Hierarchical</option>
                        </select>
                    </div>

                    <div className="filter-controls">
                        <input
                            type="text"
                            placeholder="Search nodes..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="search-input"
                        />
                        <select
                            value={filterNodeType}
                            onChange={(e) => setFilterNodeType(e.target.value)}
                            className="filter-select"
                        >
                            <option value="all">All Types</option>
                            {getUniqueNodeTypes().map(type => (
                                <option key={type} value={type}>{type}</option>
                            ))}
                        </select>
                    </div>
                </div>

                <div className="view-stats">
                    <div className="stat-item">
                        <span className="stat-label">Nodes:</span>
                        <span className="stat-value">
                            {filteredPaths.reduce((sum, path) => sum + path.nodes.length, 0)}
                        </span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">Paths:</span>
                        <span className="stat-value">{filteredPaths.length}</span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">Layout:</span>
                        <span className="stat-value">{layoutType}</span>
                    </div>
                </div>

                <div className="animation-controls">
                    <label htmlFor="animation-speed">Animation Speed:</label>
                    <input
                        id="animation-speed"
                        type="range"
                        min="0.1"
                        max="2"
                        step="0.1"
                        value={animationSpeed}
                        onChange={(e) => setAnimationSpeed(parseFloat(e.target.value))}
                        className="speed-slider"
                    />
                    <span className="speed-value">{animationSpeed}x</span>

                    <div className="toggle-controls">
                        <label className="toggle-label">
                            <input
                                type="checkbox"
                                checked={showGrid}
                                onChange={(e) => setShowGrid(e.target.checked)}
                            />
                            Show Grid
                        </label>
                        <label className="toggle-label">
                            <input
                                type="checkbox"
                                checked={autoRotate}
                                onChange={(e) => setAutoRotate(e.target.checked)}
                            />
                            Auto Rotate
                        </label>
                    </div>
                </div>
            </div>

            {/* Enhanced 3D Canvas */}
            <Canvas
                camera={{ position: [0, 8, 15], fov: 60 }}
                style={{
                    width: '100%',
                    height: '600px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '12px',
                    overflow: 'hidden'
                }}
                gl={{
                    antialias: true,
                    alpha: true,
                    powerPreference: "high-performance",
                    shadowMap: true,
                    shadowMapType: 'PCFSoftShadowMap'
                }}
                shadows
                onCreated={({ gl, scene }) => {
                    gl.setClearColor('#667eea', 1)
                    gl.shadowMap.enabled = true
                    gl.shadowMap.type = THREE.PCFSoftShadowMap

                    // Add fog for depth perception
                    scene.fog = new THREE.Fog('#667eea', 10, 100)
                }}
            >
                <GraphScene
                    paths={filteredPaths}
                    onNodeClick={handleNodeClick}
                    selectedNodeId={selectedNodeId}
                    onNodeHover={handleNodeHover}
                    hoveredNodeId={hoveredNodeId}
                    layoutType={layoutType}
                    animationSpeed={animationSpeed}
                    showGrid={showGrid}
                    autoRotate={autoRotate}
                />
            </Canvas>

            {/* Enhanced Selected Node Info Panel */}
            {selectedNodeInfo && (
                <div className="selected-node-panel enhanced">
                    <div className="panel-header">
                        <div className="panel-title">
                            <h4>🎯 Selected Node</h4>
                            <span className="node-id">#{selectedNodeInfo.id}</span>
                        </div>
                        <button onClick={() => setSelectedNodeInfo(null)} className="close-button">×</button>
                    </div>
                    <div className="panel-content">
                        <div className="node-labels">
                            {selectedNodeInfo.labels.map((label, index) => (
                                <span key={index} className={`node-label label-${label.toLowerCase()}`}>
                                    {label}
                                </span>
                            ))}
                        </div>

                        <div className="node-properties">
                            <h5>📋 Properties</h5>
                            {Object.entries(selectedNodeInfo.properties).map(([key, value]) => (
                                <div key={key} className="property-row">
                                    <span className="property-key">{key}:</span>
                                    <span className="property-value" title={String(value)}>
                                        {String(value).length > 50 ? `${String(value).substring(0, 50)}...` : String(value)}
                                    </span>
                                </div>
                            ))}
                        </div>

                        {/* Connection Analysis */}
                        <div className="node-connections">
                            <h5>🔗 Connections</h5>
                            <div className="connection-stats">
                                <div className="connection-item">
                                    <span className="connection-label">Direct Connections:</span>
                                    <span className="connection-count">
                                        {filteredPaths.filter(path =>
                                            path.nodes.some(node => node.id === selectedNodeInfo.id)
                                        ).length}
                                    </span>
                                </div>
                                <div className="connection-item">
                                    <span className="connection-label">Node Type:</span>
                                    <span className="connection-type">{selectedNodeInfo.labels[0]}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Enhanced Interactive Guide */}
            <div className="interaction-guide">
                <div className="guide-header">
                    <h5>🎮 Interactive 3D Controls</h5>
                    <div className="guide-toggle">
                        <span className="guide-status">Layout: {layoutType}</span>
                    </div>
                </div>
                <div className="guide-content">
                    <div className="control-section">
                        <div className="control-group">
                            <strong>🖱️ Mouse Controls:</strong>
                            <ul>
                                <li><kbd>Left Click + Drag</kbd> - Rotate view</li>
                                <li><kbd>Right Click + Drag</kbd> - Pan view</li>
                                <li><kbd>Scroll Wheel</kbd> - Zoom in/out</li>
                                <li><kbd>Click Node</kbd> - Select and inspect</li>
                                <li><kbd>Hover Node</kbd> - Quick info tooltip</li>
                            </ul>
                        </div>
                        <div className="control-group">
                            <strong>⚙️ Features:</strong>
                            <ul>
                                <li>Multiple layout algorithms</li>
                                <li>Real-time search and filtering</li>
                                <li>Node type-based color coding</li>
                                <li>Relationship highlighting</li>
                                <li>Data export functionality</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            {/* Quick Stats Footer */}
            <div className="graph-footer">
                <div className="performance-stats">
                    <span>🔍 Showing {filteredPaths.length} of {paths.length} paths</span>
                    {searchQuery && <span>📝 Search: "{searchQuery}"</span>}
                    {filterNodeType !== 'all' && <span>🏷️ Filter: {filterNodeType}</span>}
                </div>
                <div className="layout-info">
                    <span>📐 {layoutType} Layout</span>
                    <span>⚡ {animationSpeed}x Speed</span>
                </div>
            </div>
        </div>
    )
}

export default Graph3DViewer
