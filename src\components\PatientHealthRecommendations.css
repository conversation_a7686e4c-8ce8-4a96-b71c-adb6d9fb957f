.patient-health-recommendations {
  padding: 2rem;
  background: var(--gb-bg1);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  margin: 1rem 0;
  max-width: 100%;
  color: var(--gb-fg);
}

.health-recommendations-empty {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(235, 219, 178, 0.75);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.recommendations-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-content h2 {
  margin: 0 0 0.5rem 0;
  color: var(--gb-accent2);
  font-size: 1.75rem;
  font-weight: 700;
}

.header-content p {
  margin: 0;
  color: rgba(235, 219, 178, 0.75);
  font-size: 1rem;
  line-height: 1.5;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.technique-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.technique-selector label {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(235, 219, 178, 0.85);
}

.technique-selector select {
  padding: 0.5rem;
  border: 1px solid rgba(235, 219, 178, 0.25);
  border-radius: 6px;
  background: rgba(235, 219, 178, 0.05);
  color: var(--gb-fg);
  font-size: 0.9rem;
  min-width: 160px;
}

.refresh-btn {
  padding: 0.75rem 1rem;
  background: transparent;
  color: var(--gb-accent2);
  border: 1px solid var(--gb-accent2);
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(250, 189, 47, 0.1);
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-state {
  text-align: center;
  padding: 3rem 2rem;
  color: rgba(235, 219, 178, 0.75);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(235, 219, 178, 0.15);
  border-top: 4px solid var(--gb-accent2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.error-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #ffb4ab;
  background: rgba(220, 53, 69, 0.08);
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 8px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.retry-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(220, 53, 69, 0.2);
  color: #ffb4ab;
  border: 1px solid rgba(220, 53, 69, 0.35);
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 1rem;
}

.retry-btn:hover {
  background: rgba(220, 53, 69, 0.3);
}

.recommendations-summary {
  background: linear-gradient(135deg, var(--gb-bg1), rgba(215, 153, 33, 0.06));
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.summary-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gb-accent2);
  font-family: 'Courier New', monospace;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(235, 219, 178, 0.75);
  font-weight: 500;
}

.technique-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.technique-badge {
  background: rgba(250, 189, 47, 0.2);
  color: var(--gb-accent2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.generation-time {
  font-size: 0.8rem;
  color: rgba(235, 219, 178, 0.7);
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.recommendation-card {
  border: 1px solid rgba(235, 219, 178, 0.12);
  border-radius: 12px;
  background: var(--gb-bg1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  transition: all 0.2s ease;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.35);
  border-color: var(--gb-accent2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem 0.5rem 1.5rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-badge {
  background: rgba(235, 219, 178, 0.2);
  color: var(--gb-fg);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.difficulty-indicator {
  font-size: 1.2rem;
  cursor: help;
}

.priority-badge {
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.card-body {
  padding: 0.5rem 1.5rem 1rem 1.5rem;
}

.recommendation-title {
  margin: 0 0 0.75rem 0;
  color: var(--gb-fg);
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.3;
}

.recommendation-description {
  margin: 0 0 1rem 0;
  color: rgba(235, 219, 178, 0.85);
  line-height: 1.5;
  font-size: 0.95rem;
}

.benefit-section,
.timeline-section {
  margin: 0.75rem 0;
  padding: 0.75rem;
  background: rgba(235, 219, 178, 0.05);
  border-radius: 6px;
  border-left: 4px solid var(--gb-accent);
}

.benefit-section strong,
.timeline-section strong {
  display: block;
  color: rgba(235, 219, 178, 0.9);
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.benefit-section span,
.timeline-section span {
  color: var(--gb-fg);
  font-size: 0.9rem;
}

.actions-section {
  margin: 1rem 0;
  border-top: 1px solid rgba(235, 219, 178, 0.12);
  padding-top: 1rem;
}

.actions-toggle {
  margin: 0 0 0.75rem 0;
  color: var(--gb-accent2);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.2s ease;
}

.actions-toggle:hover {
  color: var(--gb-accent);
}

.action-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.action-item {
  transition: all 0.2s ease;
}

.action-item.completed {
  opacity: 0.6;
}

.action-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.action-checkbox:hover {
  background: rgba(235, 219, 178, 0.05);
}

.action-checkbox input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(235, 219, 178, 0.25);
  border-radius: 3px;
  position: relative;
  flex-shrink: 0;
  background: var(--gb-bg1);
  transition: all 0.2s ease;
}

.action-checkbox input[type="checkbox"]:checked+.checkmark {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.5);
}

.action-checkbox input[type="checkbox"]:checked+.checkmark::after {
  content: '✓';
  position: absolute;
  color: #10b981;
  font-size: 12px;
  font-weight: bold;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.action-text {
  flex: 1;
  color: rgba(235, 219, 178, 0.85);
  font-size: 0.9rem;
  line-height: 1.4;
}

.action-item.completed .action-text {
  text-decoration: line-through;
  color: rgba(235, 219, 178, 0.6);
}

.success-metrics {
  margin: 1rem 0;
  padding: 0.75rem;
  background: rgba(23, 162, 184, 0.08);
  border: 1px solid rgba(23, 162, 184, 0.3);
  border-radius: 6px;
}

.success-metrics h5 {
  margin: 0 0 0.5rem 0;
  color: #17a2b8;
  font-size: 0.9rem;
}

.success-metrics ul {
  margin: 0;
  padding-left: 1.5rem;
}

.success-metrics li {
  color: rgba(235, 219, 178, 0.85);
  font-size: 0.85rem;
  margin: 0.25rem 0;
}

.card-footer {
  padding: 0.75rem 1.5rem;
  background: rgba(235, 219, 178, 0.05);
  border-top: 1px solid rgba(235, 219, 178, 0.12);
}

.expand-btn {
  background: none;
  border: none;
  color: var(--gb-accent2);
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0;
  font-weight: 500;
  transition: color 0.2s ease;
}

.expand-btn:hover {
  color: var(--gb-accent);
}

.recommendations-footer {
  border-top: 1px solid rgba(235, 219, 178, 0.12);
  padding-top: 2rem;
  margin-top: 2rem;
}

.disclaimer {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.disclaimer p {
  margin: 0;
  color: #f59e0b;
  font-size: 0.9rem;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.share-btn,
.print-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--gb-accent2);
  background: transparent;
  color: var(--gb-accent2);
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.share-btn:hover,
.print-btn:hover {
  background: rgba(250, 189, 47, 0.1);
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .patient-health-recommendations {
    padding: 1rem;
  }

  .recommendations-header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-controls {
    justify-content: space-between;
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
  }

  .summary-stats {
    justify-content: center;
  }

  .technique-info {
    align-items: center;
  }
}

@media (max-width: 480px) {
  .recommendations-header h2 {
    font-size: 1.5rem;
  }

  .header-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .technique-selector {
    width: 100%;
  }

  .technique-selector select {
    min-width: unset;
    width: 100%;
  }

  .summary-stats {
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* Print styles */
@media print {
  .patient-health-recommendations {
    box-shadow: none;
    border: 1px solid #000;
  }

  .header-controls,
  .action-buttons,
  .expand-btn {
    display: none;
  }

  .recommendation-card {
    break-inside: avoid;
    border: 1px solid #000;
    margin-bottom: 1rem;
  }

  .actions-section {
    display: block !important;
  }

  .action-items {
    display: block !important;
  }
}
