import { useEffect, useState } from 'react';
import './SettingsPreferences.css';

const SettingsPreferences = ({ onSettingsChange }) => {
    const [settings, setSettings] = useState({
        // General Settings
        general: {
            theme: 'gruvbox-dark',
            language: 'en',
            timezone: 'America/New_York',
            dateFormat: 'MM/dd/yyyy',
            timeFormat: '12h',
            autoSave: true,
            sessionTimeout: 30
        },

        // Display Settings
        display: {
            glucoseUnits: 'mg/dL',
            defaultView: 'dashboard',
            showGridLines: true,
            animateTransitions: true,
            compactMode: false,
            fontSize: 'medium',
            colorBlindFriendly: false
        },

        // AGP Settings
        agp: {
            targetRange: { min: 70, max: 180 },
            timeInRangeGoal: 70,
            dataRetentionDays: 90,
            smoothingAlgorithm: 'median',
            outlierDetection: true,
            showPercentiles: [10, 25, 50, 75, 90],
            defaultTimeframe: '14'
        },

        // Notification Settings
        notifications: {
            browserNotifications: false,
            emailNotifications: true,
            alertThresholds: {
                hypoglycemia: 70,
                hyperglycemia: 250,
                dataGaps: 6
            },
            alertFrequency: 'immediate',
            quietHours: {
                enabled: false,
                start: '22:00',
                end: '07:00'
            }
        },

        // Export Settings
        export: {
            defaultFormat: 'pdf',
            includeCharts: true,
            includeRecommendations: true,
            watermark: true,
            anonymizeData: false,
            compression: 'medium'
        },

        // Security Settings
        security: {
            requirePassword: true,
            sessionTimeout: 30,
            twoFactorEnabled: false,
            auditLogging: true,
            autoLogout: true
        },

        // Clinical Settings
        clinical: {
            showAlerts: true,
            alertSeverity: 'medium',
            drugInteractionChecking: true,
            clinicalGuidelines: 'ada2023',
            riskAssessment: true,
            careProtocols: true
        }
    });

    const [activeTab, setActiveTab] = useState('general');
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
    const [importExportMode, setImportExportMode] = useState(null);

    useEffect(() => {
        loadSettings();
    }, []);

    const loadSettings = () => {
        try {
            const savedSettings = localStorage.getItem('healthhub-settings');
            if (savedSettings) {
                const parsed = JSON.parse(savedSettings);
                setSettings(prevSettings => ({
                    ...prevSettings,
                    ...parsed
                }));
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
        }
    };

    const saveSettings = () => {
        try {
            localStorage.setItem('healthhub-settings', JSON.stringify(settings));
            setHasUnsavedChanges(false);

            if (onSettingsChange) {
                onSettingsChange(settings);
            }

            // Show success message
            const event = new CustomEvent('showNotification', {
                detail: { type: 'success', message: 'Settings saved successfully!' }
            });
            window.dispatchEvent(event);

        } catch (error) {
            console.error('Failed to save settings:', error);
            const event = new CustomEvent('showNotification', {
                detail: { type: 'error', message: 'Failed to save settings.' }
            });
            window.dispatchEvent(event);
        }
    };

    const resetToDefaults = () => {
        if (window.confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
            const defaultSettings = {
                general: {
                    theme: 'gruvbox-dark',
                    language: 'en',
                    timezone: 'America/New_York',
                    dateFormat: 'MM/dd/yyyy',
                    timeFormat: '12h',
                    autoSave: true,
                    sessionTimeout: 30
                },
                display: {
                    glucoseUnits: 'mg/dL',
                    defaultView: 'dashboard',
                    showGridLines: true,
                    animateTransitions: true,
                    compactMode: false,
                    fontSize: 'medium',
                    colorBlindFriendly: false
                },
                agp: {
                    targetRange: { min: 70, max: 180 },
                    timeInRangeGoal: 70,
                    dataRetentionDays: 90,
                    smoothingAlgorithm: 'median',
                    outlierDetection: true,
                    showPercentiles: [10, 25, 50, 75, 90],
                    defaultTimeframe: '14'
                },
                notifications: {
                    browserNotifications: false,
                    emailNotifications: true,
                    alertThresholds: {
                        hypoglycemia: 70,
                        hyperglycemia: 250,
                        dataGaps: 6
                    },
                    alertFrequency: 'immediate',
                    quietHours: {
                        enabled: false,
                        start: '22:00',
                        end: '07:00'
                    }
                },
                export: {
                    defaultFormat: 'pdf',
                    includeCharts: true,
                    includeRecommendations: true,
                    watermark: true,
                    anonymizeData: false,
                    compression: 'medium'
                },
                security: {
                    requirePassword: true,
                    sessionTimeout: 30,
                    twoFactorEnabled: false,
                    auditLogging: true,
                    autoLogout: true
                },
                clinical: {
                    showAlerts: true,
                    alertSeverity: 'medium',
                    drugInteractionChecking: true,
                    clinicalGuidelines: 'ada2023',
                    riskAssessment: true,
                    careProtocols: true
                }
            };

            setSettings(defaultSettings);
            setHasUnsavedChanges(true);
        }
    };

    const updateSetting = (category, key, value) => {
        setSettings(prev => ({
            ...prev,
            [category]: {
                ...prev[category],
                [key]: value
            }
        }));
        setHasUnsavedChanges(true);
    };

    const updateNestedSetting = (category, parentKey, childKey, value) => {
        setSettings(prev => ({
            ...prev,
            [category]: {
                ...prev[category],
                [parentKey]: {
                    ...prev[category][parentKey],
                    [childKey]: value
                }
            }
        }));
        setHasUnsavedChanges(true);
    };

    const exportSettings = () => {
        const dataStr = JSON.stringify(settings, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
        const exportFileDefaultName = 'healthhub-settings.json';

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
    };

    const importSettings = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedSettings = JSON.parse(e.target.result);
                setSettings(prevSettings => ({
                    ...prevSettings,
                    ...importedSettings
                }));
                setHasUnsavedChanges(true);

                const event = new CustomEvent('showNotification', {
                    detail: { type: 'success', message: 'Settings imported successfully!' }
                });
                window.dispatchEvent(event);

            } catch (error) {
                console.error('Failed to import settings:', error);
                const event = new CustomEvent('showNotification', {
                    detail: { type: 'error', message: 'Failed to import settings. Invalid file format.' }
                });
                window.dispatchEvent(event);
            }
        };
        reader.readAsText(file);

        // Reset file input
        event.target.value = '';
    };

    const renderGeneralSettings = () => (
        <div className="settings-section">
            <h3>🔧 General Settings</h3>

            <div className="settings-grid">
                <div className="setting-item">
                    <label>Theme</label>
                    <select
                        value={settings.general.theme}
                        onChange={(e) => updateSetting('general', 'theme', e.target.value)}
                    >
                        <option value="gruvbox-dark">Gruvbox Dark</option>
                        <option value="gruvbox-light">Gruvbox Light</option>
                        <option value="high-contrast">High Contrast</option>
                    </select>
                    <p className="setting-description">Choose your preferred color theme</p>
                </div>

                <div className="setting-item">
                    <label>Language</label>
                    <select
                        value={settings.general.language}
                        onChange={(e) => updateSetting('general', 'language', e.target.value)}
                    >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                    </select>
                    <p className="setting-description">Display language for the interface</p>
                </div>

                <div className="setting-item">
                    <label>Timezone</label>
                    <select
                        value={settings.general.timezone}
                        onChange={(e) => updateSetting('general', 'timezone', e.target.value)}
                    >
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="UTC">UTC</option>
                    </select>
                    <p className="setting-description">Your local timezone for data display</p>
                </div>

                <div className="setting-item">
                    <label>Date Format</label>
                    <select
                        value={settings.general.dateFormat}
                        onChange={(e) => updateSetting('general', 'dateFormat', e.target.value)}
                    >
                        <option value="MM/dd/yyyy">MM/DD/YYYY</option>
                        <option value="dd/MM/yyyy">DD/MM/YYYY</option>
                        <option value="yyyy-MM-dd">YYYY-MM-DD</option>
                    </select>
                    <p className="setting-description">How dates are displayed</p>
                </div>

                <div className="setting-item">
                    <label>Time Format</label>
                    <select
                        value={settings.general.timeFormat}
                        onChange={(e) => updateSetting('general', 'timeFormat', e.target.value)}
                    >
                        <option value="12h">12 Hour (AM/PM)</option>
                        <option value="24h">24 Hour</option>
                    </select>
                    <p className="setting-description">Time display format</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.general.autoSave}
                            onChange={(e) => updateSetting('general', 'autoSave', e.target.checked)}
                        />
                        Auto-save changes
                    </label>
                    <p className="setting-description">Automatically save changes without confirmation</p>
                </div>
            </div>
        </div>
    );

    const renderDisplaySettings = () => (
        <div className="settings-section">
            <h3>🎨 Display Settings</h3>

            <div className="settings-grid">
                <div className="setting-item">
                    <label>Glucose Units</label>
                    <select
                        value={settings.display.glucoseUnits}
                        onChange={(e) => updateSetting('display', 'glucoseUnits', e.target.value)}
                    >
                        <option value="mg/dL">mg/dL</option>
                        <option value="mmol/L">mmol/L</option>
                    </select>
                    <p className="setting-description">Units for glucose measurements</p>
                </div>

                <div className="setting-item">
                    <label>Default View</label>
                    <select
                        value={settings.display.defaultView}
                        onChange={(e) => updateSetting('display', 'defaultView', e.target.value)}
                    >
                        <option value="dashboard">Dashboard</option>
                        <option value="agp">AGP Chart</option>
                        <option value="workbench">Neo4j Workbench</option>
                    </select>
                    <p className="setting-description">Page to show when app loads</p>
                </div>

                <div className="setting-item">
                    <label>Font Size</label>
                    <select
                        value={settings.display.fontSize}
                        onChange={(e) => updateSetting('display', 'fontSize', e.target.value)}
                    >
                        <option value="small">Small</option>
                        <option value="medium">Medium</option>
                        <option value="large">Large</option>
                        <option value="extra-large">Extra Large</option>
                    </select>
                    <p className="setting-description">Base font size for the interface</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.display.showGridLines}
                            onChange={(e) => updateSetting('display', 'showGridLines', e.target.checked)}
                        />
                        Show grid lines on charts
                    </label>
                    <p className="setting-description">Display grid lines for easier reading</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.display.animateTransitions}
                            onChange={(e) => updateSetting('display', 'animateTransitions', e.target.checked)}
                        />
                        Animate transitions
                    </label>
                    <p className="setting-description">Smooth animations between views</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.display.compactMode}
                            onChange={(e) => updateSetting('display', 'compactMode', e.target.checked)}
                        />
                        Compact mode
                    </label>
                    <p className="setting-description">Reduce spacing for more data on screen</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.display.colorBlindFriendly}
                            onChange={(e) => updateSetting('display', 'colorBlindFriendly', e.target.checked)}
                        />
                        Color-blind friendly mode
                    </label>
                    <p className="setting-description">Use patterns and alternative colors</p>
                </div>
            </div>
        </div>
    );

    const renderAGPSettings = () => (
        <div className="settings-section">
            <h3>📊 AGP Settings</h3>

            <div className="settings-grid">
                <div className="setting-item">
                    <label>Target Range (mg/dL)</label>
                    <div className="range-inputs">
                        <input
                            type="number"
                            placeholder="Min"
                            value={settings.agp.targetRange.min}
                            onChange={(e) => updateNestedSetting('agp', 'targetRange', 'min', parseInt(e.target.value))}
                        />
                        <span>to</span>
                        <input
                            type="number"
                            placeholder="Max"
                            value={settings.agp.targetRange.max}
                            onChange={(e) => updateNestedSetting('agp', 'targetRange', 'max', parseInt(e.target.value))}
                        />
                    </div>
                    <p className="setting-description">Target glucose range for TIR calculations</p>
                </div>

                <div className="setting-item">
                    <label>Time in Range Goal (%)</label>
                    <input
                        type="number"
                        min="0"
                        max="100"
                        value={settings.agp.timeInRangeGoal}
                        onChange={(e) => updateSetting('agp', 'timeInRangeGoal', parseInt(e.target.value))}
                    />
                    <p className="setting-description">Target percentage for time in range</p>
                </div>

                <div className="setting-item">
                    <label>Data Retention (days)</label>
                    <select
                        value={settings.agp.dataRetentionDays}
                        onChange={(e) => updateSetting('agp', 'dataRetentionDays', parseInt(e.target.value))}
                    >
                        <option value="30">30 days</option>
                        <option value="90">90 days</option>
                        <option value="180">180 days</option>
                        <option value="365">1 year</option>
                        <option value="-1">Unlimited</option>
                    </select>
                    <p className="setting-description">How long to keep glucose data</p>
                </div>

                <div className="setting-item">
                    <label>Default Timeframe</label>
                    <select
                        value={settings.agp.defaultTimeframe}
                        onChange={(e) => updateSetting('agp', 'defaultTimeframe', e.target.value)}
                    >
                        <option value="7">7 days</option>
                        <option value="14">14 days</option>
                        <option value="30">30 days</option>
                        <option value="90">90 days</option>
                    </select>
                    <p className="setting-description">Default time period for AGP analysis</p>
                </div>

                <div className="setting-item">
                    <label>Smoothing Algorithm</label>
                    <select
                        value={settings.agp.smoothingAlgorithm}
                        onChange={(e) => updateSetting('agp', 'smoothingAlgorithm', e.target.value)}
                    >
                        <option value="none">None</option>
                        <option value="median">Median Filter</option>
                        <option value="moving-average">Moving Average</option>
                        <option value="gaussian">Gaussian</option>
                    </select>
                    <p className="setting-description">Algorithm for smoothing glucose curves</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.agp.outlierDetection}
                            onChange={(e) => updateSetting('agp', 'outlierDetection', e.target.checked)}
                        />
                        Enable outlier detection
                    </label>
                    <p className="setting-description">Automatically detect and mark unusual values</p>
                </div>
            </div>
        </div>
    );

    const renderNotificationSettings = () => (
        <div className="settings-section">
            <h3>🔔 Notification Settings</h3>

            <div className="settings-grid">
                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.notifications.browserNotifications}
                            onChange={(e) => updateSetting('notifications', 'browserNotifications', e.target.checked)}
                        />
                        Browser notifications
                    </label>
                    <p className="setting-description">Show notifications in your browser</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.notifications.emailNotifications}
                            onChange={(e) => updateSetting('notifications', 'emailNotifications', e.target.checked)}
                        />
                        Email notifications
                    </label>
                    <p className="setting-description">Send alerts via email</p>
                </div>

                <div className="setting-item">
                    <label>Alert Frequency</label>
                    <select
                        value={settings.notifications.alertFrequency}
                        onChange={(e) => updateSetting('notifications', 'alertFrequency', e.target.value)}
                    >
                        <option value="immediate">Immediate</option>
                        <option value="hourly">Hourly Summary</option>
                        <option value="daily">Daily Summary</option>
                        <option value="weekly">Weekly Summary</option>
                    </select>
                    <p className="setting-description">How often to receive notifications</p>
                </div>

                <div className="setting-item">
                    <label>Hypoglycemia Alert (mg/dL)</label>
                    <input
                        type="number"
                        value={settings.notifications.alertThresholds.hypoglycemia}
                        onChange={(e) => updateNestedSetting('notifications', 'alertThresholds', 'hypoglycemia', parseInt(e.target.value))}
                    />
                    <p className="setting-description">Alert when glucose falls below this value</p>
                </div>

                <div className="setting-item">
                    <label>Hyperglycemia Alert (mg/dL)</label>
                    <input
                        type="number"
                        value={settings.notifications.alertThresholds.hyperglycemia}
                        onChange={(e) => updateNestedSetting('notifications', 'alertThresholds', 'hyperglycemia', parseInt(e.target.value))}
                    />
                    <p className="setting-description">Alert when glucose exceeds this value</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.notifications.quietHours.enabled}
                            onChange={(e) => updateNestedSetting('notifications', 'quietHours', 'enabled', e.target.checked)}
                        />
                        Enable quiet hours
                    </label>
                    <p className="setting-description">Suppress non-critical alerts during specified hours</p>
                </div>

                {settings.notifications.quietHours.enabled && (
                    <div className="setting-item">
                        <label>Quiet Hours</label>
                        <div className="time-range-inputs">
                            <input
                                type="time"
                                value={settings.notifications.quietHours.start}
                                onChange={(e) => updateNestedSetting('notifications', 'quietHours', 'start', e.target.value)}
                            />
                            <span>to</span>
                            <input
                                type="time"
                                value={settings.notifications.quietHours.end}
                                onChange={(e) => updateNestedSetting('notifications', 'quietHours', 'end', e.target.value)}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );

    const renderClinicalSettings = () => (
        <div className="settings-section">
            <h3>🏥 Clinical Settings</h3>

            <div className="settings-grid">
                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.clinical.showAlerts}
                            onChange={(e) => updateSetting('clinical', 'showAlerts', e.target.checked)}
                        />
                        Show clinical alerts
                    </label>
                    <p className="setting-description">Display clinical decision support alerts</p>
                </div>

                <div className="setting-item">
                    <label>Alert Severity Threshold</label>
                    <select
                        value={settings.clinical.alertSeverity}
                        onChange={(e) => updateSetting('clinical', 'alertSeverity', e.target.value)}
                    >
                        <option value="low">Low - Show all alerts</option>
                        <option value="medium">Medium - Important alerts only</option>
                        <option value="high">High - Critical alerts only</option>
                    </select>
                    <p className="setting-description">Minimum severity level for alerts</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.clinical.drugInteractionChecking}
                            onChange={(e) => updateSetting('clinical', 'drugInteractionChecking', e.target.checked)}
                        />
                        Drug interaction checking
                    </label>
                    <p className="setting-description">Check for potential drug interactions</p>
                </div>

                <div className="setting-item">
                    <label>Clinical Guidelines</label>
                    <select
                        value={settings.clinical.clinicalGuidelines}
                        onChange={(e) => updateSetting('clinical', 'clinicalGuidelines', e.target.value)}
                    >
                        <option value="ada2023">ADA Standards of Care 2023</option>
                        <option value="easd2019">EASD/ADA Consensus 2019</option>
                        <option value="international">International Consensus</option>
                    </select>
                    <p className="setting-description">Which clinical guidelines to follow</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.clinical.riskAssessment}
                            onChange={(e) => updateSetting('clinical', 'riskAssessment', e.target.checked)}
                        />
                        Enable risk assessment
                    </label>
                    <p className="setting-description">Show risk scores and assessments</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.clinical.careProtocols}
                            onChange={(e) => updateSetting('clinical', 'careProtocols', e.target.checked)}
                        />
                        Show care protocols
                    </label>
                    <p className="setting-description">Display recommended care protocols and screenings</p>
                </div>
            </div>
        </div>
    );

    const renderExportSettings = () => (
        <div className="settings-section">
            <h3>📤 Export Settings</h3>

            <div className="settings-grid">
                <div className="setting-item">
                    <label>Default Export Format</label>
                    <select
                        value={settings.export.defaultFormat}
                        onChange={(e) => updateSetting('export', 'defaultFormat', e.target.value)}
                    >
                        <option value="pdf">PDF</option>
                        <option value="csv">CSV</option>
                        <option value="json">JSON</option>
                        <option value="xlsx">Excel</option>
                    </select>
                    <p className="setting-description">Default format for data exports</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.export.includeCharts}
                            onChange={(e) => updateSetting('export', 'includeCharts', e.target.checked)}
                        />
                        Include charts in exports
                    </label>
                    <p className="setting-description">Embed visual charts in exported reports</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.export.includeRecommendations}
                            onChange={(e) => updateSetting('export', 'includeRecommendations', e.target.checked)}
                        />
                        Include AI recommendations
                    </label>
                    <p className="setting-description">Add AI-generated recommendations to exports</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.export.watermark}
                            onChange={(e) => updateSetting('export', 'watermark', e.target.checked)}
                        />
                        Add watermark to exports
                    </label>
                    <p className="setting-description">Include HealthHub watermark on exported documents</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.export.anonymizeData}
                            onChange={(e) => updateSetting('export', 'anonymizeData', e.target.checked)}
                        />
                        Anonymize exported data
                    </label>
                    <p className="setting-description">Remove patient identifiers from exports</p>
                </div>
            </div>
        </div>
    );

    const renderSecuritySettings = () => (
        <div className="settings-section">
            <h3>🔒 Security Settings</h3>

            <div className="settings-grid">
                <div className="setting-item">
                    <label>Session Timeout (minutes)</label>
                    <select
                        value={settings.security.sessionTimeout}
                        onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
                    >
                        <option value="15">15 minutes</option>
                        <option value="30">30 minutes</option>
                        <option value="60">1 hour</option>
                        <option value="120">2 hours</option>
                        <option value="240">4 hours</option>
                    </select>
                    <p className="setting-description">Auto-logout after inactivity</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.security.requirePassword}
                            onChange={(e) => updateSetting('security', 'requirePassword', e.target.checked)}
                        />
                        Require password for access
                    </label>
                    <p className="setting-description">Password protection for the application</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.security.twoFactorEnabled}
                            onChange={(e) => updateSetting('security', 'twoFactorEnabled', e.target.checked)}
                        />
                        Enable two-factor authentication
                    </label>
                    <p className="setting-description">Additional security layer for login</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.security.auditLogging}
                            onChange={(e) => updateSetting('security', 'auditLogging', e.target.checked)}
                        />
                        Enable audit logging
                    </label>
                    <p className="setting-description">Log user activities for security auditing</p>
                </div>

                <div className="setting-item checkbox-item">
                    <label>
                        <input
                            type="checkbox"
                            checked={settings.security.autoLogout}
                            onChange={(e) => updateSetting('security', 'autoLogout', e.target.checked)}
                        />
                        Auto-logout on browser close
                    </label>
                    <p className="setting-description">Automatically logout when browser is closed</p>
                </div>
            </div>
        </div>
    );

    return (
        <div className="settings-preferences">
            <div className="settings-header">
                <h2>⚙️ Settings & Preferences</h2>
                <p>Customize your HealthHub experience</p>

                <div className="settings-actions">
                    <button
                        className="settings-btn secondary"
                        onClick={exportSettings}
                    >
                        📤 Export Settings
                    </button>

                    <label className="settings-btn secondary import-btn">
                        📥 Import Settings
                        <input
                            type="file"
                            accept=".json"
                            onChange={importSettings}
                            style={{ display: 'none' }}
                        />
                    </label>

                    <button
                        className="settings-btn warning"
                        onClick={resetToDefaults}
                    >
                        🔄 Reset to Defaults
                    </button>
                </div>
            </div>

            <div className="settings-layout">
                <div className="settings-tabs">
                    <button
                        className={activeTab === 'general' ? 'active' : ''}
                        onClick={() => setActiveTab('general')}
                    >
                        🔧 General
                    </button>

                    <button
                        className={activeTab === 'display' ? 'active' : ''}
                        onClick={() => setActiveTab('display')}
                    >
                        🎨 Display
                    </button>

                    <button
                        className={activeTab === 'agp' ? 'active' : ''}
                        onClick={() => setActiveTab('agp')}
                    >
                        📊 AGP
                    </button>

                    <button
                        className={activeTab === 'notifications' ? 'active' : ''}
                        onClick={() => setActiveTab('notifications')}
                    >
                        🔔 Notifications
                    </button>

                    <button
                        className={activeTab === 'clinical' ? 'active' : ''}
                        onClick={() => setActiveTab('clinical')}
                    >
                        🏥 Clinical
                    </button>

                    <button
                        className={activeTab === 'export' ? 'active' : ''}
                        onClick={() => setActiveTab('export')}
                    >
                        📤 Export
                    </button>

                    <button
                        className={activeTab === 'security' ? 'active' : ''}
                        onClick={() => setActiveTab('security')}
                    >
                        🔒 Security
                    </button>
                </div>

                <div className="settings-content">
                    {activeTab === 'general' && renderGeneralSettings()}
                    {activeTab === 'display' && renderDisplaySettings()}
                    {activeTab === 'agp' && renderAGPSettings()}
                    {activeTab === 'notifications' && renderNotificationSettings()}
                    {activeTab === 'clinical' && renderClinicalSettings()}
                    {activeTab === 'export' && renderExportSettings()}
                    {activeTab === 'security' && renderSecuritySettings()}
                </div>
            </div>

            <div className="settings-footer">
                {hasUnsavedChanges && (
                    <div className="unsaved-changes">
                        ⚠️ You have unsaved changes
                    </div>
                )}

                <div className="footer-actions">
                    <button
                        className="settings-btn secondary"
                        onClick={loadSettings}
                    >
                        🔄 Reload Settings
                    </button>

                    <button
                        className={`settings-btn primary ${hasUnsavedChanges ? 'pulse' : ''}`}
                        onClick={saveSettings}
                    >
                        💾 Save Settings
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SettingsPreferences;
