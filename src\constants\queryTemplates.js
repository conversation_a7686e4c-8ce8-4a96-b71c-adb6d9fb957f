// Schema Explorer Query Templates
// These templates provide common query patterns for exploring the healthcare database

export const SCHEMA_QUERY_TEMPLATES = {
    patientRecentGlucose: {
        title: "Patient with Recent Glucose Readings",
        description: "Get glucose readings for a specific patient within a date range",
        query: `MATCH (p:Patient {patientId: $patientId})-[:HAD_READING]->(g:GlucoseReading)
WHERE g.timestamp >= datetime() - duration({days: 7})
RETURN p.name as patient, g.glucose as glucose, g.timestamp as timestamp
ORDER BY g.timestamp DESC
LIMIT 20`,
        parameters: ['patientId']
    },

    patientsByCondition: {
        title: "Patients by Condition",
        description: "Analyze patient distribution across medical conditions",
        query: `MATCH (p:Patient)-[:HAS_CONDITION]->(c:Condition)
RETURN c.name as condition, count(p) as patientCount, collect(p.name)[..5] as samplePatients
ORDER BY patientCount DESC`,
        parameters: []
    },

    medicationUsage: {
        title: "Medication Usage Analysis",
        description: "Analyze medication prescriptions and usage patterns",
        query: `MATCH (p:Patient)-[:TAKES_MEDICATION]->(m:Medication)
RETURN m.name as medication, m.dosage as dosage, count(p) as prescribedTo
ORDER BY prescribedTo DESC`,
        parameters: []
    },

    appointmentHistory: {
        title: "Patient Appointment History",
        description: "Get appointment history with outcomes for a patient",
        query: `MATCH (p:Patient {patientId: $patientId})-[:HAD_APPOINTMENT]->(a:Appointment)
RETURN p.name as patient, a.date as appointmentDate, a.type as appointmentType, 
       a.outcome as outcome, a.notes as notes
ORDER BY a.date DESC
LIMIT 20`,
        parameters: ['patientId']
    },

    glucoseAveragesByPatient: {
        title: "Average Glucose by Patient",
        description: "Calculate average glucose levels for all patients",
        query: `MATCH (p:Patient)-[:HAD_READING]->(g:GlucoseReading)
WHERE g.timestamp >= datetime() - duration({days: $days})
RETURN p.name as patient, p.condition as condition,
       round(avg(g.glucose), 1) as avgGlucose,
       count(g) as readingCount,
       min(g.glucose) as minGlucose,
       max(g.glucose) as maxGlucose
ORDER BY avgGlucose DESC`,
        parameters: ['days']
    },

    medicationEffectiveness: {
        title: "Medication Effectiveness Analysis",
        description: "Compare glucose levels before and after medication changes",
        query: `MATCH (p:Patient)-[:TAKES_MEDICATION]->(m:Medication)
OPTIONAL MATCH (p)-[:HAD_READING]->(g1:GlucoseReading)
WHERE g1.timestamp < datetime(m.startDate)
OPTIONAL MATCH (p)-[:HAD_READING]->(g2:GlucoseReading)
WHERE g2.timestamp > datetime(m.startDate) + duration({days: 7})
RETURN p.name as patient, m.name as medication, 
       round(avg(g1.glucose), 1) as avgBeforeMedication,
       round(avg(g2.glucose), 1) as avgAfterMedication,
       round(avg(g1.glucose) - avg(g2.glucose), 1) as improvement
ORDER BY improvement DESC NULLS LAST`,
        parameters: []
    },

    recentAppointments: {
        title: "Recent Appointments with Glucose Trends",
        description: "Recent appointments with average glucose before/after",
        query: `MATCH (p:Patient)-[:HAD_APPOINTMENT]->(a:Appointment)
WHERE a.date >= datetime() - duration({days: $days})
OPTIONAL MATCH (p)-[:HAD_READING]->(g:GlucoseReading)
WHERE g.timestamp >= datetime(a.date) - duration({days: 7}) 
  AND g.timestamp <= datetime(a.date) + duration({days: 7})
RETURN p.name as patient, a.date as appointmentDate, a.type as appointmentType,
       a.outcome as outcome, round(avg(g.glucose), 1) as avgGlucoseAroundVisit
ORDER BY a.date DESC`,
        parameters: ['days']
    },

    conditionsWithMedications: {
        title: "Conditions with Standard Medications",
        description: "Show which medications are commonly prescribed for each condition",
        query: `MATCH (p:Patient)-[:HAS_CONDITION]->(c:Condition)
MATCH (p)-[:TAKES_MEDICATION]->(m:Medication)
RETURN c.name as condition, m.name as medication, 
       count(p) as patientsCount,
       collect(DISTINCT p.name)[..3] as examplePatients
ORDER BY condition, patientsCount DESC`,
        parameters: []
    },

    hypoglycemicEvents: {
        title: "Hypoglycemic Events Analysis",
        description: "Identify and analyze low glucose events",
        query: `MATCH (p:Patient)-[:HAD_READING]->(g:GlucoseReading)
WHERE g.glucose < 70 
  AND g.timestamp >= datetime() - duration({days: $days})
RETURN p.name as patient, p.condition as condition,
       g.glucose as glucoseValue, g.timestamp as timestamp,
       CASE 
         WHEN g.glucose < 54 THEN 'Severe'
         WHEN g.glucose < 63 THEN 'Moderate'  
         ELSE 'Mild'
       END as severity
ORDER BY g.timestamp DESC, g.glucose ASC
LIMIT 50`,
        parameters: ['days']
    },

    glucoseVariability: {
        title: "Glucose Variability by Patient",
        description: "Calculate glucose variability metrics for patients",
        query: `MATCH (p:Patient)-[:HAD_READING]->(g:GlucoseReading)
WHERE g.timestamp >= datetime() - duration({days: $days})
WITH p, collect(g.glucose) as glucoseReadings
WHERE size(glucoseReadings) >= 10
RETURN p.name as patient, p.condition as condition,
       size(glucoseReadings) as readingCount,
       round(avg(glucoseReadings), 1) as avgGlucose,
       round(stdev(glucoseReadings), 1) as standardDeviation,
       round((stdev(glucoseReadings) / avg(glucoseReadings)) * 100, 1) as coefficientOfVariation,
       min(glucoseReadings) as minGlucose,
       max(glucoseReadings) as maxGlucose
ORDER BY coefficientOfVariation DESC`,
        parameters: ['days']
    }
};

// Simple node query templates for schema exploration
export const SCHEMA_NODE_QUERIES = {
    patient: 'MATCH (p:Patient) RETURN p LIMIT 10',
    glucoseReading: 'MATCH (g:GlucoseReading) RETURN g LIMIT 10',
    medication: 'MATCH (m:Medication) RETURN m LIMIT 10',
    condition: 'MATCH (c:Condition) RETURN c LIMIT 10',
    appointment: 'MATCH (a:Appointment) RETURN a LIMIT 10'
};

// Relationship query templates
export const SCHEMA_RELATIONSHIP_QUERIES = {
    patientGlucose: 'MATCH (p:Patient)-[r:HAD_READING]->(g:GlucoseReading) RETURN p, r, g LIMIT 10',
    patientMedication: 'MATCH (p:Patient)-[r:TAKES_MEDICATION]->(m:Medication) RETURN p, r, m LIMIT 10',
    patientCondition: 'MATCH (p:Patient)-[r:HAS_CONDITION]->(c:Condition) RETURN p, r, c LIMIT 10',
    patientAppointment: 'MATCH (p:Patient)-[r:HAD_APPOINTMENT]->(a:Appointment) RETURN p, r, a LIMIT 10'
};