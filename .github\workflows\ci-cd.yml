name: CI/CD - Build and Deploy to Cloudflare Pages

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

concurrency:
  group: pages-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Load .env for build (if present)
        shell: bash
        run: |
          if [[ -f .env ]]; then
            echo "Using .env for build"
            # Optionally mirror to .env.production so Vite picks prod values
            cp .env .env.production
          fi

      - name: Build
        run: npm run build
        env:
          # Optional build-time vars from GitHub Secrets (do not print values)
          VITE_OPENAI_API_KEY: ${{ secrets.VITE_OPENAI_API_KEY }}
          VITE_NEO4J_URI: ${{ secrets.VITE_NEO4J_URI }}
          VITE_NEO4J_USERNAME: ${{ secrets.VITE_NEO4J_USERNAME }}
          VITE_NEO4J_PASSWORD: ${{ secrets.VITE_NEO4J_PASSWORD }}
          VITE_NEO4J_DATABASE: ${{ secrets.VITE_NEO4J_DATABASE }}
          VITE_NEO4J_USE_API: ${{ secrets.VITE_NEO4J_USE_API }}

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist

  deploy:
    if: github.event_name == 'push'
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout (for wrangler.toml)
        uses: actions/checkout@v4

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Deploy to Cloudflare Pages via Wrangler
        run: |
          npm install -g wrangler
          npm install neo4j-driver
          wrangler pages deploy dist --project-name=healthhub-research-platform
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
