/* Patient Dashboard Styles */
.patient-dashboard {
  min-height: 100vh;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--gb-bg1);
}

.dashboard-header h1 {
  color: var(--gb-accent2);
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
}

.dashboard-controls {
  display: flex;
  gap: 0.75rem;
}

.refresh-btn {
  background: var(--gb-bg1);
  color: var(--gb-fg);
  border: 1px solid var(--gb-accent);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: var(--gb-accent);
  color: var(--gb-bg0);
}

/* Error Banner */
.error-banner {
  background: #fb4934;
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-error {
  background: none;
  border: none;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
}

/* Dashboard Layout */
.dashboard-layout {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 1.5rem;
  min-height: calc(100vh - 200px);
}

@media (max-width: 1200px) {
  .dashboard-layout {
    grid-template-columns: 300px 1fr;
  }
}

@media (max-width: 968px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Sidebar - Patient List */
.sidebar {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1rem;
  height: fit-content;
  max-height: 80vh;
  overflow-y: auto;
}

.patient-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.patient-list-header h3 {
  color: var(--gb-accent2);
  margin: 0;
  font-size: 1.25rem;
}

.comparison-toggle {
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 1px solid var(--gb-accent);
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.comparison-toggle:hover,
.comparison-toggle.active {
  background: var(--gb-accent);
  color: var(--gb-bg0);
}

.comparison-count {
  color: var(--gb-accent2);
  font-size: 0.75rem;
  margin-left: 0.5rem;
}

.patient-search {
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 1px solid var(--gb-bg1);
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(215, 153, 33, 0.2);
}

/* Patient Cards */
.patients-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.patient-card {
  background: var(--gb-bg0);
  border: 1px solid transparent;
  border-radius: 6px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.patient-card:hover {
  border-color: var(--gb-accent);
  background: rgba(215, 153, 33, 0.05);
}

.patient-card.selected {
  border-color: var(--gb-accent2);
  background: rgba(250, 189, 47, 0.1);
}

.patient-card.comparison-selected {
  border-color: #83a598;
  background: rgba(131, 165, 152, 0.1);
}

.patient-info h4 {
  margin: 0 0 0.25rem 0;
  color: var(--gb-fg);
  font-size: 1rem;
  font-weight: 600;
}

.condition {
  color: var(--gb-accent);
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.patient-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.patient-meta span {
  color: #a89984;
  font-size: 0.8rem;
}

.patient-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.readings-count,
.last-reading,
.date-range {
  font-size: 0.75rem;
  color: #928374;
}

.comparison-checkbox {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
}

.comparison-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #83a598;
}

/* Main Content */
.main-content {
  background: var(--gb-bg1);
  border-radius: 8px;
  overflow: hidden;
}

.no-patient-selected {
  padding: 3rem;
  text-align: center;
  color: #928374;
}

.no-patient-selected h3 {
  color: var(--gb-fg);
  margin-bottom: 0.5rem;
}

/* View Tabs */
.view-tabs {
  display: flex;
  background: var(--gb-bg0);
  border-bottom: 1px solid var(--gb-bg1);
}

.view-tabs button {
  background: none;
  border: none;
  color: var(--gb-fg);
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.view-tabs button:hover {
  background: rgba(215, 153, 33, 0.1);
  color: var(--gb-accent2);
}

.view-tabs button.active {
  color: var(--gb-accent2);
  border-bottom-color: var(--gb-accent2);
  background: rgba(250, 189, 47, 0.1);
}

/* View Content */
.view-content {
  padding: 1.5rem;
  position: relative;
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: var(--gb-fg);
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(40, 40, 40, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Patient Overview */
.patient-overview {
  max-width: 100%;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.patient-details h2 {
  color: var(--gb-accent2);
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 600;
}

.patient-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.patient-badges span {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.condition-badge {
  background: #fb4934 !important;
}

.age-badge {
  background: #83a598 !important;
}

.gender-badge {
  background: #d3869b !important;
}

.overview-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.date-range-select {
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 1px solid var(--gb-accent);
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.generate-report-btn {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.generate-report-btn:hover {
  background: var(--gb-accent2);
}

/* Overview Stats */
.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-bg1);
  border-radius: 6px;
  padding: 1.25rem;
  text-align: center;
}

.stat-card.highlight {
  border-color: var(--gb-accent);
  background: rgba(215, 153, 33, 0.05);
}

.stat-card h4 {
  color: #a89984;
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  color: var(--gb-accent2);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

/* Contact and Medical Info */
.contact-info,
.conditions-section,
.medications-section {
  background: var(--gb-bg0);
  border-radius: 6px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
}

.contact-info h4,
.conditions-section h4,
.medications-section h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.contact-grid>div {
  font-size: 0.875rem;
  color: var(--gb-fg);
}

.conditions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.condition-tag {
  background: rgba(251, 73, 52, 0.2);
  color: #fb4934;
  border: 1px solid #fb4934;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
}

.medications-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.medication-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gb-bg1);
}

.medication-item:last-child {
  border-bottom: none;
}

.medication-name {
  color: var(--gb-fg);
  font-weight: 500;
}

.medication-dosage {
  color: var(--gb-accent);
  font-size: 0.875rem;
}

/* Analytics View */
.analytics-view {
  width: 100%;
}

.analytics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 1200px) {
  .analytics-grid {
    grid-template-columns: 2fr 1fr;
  }
}

.chart-section,
.statistics-section,
.ai-recommendations-section {
  background: var(--gb-bg0);
  border-radius: 6px;
  padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .overview-controls {
    width: 100%;
    justify-content: space-between;
  }

  .overview-stats {
    grid-template-columns: 1fr 1fr;
  }

  .contact-grid {
    grid-template-columns: 1fr;
  }
}

/* New Tab Views Styles */

/* No Data Message */
.no-data-message {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin: 1rem 0;
}

.no-data-message h3 {
  color: var(--gb-accent);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.no-data-message p {
  color: var(--gb-fg1);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Analytics Loading */
.analytics-loading {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-accent);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin: 1rem 0;
}

.analytics-loading h3 {
  color: var(--gb-accent);
  margin-bottom: 1rem;
}

/* Trends View */
.trends-view {
  padding: 1rem;
}

.trends-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.trends-header h3 {
  color: var(--gb-accent2);
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.trends-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.trend-summary,
.trend-insights {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 8px;
  padding: 1.5rem;
}

.trend-summary h4,
.trend-insights h4 {
  color: var(--gb-accent);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.trend-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.trend-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gb-bg2);
}

.trend-stat:last-child {
  border-bottom: none;
}

.trend-label {
  color: var(--gb-fg1);
  font-weight: 500;
}

.trend-value {
  color: var(--gb-fg);
  font-weight: 600;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.insight-item {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-bg2);
  border-radius: 6px;
  padding: 0.75rem;
  color: var(--gb-fg1);
  line-height: 1.4;
}

.trends-note {
  background: var(--gb-bg1);
  border-left: 4px solid var(--gb-accent);
  padding: 1rem;
  border-radius: 0 6px 6px 0;
  font-size: 0.9rem;
  color: var(--gb-fg1);
}

/* Reports View */
.reports-view {
  padding: 1rem;
}

.reports-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.reports-header h3 {
  color: var(--gb-accent2);
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.report-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.report-option {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 8px;
  padding: 1.5rem;
}

.report-option h4 {
  color: var(--gb-accent);
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
}

.report-option p {
  color: var(--gb-fg1);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.generate-btn {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.generate-btn:hover:not(.disabled) {
  background: var(--gb-accent2);
  transform: translateY(-1px);
}

.generate-btn.disabled {
  background: var(--gb-bg2);
  color: var(--gb-fg1);
  cursor: not-allowed;
  opacity: 0.6;
}

.report-info {
  background: var(--gb-bg1);
  border: 1px solid var(--gb-bg2);
  border-radius: 8px;
  padding: 1.5rem;
}

.report-info h4 {
  color: var(--gb-accent);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--gb-bg2);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--gb-fg1);
  font-weight: 500;
}

.info-value {
  color: var(--gb-fg);
  font-weight: 600;
}

/* Responsive adjustments for new views */
@media (max-width: 768px) {
  .trends-grid {
    grid-template-columns: 1fr;
  }

  .report-options {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
