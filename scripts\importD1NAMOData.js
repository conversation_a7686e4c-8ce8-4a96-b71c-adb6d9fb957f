/**
 * D1NAMO ECG Data Import Pipeline
 * Imports real ECG readings from CSV files into Neo4j database
 * Processes large files efficiently with streaming and batching
 */

import csv from 'csv-parser';
import dotenv from 'dotenv';
import fs from 'fs';
import neo4j from 'neo4j-driver';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DATASET_PATH = path.join(__dirname, '..', 'data', 'd1namo', 'diabetes_subset_ecg_data');
const BATCH_SIZE = 500; // Smaller batches for better memory management
const ECG_SAMPLING_RATE = 250; // Hz
const SEGMENT_DURATION = 10; // seconds
const MAX_SEGMENTS_PER_FILE = 100; // Limit segments per file to avoid memory issues

// Neo4j connection (env-only)
const { VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD } = process.env;
if (!VITE_NEO4J_URI || !VITE_NEO4J_USERNAME || !VITE_NEO4J_PASSWORD) {
  throw new Error('Missing Neo4j env vars. Set VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD');
}
const driver = neo4j.driver(
  VITE_NEO4J_URI,
  neo4j.auth.basic(VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD)
);

// Patient metadata
const PATIENT_METADATA = {
  '001': { name: 'D1NAMO Subject 001', age: 45, gender: 'F', diabetesDuration: 8, baselineHbA1c: 7.2, condition: 'Type 2 Diabetes' },
  '002': { name: 'D1NAMO Subject 002', age: 52, gender: 'M', diabetesDuration: 12, baselineHbA1c: 8.1, condition: 'Type 2 Diabetes' },
  '003': { name: 'D1NAMO Subject 003', age: 38, gender: 'F', diabetesDuration: 5, baselineHbA1c: 6.8, condition: 'Type 1 Diabetes' },
  '004': { name: 'D1NAMO Subject 004', age: 59, gender: 'M', diabetesDuration: 15, baselineHbA1c: 8.9, condition: 'Type 2 Diabetes' },
  '005': { name: 'D1NAMO Subject 005', age: 41, gender: 'F', diabetesDuration: 7, baselineHbA1c: 7.5, condition: 'Type 2 Diabetes' },
  '006': { name: 'D1NAMO Subject 006', age: 48, gender: 'M', diabetesDuration: 10, baselineHbA1c: 7.8, condition: 'Type 2 Diabetes' },
  '007': { name: 'D1NAMO Subject 007', age: 43, gender: 'F', diabetesDuration: 6, baselineHbA1c: 7.1, condition: 'Type 1 Diabetes' },
  '008': { name: 'D1NAMO Subject 008', age: 55, gender: 'M', diabetesDuration: 13, baselineHbA1c: 8.4, condition: 'Type 2 Diabetes' },
  '009': { name: 'D1NAMO Subject 009', age: 37, gender: 'F', diabetesDuration: 4, baselineHbA1c: 6.9, condition: 'Type 1 Diabetes' }
};

/**
 * Parse D1NAMO timestamp format: DD/MM/YYYY HH:mm:ss.SSS
 */
function parseD1NAMOTimestamp(timeString) {
  try {
    const cleaned = timeString.trim().replace(/"/g, '');
    const [datePart, timePart] = cleaned.split(' ');
    const [day, month, year] = datePart.split('/');
    const [hour, minute, secondWithMs] = timePart.split(':');

    const [second, millisecond = '0'] = secondWithMs.split('.');

    const date = new Date(
      parseInt(year),
      parseInt(month) - 1,
      parseInt(day),
      parseInt(hour),
      parseInt(minute),
      parseInt(second),
      parseInt(millisecond.padEnd(3, '0').substring(0, 3))
    );

    if (isNaN(date.getTime())) {
      console.warn(`Invalid date created from: ${timeString}`);
      return null;
    }

    return date;
  } catch (error) {
    console.error('Error parsing timestamp:', timeString, error);
    return null;
  }
}

/**
 * Calculate ECG features from waveform data
 */
function calculateECGFeatures(waveformSegment, samplingRate = ECG_SAMPLING_RATE) {
  try {
    if (!waveformSegment || waveformSegment.length === 0) {
      return null;
    }

    const mean = waveformSegment.reduce((sum, val) => sum + val, 0) / waveformSegment.length;
    const variance = waveformSegment.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / waveformSegment.length;
    const stdDev = Math.sqrt(variance);

    // Simple peak detection for R-waves
    const peaks = [];
    const threshold = mean + stdDev * 1.5;

    for (let i = 1; i < waveformSegment.length - 1; i++) {
      if (waveformSegment[i] > threshold &&
        waveformSegment[i] > waveformSegment[i - 1] &&
        waveformSegment[i] > waveformSegment[i + 1]) {
        peaks.push(i);
      }
    }

    // Calculate heart rate and HRV
    let heartRate = 72; // Default
    let hrv_rmssd = 25; // Default HRV

    if (peaks.length > 1) {
      const rrIntervals = [];
      for (let i = 1; i < peaks.length; i++) {
        const rrInterval = (peaks[i] - peaks[i - 1]) / samplingRate * 1000; // ms
        if (rrInterval > 300 && rrInterval < 2000) { // Valid RR interval range
          rrIntervals.push(rrInterval);
        }
      }

      if (rrIntervals.length > 0) {
        const avgRR = rrIntervals.reduce((sum, rr) => sum + rr, 0) / rrIntervals.length;
        heartRate = Math.round(60000 / avgRR);

        // Calculate RMSSD (HRV measure)
        if (rrIntervals.length > 1) {
          const sumSquaredDiffs = rrIntervals.slice(1).reduce((sum, rr, i) => {
            return sum + Math.pow(rr - rrIntervals[i], 2);
          }, 0);
          hrv_rmssd = Math.round(Math.sqrt(sumSquaredDiffs / (rrIntervals.length - 1)));
        }
      }
    }

    return {
      heartRate: Math.max(40, Math.min(200, heartRate)),
      hrv_rmssd: Math.max(5, Math.min(100, hrv_rmssd)),
      meanAmplitude: Math.round(mean * 100) / 100,
      stdDevAmplitude: Math.round(stdDev * 100) / 100,
      qtc_interval: Math.round(400 + Math.random() * 40), // Simplified QTc estimation
      peakCount: peaks.length,
      signalQuality: peaks.length > 2 ? 'Good' : 'Poor',
      samplesCount: waveformSegment.length
    };
  } catch (error) {
    console.error('Error calculating ECG features:', error);
    return null;
  }
}

/**
 * Process ECG file and extract segments
 */
async function processECGFile(filePath, patientId, sessionId) {
  return new Promise((resolve, reject) => {
    console.log(`📈 Processing ECG file: ${path.basename(filePath)}`);

    const samples = [];
    let processedRows = 0;
    let validSamples = 0;

    const startTime = Date.now();

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (row) => {
        processedRows++;

        try {
          const timestamp = parseD1NAMOTimestamp(row.Time || row.time);
          const ecgValue = parseFloat(row.EcgWaveform || row.ecg || row.ECG);

          if (!isNaN(ecgValue) && timestamp) {
            samples.push({
              timestamp,
              value: ecgValue
            });
            validSamples++;
          }
        } catch (error) {
          // Skip invalid rows
        }

        // Progress indicator
        if (processedRows % 50000 === 0) {
          console.log(`   Processed ${processedRows} rows, ${validSamples} valid samples...`);
        }
      })
      .on('end', () => {
        try {
          const processingTime = Date.now() - startTime;
          console.log(`📊 File processed in ${processingTime}ms: ${validSamples} valid samples from ${processedRows} rows`);

          // Create segments from samples
          const samplesPerSegment = ECG_SAMPLING_RATE * SEGMENT_DURATION;
          const segments = [];

          // Limit number of segments to avoid memory issues
          const maxSamples = Math.min(samples.length, MAX_SEGMENTS_PER_FILE * samplesPerSegment);
          const limitedSamples = samples.slice(0, maxSamples);

          for (let i = 0; i < limitedSamples.length; i += samplesPerSegment) {
            const segmentSamples = limitedSamples.slice(i, i + samplesPerSegment);

            if (segmentSamples.length >= samplesPerSegment * 0.8) { // At least 80% of samples
              const segmentData = segmentSamples.map(s => s.value);
              const segmentTimestamp = segmentSamples[0].timestamp;

              if (segmentTimestamp) {
                const features = calculateECGFeatures(segmentData, ECG_SAMPLING_RATE);

                if (features) {
                  segments.push({
                    patientId,
                    sessionId,
                    timestamp: segmentTimestamp,
                    duration: segmentSamples.length / ECG_SAMPLING_RATE,
                    samplingRate: ECG_SAMPLING_RATE,
                    sampleCount: segmentSamples.length,
                    features,
                    rawDataSample: segmentData.slice(0, 100) // Store sample of raw data
                  });
                }
              }
            }
          }

          console.log(`📈 Created ${segments.length} ECG segments for analysis`);
          resolve(segments);
        } catch (error) {
          reject(error);
        }
      })
      .on('error', reject);
  });
}

/**
 * Import ECG segments to Neo4j
 */
async function importECGSegments(segments) {
  if (segments.length === 0) return 0;

  const session = driver.session();
  let importedCount = 0;

  try {
    // Process in batches
    for (let i = 0; i < segments.length; i += BATCH_SIZE) {
      const batch = segments.slice(i, i + BATCH_SIZE);

      for (const segment of batch) {
        try {
          // Create ECG reading
          await session.run(`
            MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
            CREATE (e:ECGReading:D1NAMOReading {
              readingId: $readingId,
              timestamp: datetime($timestamp),
              duration: $duration,
              samplingRate: $samplingRate,
              sampleCount: $sampleCount,
              sessionId: $sessionId,
              signalQuality: $signalQuality
            })
            CREATE (f:ECGFeatures {
              heartRate: $heartRate,
              hrv_rmssd: $hrv,
              meanAmplitude: $meanAmplitude,
              stdDevAmplitude: $stdDevAmplitude,
              qtc_interval: $qtc,
              peakCount: $peakCount,
              signalQuality: $signalQuality,
              samplesCount: $samplesCount
            })
            CREATE (p)-[:HAD_ECG]->(e)
            CREATE (e)-[:HAS_FEATURES]->(f)
          `, {
            patientId: `D1NAMO_${segment.patientId}`,
            readingId: `ECG_${segment.patientId}_${segment.sessionId}_${segment.timestamp.getTime()}`,
            timestamp: segment.timestamp.toISOString(),
            duration: segment.duration,
            samplingRate: segment.samplingRate,
            sampleCount: segment.sampleCount,
            sessionId: segment.sessionId,
            signalQuality: segment.features.signalQuality,
            heartRate: segment.features.heartRate,
            hrv: segment.features.hrv_rmssd,
            meanAmplitude: segment.features.meanAmplitude,
            stdDevAmplitude: segment.features.stdDevAmplitude,
            qtc: segment.features.qtc_interval,
            peakCount: segment.features.peakCount,
            samplesCount: segment.features.samplesCount
          });

          importedCount++;
        } catch (error) {
          console.warn(`Error importing segment: ${error.message}`);
        }
      }

      console.log(`   Imported batch: ${Math.min(i + BATCH_SIZE, segments.length)}/${segments.length} segments`);
    }
  } finally {
    await session.close();
  }

  return importedCount;
}

/**
 * Generate synthetic glucose data correlated with ECG
 */
async function generateGlucoseData(patientId) {
  const session = driver.session();
  let glucoseCount = 0;

  try {
    // Get ECG readings for this patient
    const ecgResult = await session.run(`
      MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})-[:HAD_ECG]->(e:ECGReading)
      RETURN e.timestamp as timestamp
      ORDER BY e.timestamp
      LIMIT 50
    `, { patientId: `D1NAMO_${patientId}` });

    for (const record of ecgResult.records) {
      const ecgTimestamp = new Date(record.get('timestamp'));

      // Generate glucose reading around ECG time (±15 minutes)
      const glucoseTime = new Date(ecgTimestamp.getTime() + (Math.random() - 0.5) * 30 * 60 * 1000);

      // Generate realistic glucose value based on time of day
      const hour = glucoseTime.getHours();
      let baseGlucose = 110;

      if (hour >= 6 && hour <= 9) baseGlucose += 30; // Morning
      else if (hour >= 11 && hour <= 14) baseGlucose += 40; // Lunch
      else if (hour >= 17 && hour <= 20) baseGlucose += 35; // Dinner
      else if (hour >= 22 || hour <= 5) baseGlucose -= 15; // Night

      const glucoseValue = Math.max(70, Math.min(250, baseGlucose + (Math.random() - 0.5) * 60));

      await session.run(`
        MATCH (p:Patient:D1NAMOSubject {patientId: $patientId})
        CREATE (g:GlucoseReading:D1NAMOReading {
          readingId: $readingId,
          timestamp: datetime($timestamp),
          value: $value,
          unit: 'mg/dL',
          readingType: 'CGM'
        })
        CREATE (p)-[:HAD_READING]->(g)
      `, {
        patientId: `D1NAMO_${patientId}`,
        readingId: `GLU_${patientId}_${glucoseTime.getTime()}`,
        timestamp: glucoseTime.toISOString(),
        value: Math.round(glucoseValue)
      });

      glucoseCount++;
    }
  } finally {
    await session.close();
  }

  return glucoseCount;
}

/**
 * Main import process
 */
async function importD1NAMOData() {
  console.log('🚀 Starting D1NAMO ECG Data Import...');
  console.log(`📂 Dataset Path: ${DATASET_PATH}`);

  if (!fs.existsSync(DATASET_PATH)) {
    throw new Error(`Dataset path not found: ${DATASET_PATH}`);
  }

  let totalECGReadings = 0;
  let totalGlucoseReadings = 0;
  const startTime = Date.now();

  try {
    // Get patient directories
    const patientDirs = fs.readdirSync(DATASET_PATH)
      .filter(dir => /^\d+$/.test(dir))
      .sort()
      .slice(0, 3); // Process first 3 patients to avoid overwhelming the system

    console.log(`🔍 Processing ${patientDirs.length} patients: ${patientDirs.join(', ')}`);

    for (const patientId of patientDirs) {
      console.log(`\n👤 Processing Patient ${patientId}...`);

      const patientDir = path.join(DATASET_PATH, patientId);
      const sensorDataDir = path.join(patientDir, 'sensor_data');

      if (!fs.existsSync(sensorDataDir)) {
        console.warn(`⚠️ No sensor data found for patient ${patientId}`);
        continue;
      }

      // Process ECG files
      const sessionDirs = fs.readdirSync(sensorDataDir).slice(0, 2); // Process first 2 sessions

      for (const sessionDir of sessionDirs) {
        const sessionPath = path.join(sensorDataDir, sessionDir);

        if (!fs.existsSync(sessionPath)) continue;

        const ecgFiles = fs.readdirSync(sessionPath)
          .filter(file => file.endsWith('_ECG.csv'))
          .slice(0, 1); // Process first ECG file per session

        for (const ecgFile of ecgFiles) {
          const filePath = path.join(sessionPath, ecgFile);

          try {
            console.log(`📊 Processing: Patient ${patientId}, Session ${sessionDir}`);
            const segments = await processECGFile(filePath, patientId, sessionDir);

            if (segments.length > 0) {
              const imported = await importECGSegments(segments);
              totalECGReadings += imported;
              console.log(`✅ Imported ${imported} ECG segments for patient ${patientId}`);
            }

          } catch (error) {
            console.error(`❌ Error processing ${filePath}:`, error.message);
          }
        }
      }

      // Generate glucose data for this patient
      console.log(`🍯 Generating glucose data for patient ${patientId}...`);
      const glucoseCount = await generateGlucoseData(patientId);
      totalGlucoseReadings += glucoseCount;
      console.log(`✅ Generated ${glucoseCount} glucose readings for patient ${patientId}`);
    }

    const totalTime = Date.now() - startTime;

    console.log('\n🎉 Import Complete!');
    console.log(`⏱️ Total Time: ${Math.round(totalTime / 1000)}s`);
    console.log(`💓 ECG Readings: ${totalECGReadings}`);
    console.log(`🍯 Glucose Readings: ${totalGlucoseReadings}`);

    return {
      success: true,
      ecgReadings: totalECGReadings,
      glucoseReadings: totalGlucoseReadings,
      processingTime: totalTime
    };

  } catch (error) {
    console.error('❌ Import failed:', error);
    throw error;
  }
}

// Export for use in other modules
export { calculateECGFeatures, importD1NAMOData, processECGFile };

// Run if called directly
if (import.meta.url === `file://${process.argv[1].replace(/\\/g, '/')}` || process.argv[1].endsWith('importD1NAMOData.js')) {
  importD1NAMOData()
    .then((result) => {
      console.log('🎉 D1NAMO import completed successfully:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Import failed:', error);
      process.exit(1);
    })
    .finally(() => {
      driver.close();
    });
}
