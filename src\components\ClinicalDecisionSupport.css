/* Clinical Decision Support Styles */
.clinical-decision-support {
  padding: 1.5rem;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  min-height: 100vh;
}

.cds-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--gb-accent);
}

.cds-header h2 {
  color: var(--gb-accent2);
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
}

.cds-header p {
  color: #a89984;
  margin: 0;
  font-size: 1.1rem;
}

/* Navigation */
.cds-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
  background: var(--gb-bg1);
  padding: 1rem;
  border-radius: 8px;
}

.cds-navigation button {
  background: transparent;
  color: var(--gb-fg);
  border: 1px solid transparent;
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.cds-navigation button:hover {
  background: rgba(215, 153, 33, 0.1);
  border-color: var(--gb-accent);
}

.cds-navigation button.active {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border-color: var(--gb-accent);
  font-weight: 600;
}

/* Content Area */
.cds-content {
  background: var(--gb-bg1);
  border-radius: 12px;
  padding: 2rem;
  min-height: 500px;
}

/* Overview */
.cds-overview {
  width: 100%;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.overview-card {
  background: var(--gb-bg0);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.overview-card.alerts {
  border-left-color: #fb4934;
}

.overview-card.recommendations {
  border-left-color: var(--gb-accent);
}

.overview-card.interactions {
  border-left-color: #fe8019;
}

.overview-card.protocols {
  border-left-color: #83a598;
}

.overview-card h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gb-accent2);
  margin-bottom: 0.5rem;
}

.metric-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.high-priority {
  color: #fb4934;
  font-weight: 600;
}

.medium-priority {
  color: #fe8019;
  font-weight: 500;
}

.recommendation-preview {
  color: #928374;
  font-size: 0.8rem;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.interaction-warning {
  color: #fe8019;
  font-weight: 600;
}

.no-interactions {
  color: #b8bb26;
  font-weight: 600;
}

.due-soon {
  color: #fb4934;
  font-weight: 600;
}

/* Alerts Section */
.alerts-section h3,
.recommendations-section h3,
.interactions-section h3,
.protocols-section h3,
.guidelines-section h3 {
  color: var(--gb-accent2);
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.no-alerts,
.no-interactions {
  text-align: center;
  padding: 3rem 2rem;
  color: #928374;
}

.no-alerts p:first-child,
.no-interactions p:first-child {
  color: #b8bb26;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

/* Alert Cards */
.alerts-list,
.recommendations-list,
.interactions-list,
.protocols-list,
.guidelines-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.alert-card,
.recommendation-card,
.interaction-card,
.protocol-card,
.guideline-card {
  background: var(--gb-bg0);
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid;
  transition: all 0.2s ease;
}

.alert-card:hover,
.recommendation-card:hover,
.interaction-card:hover,
.protocol-card:hover,
.guideline-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

/* Alert Card Styling */
.alert-card.critical {
  border-left-color: #fb4934;
  background: rgba(251, 73, 52, 0.02);
}

.alert-card.warning {
  border-left-color: #fe8019;
  background: rgba(254, 128, 25, 0.02);
}

.alert-card.info {
  border-left-color: #83a598;
  background: rgba(131, 165, 152, 0.02);
}

.alert-header,
.recommendation-header,
.interaction-header,
.protocol-header,
.guideline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.alert-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert-title h4,
.recommendation-header h4,
.interaction-header h4,
.protocol-header h4,
.guideline-header h4 {
  color: var(--gb-accent2);
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.priority-badge,
.severity-badge,
.urgency-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.high,
.severity-badge.severe,
.urgency-badge.high {
  background: #fb4934;
  color: white;
}

.priority-badge.medium,
.severity-badge.moderate,
.urgency-badge.medium {
  background: #fe8019;
  color: white;
}

.priority-badge.low,
.severity-badge.mild,
.urgency-badge.low {
  background: #83a598;
  color: white;
}

.alert-category,
.recommendation-category,
.guideline-organization {
  background: var(--gb-bg1);
  color: var(--gb-accent);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid var(--gb-accent);
}

/* Content Sections */
.alert-content,
.recommendation-content,
.interaction-content,
.protocol-content,
.guideline-content {
  line-height: 1.6;
}

.alert-message,
.recommendation-description,
.protocol-description {
  color: var(--gb-fg);
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.alert-recommendations h5,
.recommendation-rationale h5,
.contraindications h5,
.monitoring-requirements h5,
.interaction-mechanism h5,
.interaction-management h5 {
  color: var(--gb-accent2);
  margin: 0 0 0.5rem 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.alert-recommendations ul,
.contraindications ul {
  margin: 0 0 1rem 0;
  padding-left: 1.25rem;
}

.alert-recommendations li,
.contraindications li {
  margin-bottom: 0.25rem;
  color: var(--gb-fg);
}

.alert-evidence {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gb-bg1);
  font-size: 0.875rem;
  color: #928374;
  font-style: italic;
}

/* Protocol Specific Styles */
.protocol-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gb-bg1);
}

.protocol-frequency,
.protocol-due,
.protocol-evidence {
  font-size: 0.875rem;
  color: var(--gb-fg);
}

.protocol-frequency strong,
.protocol-due strong,
.protocol-evidence strong {
  color: var(--gb-accent2);
}

/* Guideline Specific Styles */
.guideline-section {
  color: var(--gb-accent);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.guideline-text {
  color: var(--gb-fg);
  margin-bottom: 1rem;
  font-size: 1rem;
  line-height: 1.6;
}

.guideline-link {
  color: var(--gb-accent);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.2s ease;
}

.guideline-link:hover {
  color: var(--gb-accent2);
  text-decoration: underline;
}

/* Interaction Card Styling */
.interaction-card.severe {
  border-left-color: #fb4934;
  background: rgba(251, 73, 52, 0.02);
}

.interaction-card.moderate {
  border-left-color: #fe8019;
  background: rgba(254, 128, 25, 0.02);
}

.interaction-card.mild {
  border-left-color: #fabd2f;
  background: rgba(250, 189, 47, 0.02);
}

.interaction-mechanism,
.interaction-management {
  margin-bottom: 1rem;
}

.interaction-mechanism p,
.interaction-management p {
  color: var(--gb-fg);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .cds-navigation {
    justify-content: flex-start;
    overflow-x: auto;
    padding: 0.75rem;
  }

  .overview-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .protocol-details {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .clinical-decision-support {
    padding: 1rem;
  }

  .cds-header h2 {
    font-size: 1.5rem;
  }

  .cds-content {
    padding: 1.5rem;
  }

  .alert-header,
  .recommendation-header,
  .interaction-header,
  .protocol-header,
  .guideline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .alert-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .metric-value {
    font-size: 2rem;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .cds-navigation button {
    font-size: 0.8rem;
    padding: 0.625rem 1rem;
  }

  .alert-card,
  .recommendation-card,
  .interaction-card,
  .protocol-card,
  .guideline-card {
    padding: 1rem;
  }

  .metric-value {
    font-size: 1.75rem;
  }
}

/* Animation for new alerts */
@keyframes alertPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(251, 73, 52, 0.7);
    transform: scale(1);
  }

  50% {
    box-shadow: 0 0 0 10px rgba(251, 73, 52, 0);
    transform: scale(1.02);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(251, 73, 52, 0);
    transform: scale(1);
  }
}

.alert-card.critical.new {
  animation: alertPulse 2s infinite;
}

/* Print styles */
@media print {
  .cds-navigation {
    display: none;
  }

  .clinical-decision-support {
    padding: 0;
  }

  .alert-card,
  .recommendation-card {
    break-inside: avoid;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
  }
}
