/**
 * Prompt Analytics Component Styles
 * Two-tone Gruvbox theme for advanced analytics dashboard and charts
 */

.prompt-analytics-dashboard {
  padding: 1.5rem;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.analytics-controls {
  display: flex;
  gap: 1.25rem;
  align-items: center;
  background: linear-gradient(135deg, var(--gb-bg1) 0%, rgba(215, 153, 33, 0.08) 100%);
  padding: 1.25rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-group label {
  font-weight: 500;
  color: var(--gb-muted-strong);
  font-size: 0.875rem;
}

.control-group select {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  font-size: 0.875rem;
  min-width: 120px;
  transition: all 0.2s ease;
}

.control-group select:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(250, 189, 47, 0.1);
}

.export-btn {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--gb-accent) 0%, var(--gb-accent2) 100%);
  color: var(--gb-bg0);
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: auto;
  box-shadow: var(--gb-shadow-1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.export-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--gb-shadow-2);
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.25rem;
}

.analytics-card {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--gb-shadow-1);
  border: 1px solid var(--gb-border);
  transition: all 0.2s ease;
}

.analytics-card:hover {
  border-color: var(--gb-accent);
  transform: translateY(-1px);
  box-shadow: var(--gb-shadow-2);
}

.analytics-card h3 {
  margin: 0 0 1.25rem 0;
  color: var(--gb-accent2);
  font-size: 1.125rem;
  font-weight: 600;
}

.analytics-card ul {
  margin: 1.25rem 0;
  padding-left: 1.25rem;
}

.analytics-card li {
  margin: 0.5rem 0;
  color: var(--gb-fg);
  line-height: 1.5;
}

/* Chart containers */
.chart-wrapper {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  min-height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  color: var(--gb-muted);
  font-style: italic;
  text-align: center;
}

/* Analytics metrics */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric-item {
  background: var(--gb-surface-1);
  border: 1px solid var(--gb-border);
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
  transition: all 0.2s ease;
}

.metric-item:hover {
  border-color: var(--gb-accent);
  transform: translateY(-1px);
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gb-accent2);
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--gb-muted);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Responsive design */
@media (max-width: 768px) {
  .prompt-analytics-dashboard {
    padding: 1rem;
  }

  .analytics-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .export-btn {
    margin-left: 0;
    align-self: center;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }
}
