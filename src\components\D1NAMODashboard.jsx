import { useEffect, useMemo, useState } from 'react';
import neo4jService from '../services/neo4jService';
import './D1NAMODashboard.css';
import ECGGlucoseCorrelation from './ECGGlucoseCorrelation';
import ECGWaveformViewer from './ECGWaveformViewer';
import PatientHealthRecommendations from './PatientHealthRecommendations';
import PromptTesting from './PromptTesting';

const D1NAMODashboard = () => {
    const [selectedPatient, setSelectedPatient] = useState(null);
    const [patients, setPatients] = useState([]);
    const [synchronizedData, setSynchronizedData] = useState([]);
    const [allPatientData, setAllPatientData] = useState([]); // Store all data for filtering
    const [ecgLeadData, setECGLeadData] = useState([]);
    const [selectedTimestamp, setSelectedTimestamp] = useState(null);
    const [selectedLead, setSelectedLead] = useState('II');
    const [timeRange, setTimeRange] = useState('all'); // Changed to string identifier
    const [availableTimeRanges, setAvailableTimeRanges] = useState([]); // Dynamic time ranges
    const [loading, setLoading] = useState(false);
    const [activeView, setActiveView] = useState('correlation'); // correlation | recommendations | promptTesting | analytics
    const [promptTestHistory, setPromptTestHistory] = useState([]);
    const [error, setError] = useState(null);
    const [dataSummary, setDataSummary] = useState(null);
    const [patientDataQuality, setPatientDataQuality] = useState(null);

    // Initialize Neo4j connection and load patients on component mount
    useEffect(() => {
        initializeConnection();
    }, []);

    const initializeConnection = async () => {
        try {
            setLoading(true);
            setError(null);

            // Connect to Neo4j if not already connected
            if (!neo4jService.isConnected) {
                await neo4jService.connect();
            }

            // Load D1NAMO data summary first
            const summaryResult = await neo4jService.getD1NAMODataSummary();
            if (summaryResult.success && summaryResult.records.length > 0) {
                setDataSummary(summaryResult.records[0]);
            }

            // Load patients after successful connection
            await loadD1NAMOPatients();
        } catch (err) {
            console.error('Error initializing Neo4j connection:', err);
            setError(`Failed to connect to database: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    // Load synchronized data when patient changes
    useEffect(() => {
        if (selectedPatient) {
            loadAllSynchronizedData();
        }
    }, [selectedPatient]);

    // Filter data when time range changes
    useEffect(() => {
        if (allPatientData.length > 0) {
            filterDataByTimeRange();
        }
    }, [timeRange, allPatientData]);

    // Load prompt comparison history for analytics when dashboard mounts or patient changes
    useEffect(() => {
        try {
            const history = localStorage.getItem('promptComparisonHistory');
            if (history) {
                setPromptTestHistory(JSON.parse(history));
            } else {
                setPromptTestHistory([]);
            }
        } catch (e) {
            setPromptTestHistory([]);
        }
    }, [selectedPatient]);

    const loadD1NAMOPatients = async () => {
        try {
            console.log('Loading D1NAMO patients...');

            const result = await neo4jService.getD1NAMOPatients();

            if (result.success && result.records) {
                setPatients(result.records);
                console.log(`Loaded ${result.records.length} D1NAMO patients`);

                // Auto-select first patient if available
                if (result.records.length > 0) {
                    setSelectedPatient(result.records[0]);
                }
            } else {
                console.warn('No D1NAMO patients found in database');
                setPatients([]);
            }
        } catch (err) {
            console.error('Error loading D1NAMO patients:', err);
            setError('Failed to load patient data. Please check your database connection.');
            setPatients([]);
        }
    };

    // Helper function to calculate available time ranges based on actual data
    const calculateAvailableTimeRanges = (data) => {
        if (!data || data.length === 0) return [];

        const timestamps = data
            .map(d => new Date(d.timestamp))
            .filter(d => !isNaN(d.getTime()))
            .sort((a, b) => a - b);

        if (timestamps.length === 0) return [];

        const earliest = timestamps[0];
        const latest = timestamps[timestamps.length - 1];
        const totalSpanMs = latest.getTime() - earliest.getTime();
        const totalSpanDays = totalSpanMs / (24 * 60 * 60 * 1000);

        const ranges = [
            { id: 'all', label: 'All available data', daysFromLatest: null }
        ];

        // Add dynamic ranges based on actual data span
        if (totalSpanDays >= 1) {
            ranges.push({ id: 'last_24h', label: 'Last 24 hours', daysFromLatest: 1 });
        }
        if (totalSpanDays >= 3) {
            ranges.push({ id: 'last_3d', label: 'Last 3 days', daysFromLatest: 3 });
        }
        if (totalSpanDays >= 7) {
            ranges.push({ id: 'last_7d', label: 'Last week', daysFromLatest: 7 });
        }
        if (totalSpanDays >= 14) {
            ranges.push({ id: 'last_14d', label: 'Last 2 weeks', daysFromLatest: 14 });
        }
        if (totalSpanDays >= 30) {
            ranges.push({ id: 'last_30d', label: 'Last month', daysFromLatest: 30 });
        }

        // Note: Additional ranges for the actual data span could be added here if needed
        // based on totalSpanDays (days/weeks/months formatting was removed as unused)

        return ranges;
    };

    // Helper function to filter data based on selected time range
    const filterDataByTimeRange = () => {
        if (!allPatientData.length) {
            setSynchronizedData([]);
            return;
        }

        if (timeRange === 'all') {
            setSynchronizedData(allPatientData);
            return;
        }

        // Backward compatibility: handle numeric timeRange (legacy format)
        if (typeof timeRange === 'number' || !isNaN(parseInt(timeRange))) {
            const daysFromLatest = typeof timeRange === 'number' ? timeRange : parseInt(timeRange);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysFromLatest);

            const filtered = allPatientData.filter(d => {
                const dataDate = new Date(d.timestamp);
                return dataDate >= cutoffDate;
            });

            setSynchronizedData(filtered);
            return;
        }

        // New string identifier format
        const selectedRange = availableTimeRanges.find(r => r.id === timeRange);
        if (!selectedRange || !selectedRange.daysFromLatest) {
            setSynchronizedData(allPatientData);
            return;
        }

        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - selectedRange.daysFromLatest);

        const filtered = allPatientData.filter(d => {
            const dataDate = new Date(d.timestamp);
            return dataDate >= cutoffDate;
        });

        setSynchronizedData(filtered);
        console.log(`Filtered to ${filtered.length} points from last ${selectedRange.daysFromLatest} days`);
    };

    // Load all synchronized data for the selected patient
    const loadAllSynchronizedData = async () => {
        if (!selectedPatient?.patientId) return;

        try {
            setLoading(true);
            setError(null);

            // Load all data without time restriction
            const result = await neo4jService.getSynchronizedECGGlucoseData(
                selectedPatient.patientId,
                365, // Load up to 1 year of data to get the full span
                10000 // Increased limit to get all available data
            );

            if (result.success && result.records) {
                // Normalize record shape to what visualization components expect
                const normalized = result.records.map((r) => ({
                    // Prefer glucose timestamp, fall back to ecg timestamp
                    timestamp: r.glucoseTimestamp || r.ecgTimestamp || r.timestamp,
                    // Standardize glucose key
                    glucose: typeof r.glucoseValue === 'number' ? r.glucoseValue : (
                        typeof r.glucose === 'number' ? r.glucose : undefined
                    ),
                    // Standardize ECG feature keys
                    heartRate: r.heartRate ?? r.heart_rate ?? undefined,
                    hrv: r.hrv ?? r.hrv_rmssd ?? undefined,
                    qtcInterval: r.qtcInterval ?? r.qtc ?? r.qtc_interval ?? undefined,
                    rrInterval: r.rrInterval ?? r.rr_interval ?? undefined,
                    // Carry original ids if present
                    ecgId: r.ecgId,
                    glucoseId: r.glucoseId,
                })).filter(d => d.timestamp);

                // Store all data and calculate available time ranges
                setAllPatientData(normalized);
                const ranges = calculateAvailableTimeRanges(normalized);
                setAvailableTimeRanges(ranges);

                // Set initial time range to 'all' for new patient
                setTimeRange('all');
                setSynchronizedData(normalized);

                console.log(`Loaded ${result.records.length} synchronized rows, ${normalized.length} normalized points`);
                console.log(`Available time ranges:`, ranges);
            } else {
                setAllPatientData([]);
                setSynchronizedData([]);
                setAvailableTimeRanges([]);
            }
        } catch (err) {
            console.error('Error loading synchronized data:', err);
            setError('Failed to load synchronized data.');
            setAllPatientData([]);
            setSynchronizedData([]);
            setAvailableTimeRanges([]);
        } finally {
            setLoading(false);
        }
    };

    const loadECGLeadData = async (timestamp) => {
        if (!selectedPatient?.patientId || !timestamp) return;

        try {
            setLoading(true);
            const result = await neo4jService.getECGLeadData(selectedPatient.patientId, timestamp, selectedLead);

            if (result.success && result.records) {
                setECGLeadData(result.records);
            } else {
                setECGLeadData([]);
            }
        } catch (err) {
            console.error('Error loading ECG lead data:', err);
            setECGLeadData([]);
        } finally {
            setLoading(false);
        }
    };

    // Handle patient selection
    const handlePatientChange = async (patient) => {
        setSelectedPatient(patient);
        setSynchronizedData([]);
        setECGLeadData([]);
        setSelectedTimestamp(null);
        setPatientDataQuality(null);

        if (patient?.patientId) {
            try {
                const qualityResult = await neo4jService.getPatientDataQuality(patient.patientId);
                if (qualityResult.success && qualityResult.records.length > 0) {
                    setPatientDataQuality(qualityResult.records[0]);
                }
            } catch (err) {
                console.warn('Could not load patient data quality:', err.message);
            }
        }
    };

    // Handle time range change
    const handleTimeRangeChange = (rangeId) => {
        // Handle both legacy numeric format and new string identifier format
        setTimeRange(rangeId);
    };

    // Handle ECG data point click for detailed view
    const handleDataPointClick = (dataPoint) => {
        if (dataPoint?.timestamp) {
            setSelectedTimestamp(dataPoint.timestamp);
            loadECGLeadData(dataPoint.timestamp);
        }
    };

    // Handle lead change for ECG viewer
    const handleLeadChange = (leadName) => {
        setSelectedLead(leadName);
    };

    // Calculate dashboard statistics with enhanced D1NAMO-specific metrics
    const dashboardStats = useMemo(() => {
        if (!synchronizedData?.length) return {};

        const glucoseValues = synchronizedData
            .filter(d => d.glucose && !isNaN(d.glucose))
            .map(d => d.glucose);

        const heartRateValues = synchronizedData
            .filter(d => d.heartRate && !isNaN(d.heartRate))
            .map(d => d.heartRate);

        const hrvValues = synchronizedData
            .filter(d => d.hrv && !isNaN(d.hrv))
            .map(d => d.hrv);

        const syncQualityValues = synchronizedData
            .filter(d => d.syncTimeDiff !== undefined)
            .map(d => d.syncTimeDiff || 0);

        // Enhanced D1NAMO-specific metrics
        const perfectSyncCount = syncQualityValues.filter(t => t === 0).length;
        const goodSyncCount = syncQualityValues.filter(t => t <= 5).length;
        const avgSyncTime = syncQualityValues.length > 0 ?
            syncQualityValues.reduce((a, b) => a + b, 0) / syncQualityValues.length : 0;

        return {
            totalReadings: synchronizedData.length,
            avgGlucose: glucoseValues.length > 0 ?
                (glucoseValues.reduce((a, b) => a + b, 0) / glucoseValues.length).toFixed(1) : 'N/A',
            avgHeartRate: heartRateValues.length > 0 ?
                (heartRateValues.reduce((a, b) => a + b, 0) / heartRateValues.length).toFixed(1) : 'N/A',
            avgHRV: hrvValues.length > 0 ?
                (hrvValues.reduce((a, b) => a + b, 0) / hrvValues.length).toFixed(1) : 'N/A',
            timeInRange: glucoseValues.length > 0 ?
                ((glucoseValues.filter(g => g >= 70 && g <= 180).length / glucoseValues.length) * 100).toFixed(1) : 'N/A',
            // D1NAMO-specific synchronization metrics
            perfectSyncPercent: syncQualityValues.length > 0 ?
                ((perfectSyncCount / syncQualityValues.length) * 100).toFixed(1) : 'N/A',
            goodSyncPercent: syncQualityValues.length > 0 ?
                ((goodSyncCount / syncQualityValues.length) * 100).toFixed(1) : 'N/A',
            avgSyncTime: avgSyncTime.toFixed(1),
            dataQuality: avgSyncTime <= 2 ? 'Excellent' : avgSyncTime <= 5 ? 'Good' : avgSyncTime <= 15 ? 'Fair' : 'Poor',
            ecgFeatureCount: synchronizedData.filter(d => d.heartRate || d.hrv || d.qtcInterval).length,
            glucoseDataPoints: glucoseValues.length,
            ecgDataPoints: heartRateValues.length
        };
    }, [synchronizedData]);

    // Build a lightweight patientData object for AI components
    const patientData = useMemo(() => {
        if (!selectedPatient) return null;

        const glucoseValues = synchronizedData
            ?.filter(d => typeof d.glucose === 'number')
            ?.map(d => d.glucose) || [];

        const avgGlucose = glucoseValues.length
            ? glucoseValues.reduce((a, b) => a + b, 0) / glucoseValues.length
            : undefined;

        const timeInRange = glucoseValues.length
            ? (glucoseValues.filter(g => g >= 70 && g <= 180).length / glucoseValues.length) * 100
            : undefined;

        return {
            name: selectedPatient.name,
            age: selectedPatient.age,
            gender: selectedPatient.gender,
            diabetesType: selectedPatient.condition, // map condition -> diabetesType
            diagnosisDate: selectedPatient.diagnosisDate,
            currentMedications: selectedPatient.medications || [],
            recentGlucoseData: {
                averageGlucose: avgGlucose,
                timeInRange: timeInRange ? Number(timeInRange.toFixed(1)) : undefined,
            },
            lifestyle: selectedPatient.lifestyle || {},
            challenges: selectedPatient.challenges || [],
            goals: selectedPatient.goals || [],
        };
    }, [selectedPatient, synchronizedData]);

    const handlePromptResultsUpdate = () => {
        // Refresh local history after a comparison run finishes
        try {
            const history = localStorage.getItem('promptComparisonHistory');
            setPromptTestHistory(history ? JSON.parse(history) : []);
        } catch {
            setPromptTestHistory([]);
        }
    };

    return (
        <div className="d1namo-dashboard">
            <div className="dashboard-header">
                <div className="header-content">
                    <h1>D1NAMO Research Dashboard</h1>
                    <p>Synchronized ECG and Continuous Glucose Monitoring Analysis</p>
                </div>

                <div className="header-controls">
                    <div className="patient-selector">
                        <label>Research Subject:</label>
                        <select
                            value={selectedPatient?.patientId || ''}
                            onChange={(e) => {
                                const patient = patients.find(p => p.patientId === e.target.value);
                                if (patient) handlePatientChange(patient);
                            }}
                            disabled={loading || !patients.length}
                        >
                            <option value="">Select a patient...</option>
                            {patients.map(patient => {
                                const dataIndicator = patient.dataAvailability === 'complete' ? '🟢' :
                                    patient.dataAvailability === 'partial' ? '🟡' : '🔴';
                                return (
                                    <option key={patient.patientId} value={patient.patientId}>
                                        {dataIndicator} {patient.name} ({patient.patientId}) - {patient.ecgReadingCount || 0} ECG, {patient.glucoseReadingCount || 0} Glucose
                                    </option>
                                );
                            })}
                        </select>
                        {selectedPatient?.dataAvailability && (
                            <div className="data-availability-info">
                                <span className={`availability-badge ${selectedPatient.dataAvailability}`}>
                                    {selectedPatient.dataAvailability === 'complete' && '🟢 Complete Data'}
                                    {selectedPatient.dataAvailability === 'partial' && '🟡 Partial Data'}
                                    {selectedPatient.dataAvailability === 'no-data' && '🔴 No Data'}
                                </span>
                            </div>
                        )}
                    </div>

                    <div className="time-range-selector">
                        <label>Time Range:</label>
                        <select
                            value={timeRange}
                            onChange={(e) => handleTimeRangeChange(e.target.value)}
                            disabled={loading || availableTimeRanges.length === 0}
                        >
                            {availableTimeRanges.length === 0 ? (
                                <option value="all">No data available</option>
                            ) : (
                                availableTimeRanges.map(range => (
                                    <option key={range.id} value={range.id}>
                                        {range.label}
                                    </option>
                                ))
                            )}
                        </select>
                    </div>

                    {/* Simple view tabs */}
                    <div className="view-controls">
                        <div className="view-tabs">
                            <button
                                className={`tab-button ${activeView === 'correlation' ? 'active' : ''}`}
                                onClick={() => setActiveView('correlation')}
                            >
                                📈 Correlation
                            </button>
                            <button
                                className={`tab-button ${activeView === 'recommendations' ? 'active' : ''}`}
                                onClick={() => setActiveView('recommendations')}
                            >
                                💡 Recommendations
                            </button>
                            <button
                                className={`tab-button ${activeView === 'promptTesting' ? 'active' : ''}`}
                                onClick={() => setActiveView('promptTesting')}
                            >
                                🧪 Prompt Testing
                            </button>
                            <button
                                className={`tab-button ${activeView === 'analytics' ? 'active' : ''}`}
                                onClick={() => setActiveView('analytics')}
                            >
                                📊 Analytics
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {error && (
                <div className="error-message">
                    <div className="error-icon">⚠️</div>
                    <div className="error-content">
                        <h4>Error Loading Data</h4>
                        <p>{error}</p>
                        <button onClick={loadD1NAMOPatients} className="retry-button">
                            Retry
                        </button>
                    </div>
                </div>
            )}

            {selectedPatient && (
                <div className="patient-summary">
                    <div className="patient-info">
                        <h3>{selectedPatient.name}</h3>
                        <div className="patient-details">
                            <span>Age: {selectedPatient.age}</span>
                            <span>Gender: {selectedPatient.gender}</span>
                            <span>Condition: {selectedPatient.condition}</span>
                            {selectedPatient.studyPhase && (
                                <span>Study Phase: {selectedPatient.studyPhase}</span>
                            )}
                            {selectedPatient.baselineHbA1c && (
                                <span>Baseline HbA1c: {selectedPatient.baselineHbA1c}%</span>
                            )}
                        </div>
                        {patientDataQuality && (
                            <div className="data-quality-summary">
                                <h4>📊 Data Quality Assessment</h4>
                                <div className="quality-metrics">
                                    <span className="metric">ECG Readings: <strong>{patientDataQuality.ecgCount || 0}</strong></span>
                                    <span className="metric">Glucose Readings: <strong>{patientDataQuality.glucoseCount || 0}</strong></span>
                                    <span className="metric">ECG Features: <strong>{patientDataQuality.featuresCount || 0}</strong></span>
                                    <span className={`quality-badge ${patientDataQuality.dataQuality}`}>
                                        {patientDataQuality.dataQuality === 'excellent' && '✅ Excellent'}
                                        {patientDataQuality.dataQuality === 'good' && '👍 Good'}
                                        {patientDataQuality.dataQuality === 'limited' && '⚠️ Limited'}
                                    </span>
                                </div>
                                {patientDataQuality.earliestECG && (
                                    <div className="time-range-info">
                                        <small>ECG Range: {new Date(patientDataQuality.earliestECG).toLocaleDateString()} - {new Date(patientDataQuality.latestECG).toLocaleDateString()}</small>
                                        {patientDataQuality.earliestGlucose && (
                                            <small>Glucose Range: {new Date(patientDataQuality.earliestGlucose).toLocaleDateString()} - {new Date(patientDataQuality.latestGlucose).toLocaleDateString()}</small>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    <div className="dashboard-stats">
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.totalReadings || 0}</span>
                            <span className="stat-label">Synchronized Readings</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgGlucose || 'N/A'}</span>
                            <span className="stat-label">Avg Glucose (mg/dL)</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgHeartRate || 'N/A'}</span>
                            <span className="stat-label">Avg Heart Rate (BPM)</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgHRV || 'N/A'}</span>
                            <span className="stat-label">Avg HRV (ms)</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.timeInRange || 'N/A'}%</span>
                            <span className="stat-label">Time in Range (70-180)</span>
                        </div>
                        <div className="stat-item">
                            <span className={`stat-value quality-${dashboardStats.dataQuality?.toLowerCase()}`}>{dashboardStats.dataQuality || 'N/A'}</span>
                            <span className="stat-label">Sync Quality</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.perfectSyncPercent || 'N/A'}%</span>
                            <span className="stat-label">Perfect Sync</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{dashboardStats.avgSyncTime || 'N/A'} min</span>
                            <span className="stat-label">Avg Sync Time</span>
                        </div>
                    </div>
                </div>
            )}

            {loading && (
                <div className="loading-indicator">
                    <div className="loading-spinner"></div>
                    <p>Loading D1NAMO data...</p>
                </div>
            )}

            {selectedPatient && (
                <div className="dashboard-content">
                    {activeView === 'correlation' && (
                        <>
                            <div className="analysis-section">
                                <h2>ECG-Glucose Correlation Analysis</h2>
                                <ECGGlucoseCorrelation
                                    synchronizedData={synchronizedData}
                                    patientInfo={selectedPatient}
                                    timeRange={timeRange * 24} // convert to hours
                                    onDataPointClick={handleDataPointClick}
                                />
                            </div>

                            {ecgLeadData.length > 0 && (
                                <div className="waveform-section">
                                    <h2>ECG Waveform Analysis</h2>
                                    <div className="waveform-info">
                                        <p>
                                            <strong>Selected Timestamp:</strong> {selectedTimestamp ? new Date(selectedTimestamp).toLocaleString() : 'None'}
                                        </p>
                                        <p>
                                            <strong>Signal Quality:</strong> {ecgLeadData[0]?.quality || 'Unknown'}
                                        </p>
                                    </div>
                                    <ECGWaveformViewer
                                        ecgData={ecgLeadData}
                                        selectedLead={selectedLead}
                                        onLeadChange={handleLeadChange}
                                        showGrid={true}
                                        autoScale={true}
                                        samplingRate={ecgLeadData[0]?.samplingRate || 1000}
                                    />
                                </div>
                            )}
                        </>
                    )}

                    {activeView === 'recommendations' && (
                        <div className="analysis-section">
                            <h2>Personalized Health Recommendations</h2>
                            <PatientHealthRecommendations
                                patientData={patientData}
                                preferredTechnique="structured"
                                showTechniqueSelector={true}
                                onRecommendationsUpdate={() => { /* no-op */ }}
                            />
                        </div>
                    )}

                    {activeView === 'promptTesting' && (
                        <div className="analysis-section">
                            <h2>🧪 Prompt Testing Laboratory</h2>
                            <PromptTesting />
                        </div>
                    )}

                    {activeView === 'analytics' && (
                        <div className="analysis-section">
                            <h2>📊 Patient Analytics</h2>
                            <div className="analytics-placeholder">
                                <p>📈 Advanced analytics features coming soon...</p>
                                <p>This section will include:</p>
                                <ul>
                                    <li>Patient outcome trends</li>
                                    <li>Treatment effectiveness analysis</li>
                                    <li>Population health insights</li>
                                    <li>Comparative analysis tools</li>
                                </ul>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {selectedPatient && !loading && synchronizedData.length === 0 && (
                <div className="no-data-message">
                    <div className="no-data-icon">📊</div>
                    <h3>No Synchronized Data Available</h3>
                    <p>
                        No ECG-glucose synchronized data found for {selectedPatient.name}
                        in the selected time range ({availableTimeRanges.find(r => r.id === timeRange)?.label || timeRange}).
                    </p>
                    {selectedPatient.dataAvailability === 'no-data' ? (
                        <div className="no-data-guidance">
                            <p><strong>⚠️ Patient {selectedPatient.patientId} has no physiological monitoring data.</strong></p>
                            <p>Based on the D1NAMO database analysis:</p>
                            <ul>
                                <li>Patients 001, 002, and 003 have complete ECG-glucose data</li>
                                <li>Patients 004-009 are currently missing synchronized readings</li>
                                <li>Consider selecting patients 001-003 for full dashboard functionality</li>
                            </ul>
                        </div>
                    ) : (
                        <div className="data-guidance">
                            <p>This could mean:</p>
                            <ul>
                                <li>The selected time range doesn't contain synchronized data</li>
                                <li>ECG and glucose readings are more than 15 minutes apart</li>
                                <li>Data import is incomplete for this patient</li>
                                <li>Data hasn't been imported yet</li>
                                <li>There are no synchronized measurements for this patient</li>
                            </ul>
                            <p><strong>Try:</strong> Selecting a different time range or patient with complete data (001-003).</p>
                        </div>
                    )}
                    <button onClick={loadAllSynchronizedData} className="refresh-button">
                        Refresh Data
                    </button>
                </div>
            )}

            <div className="dashboard-footer">
                <div className="footer-info">
                    <p>
                        <strong>D1NAMO Dataset:</strong> Synchronized ECG and continuous glucose monitoring
                        data for diabetes research and clinical analysis.
                    </p>
                    {dataSummary && (
                        <div className="dataset-summary">
                            <p>
                                <strong>Dataset Overview:</strong> {dataSummary.totalPatients} patients,
                                {dataSummary.totalECG} ECG readings, {dataSummary.totalGlucose} glucose measurements
                            </p>
                            <p>
                                <strong>Monitoring Period:</strong> {dataSummary.monitoringDays} days
                                ({new Date(dataSummary.earliestData).toLocaleDateString()} - {new Date(dataSummary.latestData).toLocaleDateString()})
                            </p>
                            <p>
                                <strong>Conditions:</strong> {dataSummary.conditions?.join(', ')}
                            </p>
                        </div>
                    )}
                    <p>
                        <strong>Best Performance:</strong> Patients 001-003 have complete ECG-glucose synchronization.
                        ECG features sampled every 15 minutes with glucose trend correlation.
                    </p>
                </div>

                <div className="footer-actions">
                    <button onClick={loadD1NAMOPatients} disabled={loading}>
                        Refresh Patients
                    </button>
                    {selectedPatient && (
                        <button onClick={loadAllSynchronizedData} disabled={loading}>
                            Refresh Data
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default D1NAMODashboard;
