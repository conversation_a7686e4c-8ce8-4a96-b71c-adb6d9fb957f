// AGP Report Generation Utilities
import { analyzeDailyPatterns, calculateAGPSummary, calculateGMI, calculateTimeInRange } from './agpCalculation';

// Generate comprehensive AGP report data
export function generateAGPReport(glucoseData, patientInfo = {}, agpSettings = {}) {
  if (!glucoseData || glucoseData.length === 0) return null;

  const settings = {
    hypoThreshold: 70,
    hyperThreshold: 180,
    targetMin: 70,
    targetMax: 180,
    ...agpSettings
  };

  const tirStats = calculateTimeInRange(glucoseData, {
    veryLow: { min: 0, max: 54, label: 'Very Low (<54)', color: '#dc2626' },
    low: { min: 54, max: settings.hypoThreshold, label: `Low (54-${settings.hypoThreshold})`, color: '#f59e0b' },
    target: { min: settings.targetMin, max: settings.targetMax, label: `Target Range (${settings.targetMin}-${settings.targetMax})`, color: '#10b981' },
    high: { min: settings.hyperThreshold, max: 250, label: `High (${settings.hyperThreshold}-250)`, color: '#f59e0b' },
    veryHigh: { min: 250, max: 400, label: 'Very High (>250)', color: '#dc2626' }
  });

  const gmiStats = calculateGMI(glucoseData);
  const summaryStats = calculateAGPSummary(glucoseData);
  const dailyPatterns = analyzeDailyPatterns(glucoseData);

  return {
    metadata: {
      reportDate: new Date().toISOString(),
      patientInfo,
      settings,
      dataQuality: {
        totalReadings: glucoseData.length,
        daysOfData: Math.ceil((new Date(Math.max(...glucoseData.map(d => new Date(d.timestamp || d['g.timestamp'])))) -
          new Date(Math.min(...glucoseData.map(d => new Date(d.timestamp || d['g.timestamp']))))) / (1000 * 60 * 60 * 24)),
        readingsPerDay: (glucoseData.length / Math.ceil((new Date(Math.max(...glucoseData.map(d => new Date(d.timestamp || d['g.timestamp'])))) -
          new Date(Math.min(...glucoseData.map(d => new Date(d.timestamp || d['g.timestamp']))))) / (1000 * 60 * 60 * 24))).toFixed(1)
      }
    },
    timeInRange: tirStats,
    glucoseManagementIndicator: gmiStats,
    summaryStatistics: summaryStats,
    dailyPatterns: dailyPatterns,
    clinicalInsights: generateClinicalInsights(tirStats, gmiStats, summaryStats)
  };
}

// Generate clinical insights based on AGP data
function generateClinicalInsights(tirStats, gmiStats, summaryStats) {
  const insights = [];

  // Time in Range insights
  if (tirStats) {
    const targetPercentage = parseFloat(tirStats.targetRangePercentage);
    if (targetPercentage >= 70) {
      insights.push({
        category: 'Time in Range',
        level: 'excellent',
        message: `Excellent glucose control with ${targetPercentage}% time in target range. This meets the clinical goal of ≥70%.`,
        recommendation: 'Continue current diabetes management strategies. Regular monitoring recommended.'
      });
    } else if (targetPercentage >= 50) {
      insights.push({
        category: 'Time in Range',
        level: 'good',
        message: `Good progress with ${targetPercentage}% time in target range. Work toward the 70% clinical goal.`,
        recommendation: 'Consider discussing medication adjustments or lifestyle modifications with healthcare provider.'
      });
    } else {
      insights.push({
        category: 'Time in Range',
        level: 'needs_improvement',
        message: `Time in range is ${targetPercentage}%. This indicates significant opportunity for improvement.`,
        recommendation: 'Consult with healthcare team for comprehensive diabetes management review.'
      });
    }

    // Hypoglycemia analysis
    const lowPercentage = parseFloat(tirStats.ranges.low?.percentage || 0);
    const veryLowPercentage = parseFloat(tirStats.ranges.veryLow?.percentage || 0);

    if (veryLowPercentage > 1) {
      insights.push({
        category: 'Hypoglycemia Risk',
        level: 'alert',
        message: `${veryLowPercentage}% time below 54 mg/dL exceeds the recommended <1% target.`,
        recommendation: 'Urgent discussion with healthcare provider needed. Risk of severe hypoglycemia.'
      });
    }

    if (lowPercentage + veryLowPercentage > 4) {
      insights.push({
        category: 'Hypoglycemia Risk',
        level: 'caution',
        message: `${(lowPercentage + veryLowPercentage).toFixed(1)}% time below range exceeds 4% target.`,
        recommendation: 'Review hypoglycemia prevention strategies with healthcare team.'
      });
    }
  }

  // Glucose variability insights
  if (summaryStats) {
    const cv = parseFloat(summaryStats.coefficientOfVariation);
    if (cv <= 36) {
      insights.push({
        category: 'Glucose Stability',
        level: 'excellent',
        message: `Low glucose variability (CV: ${cv}%) indicates stable glucose patterns.`,
        recommendation: 'Maintain current management approach for consistent glucose control.'
      });
    } else {
      insights.push({
        category: 'Glucose Stability',
        level: 'needs_improvement',
        message: `High glucose variability (CV: ${cv}%) suggests unstable glucose patterns.`,
        recommendation: 'Consider continuous glucose monitoring and pattern analysis with healthcare provider.'
      });
    }
  }

  // GMI insights
  if (gmiStats) {
    const gmi = parseFloat(gmiStats.gmi);
    if (gmi <= 7.0) {
      insights.push({
        category: 'A1C Estimate',
        level: 'excellent',
        message: `Estimated A1C of ${gmi}% meets target for most adults with diabetes.`,
        recommendation: 'Continue current diabetes management to maintain A1C target.'
      });
    } else if (gmi <= 8.0) {
      insights.push({
        category: 'A1C Estimate',
        level: 'good',
        message: `Estimated A1C of ${gmi}% is close to target but could be improved.`,
        recommendation: 'Discuss optimization strategies with healthcare provider.'
      });
    } else {
      insights.push({
        category: 'A1C Estimate',
        level: 'needs_improvement',
        message: `Estimated A1C of ${gmi}% is above target for most adults.`,
        recommendation: 'Comprehensive diabetes management review recommended with healthcare team.'
      });
    }
  }

  return insights;
}

// Export AGP report as JSON
export function exportAGPReportJSON(reportData, filename = null) {
  if (!reportData) return;

  const defaultFilename = `agp_report_${new Date().toISOString().split('T')[0]}.json`;
  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
  downloadBlob(blob, filename || defaultFilename);
}

// Export AGP report as CSV summary
export function exportAGPReportCSV(reportData, filename = null) {
  if (!reportData) return;

  const csvRows = [
    ['AGP Report Summary', ''],
    ['Report Date', reportData.metadata.reportDate],
    ['Total Readings', reportData.metadata.dataQuality.totalReadings],
    ['Days of Data', reportData.metadata.dataQuality.daysOfData],
    ['Readings per Day', reportData.metadata.dataQuality.readingsPerDay],
    ['', ''],
    ['Time in Range Analysis', ''],
    ['Very Low (<54 mg/dL)', `${reportData.timeInRange.ranges.veryLow?.percentage || 0}%`],
    ['Low (54-70 mg/dL)', `${reportData.timeInRange.ranges.low?.percentage || 0}%`],
    ['Target Range (70-180 mg/dL)', `${reportData.timeInRange.ranges.target?.percentage || 0}%`],
    ['High (180-250 mg/dL)', `${reportData.timeInRange.ranges.high?.percentage || 0}%`],
    ['Very High (>250 mg/dL)', `${reportData.timeInRange.ranges.veryHigh?.percentage || 0}%`],
    ['', ''],
    ['Glucose Statistics', ''],
    ['Mean Glucose', `${reportData.summaryStatistics.mean} mg/dL`],
    ['Median Glucose', `${reportData.summaryStatistics.median} mg/dL`],
    ['Standard Deviation', `${reportData.summaryStatistics.standardDeviation} mg/dL`],
    ['Coefficient of Variation', `${reportData.summaryStatistics.coefficientOfVariation}%`],
    ['Glucose Range', `${reportData.summaryStatistics.minimum}-${reportData.summaryStatistics.maximum} mg/dL`],
    ['', ''],
    ['Glucose Management Indicator', ''],
    ['Estimated A1C (GMI)', `${reportData.glucoseManagementIndicator.gmi}%`],
    ['Mean Glucose (GMI)', `${reportData.glucoseManagementIndicator.meanGlucose} mg/dL`]
  ];

  const csvContent = csvRows.map(row => row.join(',')).join('\n');
  const defaultFilename = `agp_summary_${new Date().toISOString().split('T')[0]}.csv`;
  const blob = new Blob([csvContent], { type: 'text/csv' });
  downloadBlob(blob, filename || defaultFilename);
}

// Helper function to download blob
function downloadBlob(blob, filename) {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}
