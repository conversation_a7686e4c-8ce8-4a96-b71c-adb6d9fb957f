import { useEffect, useState } from 'react';
import './AIControlPanel.css';

function AIControlPanel({ onSettingsChange, currentSettings = {} }) {
    const [aiSettings, setAiSettings] = useState({
        enabled: false,
        apiKey: '',
        model: 'gpt-4o-mini',
        maxTokens: 2000,
        temperature: 0.3,
        recommendationLevel: 'detailed',
        contextWindow: 8192,
        clinicalMode: true,
        patientPrivacy: 'strict',
        cacheEnabled: true,
        autoRefresh: false,
        confidenceThreshold: 0.7,
        fallbackEnabled: true,
        responseFormat: 'structured',
        ...currentSettings
    });

    const [testResults, setTestResults] = useState(null);
    const [isConnecting, setIsConnecting] = useState(false);
    const [connectionStatus, setConnectionStatus] = useState('unknown');

    useEffect(() => {
        // Check if OpenAI API key is configured
        const configuredKey = import.meta.env.VITE_OPENAI_API_KEY;
        if (configuredKey && configuredKey !== 'your-openai-api-key-here') {
            setAiSettings(prev => ({
                ...prev,
                apiKey: configuredKey,
                enabled: import.meta.env.VITE_AI_ENABLED === 'true'
            }));
            setConnectionStatus('configured');
        } else {
            setConnectionStatus('not_configured');
        }
    }, []);

    const handleSettingChange = (key, value) => {
        const newSettings = { ...aiSettings, [key]: value };
        setAiSettings(newSettings);
        onSettingsChange?.(newSettings);
    };

    const testConnection = async () => {
        setIsConnecting(true);
        setTestResults(null);

        try {
            const testPrompt = "Respond with 'Connection successful' if you can read this message.";

            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${aiSettings.apiKey}`
                },
                body: JSON.stringify({
                    model: aiSettings.model,
                    messages: [{ role: "user", content: testPrompt }],
                    max_tokens: 50,
                    temperature: 0.1
                })
            });

            if (response.ok) {
                const data = await response.json();
                setTestResults({
                    success: true,
                    message: 'OpenAI API connection successful!',
                    response: data.choices[0]?.message?.content || 'No response content',
                    model: data.model,
                    usage: data.usage
                });
                setConnectionStatus('connected');
            } else {
                const error = await response.json();
                setTestResults({
                    success: false,
                    message: `Connection failed: ${response.status}`,
                    error: error.error?.message || JSON.stringify(error)
                });
                setConnectionStatus('error');
            }
        } catch (error) {
            setTestResults({
                success: false,
                message: 'Connection failed',
                error: error.message
            });
            setConnectionStatus('error');
        } finally {
            setIsConnecting(false);
        }
    };

    const resetToDefaults = () => {
        const defaults = {
            enabled: false,
            apiKey: '',
            model: 'gpt-4o-mini',
            maxTokens: 2000,
            temperature: 0.3,
            recommendationLevel: 'detailed',
            contextWindow: 8192,
            clinicalMode: true,
            patientPrivacy: 'strict',
            cacheEnabled: true,
            autoRefresh: false,
            confidenceThreshold: 0.7,
            fallbackEnabled: true,
            responseFormat: 'structured'
        };
        setAiSettings(defaults);
        onSettingsChange?.(defaults);
    };

    const exportSettings = () => {
        const exportData = {
            ...aiSettings,
            apiKey: '***HIDDEN***' // Don't export the actual API key
        };
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ai-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const getStatusColor = () => {
        switch (connectionStatus) {
            case 'connected': return '#10b981';
            case 'configured': return '#f59e0b';
            case 'error': return '#ef4444';
            case 'not_configured': return '#6b7280';
            default: return '#6b7280';
        }
    };

    const getStatusText = () => {
        switch (connectionStatus) {
            case 'connected': return 'Connected & Tested';
            case 'configured': return 'API Key Configured';
            case 'error': return 'Connection Error';
            case 'not_configured': return 'Not Configured';
            default: return 'Unknown Status';
        }
    };

    return (
        <div className="ai-control-panel">
            <div className="ai-panel-header">
                <div className="ai-title-section">
                    <h2>🤖 AI Control Panel</h2>
                    <div className="ai-status-indicator">
                        <div
                            className="status-dot"
                            style={{ backgroundColor: getStatusColor() }}
                        ></div>
                        <span className="status-text">{getStatusText()}</span>
                    </div>
                </div>

                <div className="ai-panel-actions">
                    <button
                        className="reset-defaults-btn"
                        onClick={() => {
                            try {
                                const current = localStorage.getItem('ai_force_gpt5') === 'true';
                                const next = (!current).toString();
                                localStorage.setItem('ai_force_gpt5', next);
                                alert(`Global GPT-5 override ${next === 'true' ? 'ENABLED' : 'DISABLED'}. New sessions will use: ${next === 'true' ? 'gpt-5' : 'configured model'}.`);
                            } catch (error) {
                                console.error('Failed to toggle GPT-5 setting:', error);
                            }
                        }}
                        title="Enable GPT-5 for all clients (global override)"
                    >
                        🚀 Toggle GPT-5 (Global)
                    </button>
                    <button
                        className="test-connection-btn"
                        onClick={testConnection}
                        disabled={!aiSettings.apiKey || isConnecting}
                    >
                        {isConnecting ? '🔄 Testing...' : '🧪 Test Connection'}
                    </button>
                    <button className="export-settings-btn" onClick={exportSettings}>
                        📥 Export Settings
                    </button>
                    <button className="reset-defaults-btn" onClick={resetToDefaults}>
                        🔄 Reset Defaults
                    </button>
                </div>
            </div>

            {testResults && (
                <div className={`test-results ${testResults.success ? 'success' : 'error'}`}>
                    <h4>{testResults.success ? '✅ Connection Test Results' : '❌ Connection Test Failed'}</h4>
                    <p><strong>Status:</strong> {testResults.message}</p>
                    {testResults.response && <p><strong>AI Response:</strong> {testResults.response}</p>}
                    {testResults.model && <p><strong>Model:</strong> {testResults.model}</p>}
                    {testResults.usage && (
                        <p><strong>Tokens Used:</strong> {testResults.usage.total_tokens}
                            (Prompt: {testResults.usage.prompt_tokens}, Completion: {testResults.usage.completion_tokens})</p>
                    )}
                    {testResults.error && <p><strong>Error:</strong> {testResults.error}</p>}
                </div>
            )}

            <div className="ai-settings-grid">
                {/* Core Configuration */}
                <div className="settings-section">
                    <h3>🔧 Core Configuration</h3>

                    <div className="setting-group">
                        <label className="setting-label">Global GPT-5 Override</label>
                        <div className="setting-inline">
                            <button
                                className="setting-button"
                                onClick={() => {
                                    try {
                                        const current = localStorage.getItem('ai_force_gpt5') === 'true';
                                        const next = (!current).toString();
                                        localStorage.setItem('ai_force_gpt5', next);
                                        alert(`Global GPT-5 override ${next === 'true' ? 'ENABLED' : 'DISABLED'}.`);
                                    } catch (error) {
                                        console.warn('Failed to toggle GPT-5 override:', error);
                                    }
                                }}
                            >
                                {(() => {
                                    try { return localStorage.getItem('ai_force_gpt5') === 'true' ? 'Disable GPT-5' : 'Enable GPT-5'; } catch { return 'Toggle GPT-5'; }
                                })()}
                            </button>
                            <span className="setting-hint">Forces all clients to use GPT-5 when available; falls back automatically if unsupported.</span>
                        </div>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">
                            <input
                                type="checkbox"
                                checked={aiSettings.enabled}
                                onChange={(e) => handleSettingChange('enabled', e.target.checked)}
                            />
                            Enable AI Recommendations
                        </label>
                        <p className="setting-description">
                            Enable or disable AI-powered clinical recommendations throughout the application.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">OpenAI API Key</label>
                        <input
                            type="password"
                            placeholder="sk-..."
                            value={aiSettings.apiKey}
                            onChange={(e) => handleSettingChange('apiKey', e.target.value)}
                            className="setting-input"
                        />
                        <p className="setting-description">
                            Your OpenAI API key for accessing GPT models. Keep this secure and private.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">AI Model</label>
                        <select
                            value={aiSettings.model}
                            onChange={(e) => handleSettingChange('model', e.target.value)}
                            className="setting-select"
                        >
                            <option value="gpt-4o-mini">GPT-4o Mini (Recommended)</option>
                            <option value="gpt-4o">GPT-4o (Advanced)</option>
                            <option value="gpt-4-turbo">GPT-4 Turbo</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo (Cost-effective)</option>
                        </select>
                        <p className="setting-description">
                            Choose the AI model for generating recommendations. GPT-4o Mini offers the best balance of quality and cost.
                        </p>
                    </div>
                </div>

                {/* Response Configuration */}
                <div className="settings-section">
                    <h3>📝 Response Configuration</h3>

                    <div className="setting-group">
                        <label className="setting-label">Max Tokens</label>
                        <input
                            type="range"
                            min="500"
                            max="4000"
                            step="100"
                            value={aiSettings.maxTokens}
                            onChange={(e) => handleSettingChange('maxTokens', parseInt(e.target.value))}
                            className="setting-range"
                        />
                        <span className="range-value">{aiSettings.maxTokens}</span>
                        <p className="setting-description">
                            Maximum tokens for AI responses. Higher values allow more detailed recommendations but cost more.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">Temperature</label>
                        <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.1"
                            value={aiSettings.temperature}
                            onChange={(e) => handleSettingChange('temperature', parseFloat(e.target.value))}
                            className="setting-range"
                        />
                        <span className="range-value">{aiSettings.temperature}</span>
                        <p className="setting-description">
                            Controls randomness in AI responses. Lower values (0.1-0.3) for more consistent clinical advice.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">Recommendation Detail Level</label>
                        <select
                            value={aiSettings.recommendationLevel}
                            onChange={(e) => handleSettingChange('recommendationLevel', e.target.value)}
                            className="setting-select"
                        >
                            <option value="brief">Brief - Key points only</option>
                            <option value="standard">Standard - Balanced detail</option>
                            <option value="detailed">Detailed - Comprehensive analysis</option>
                            <option value="expert">Expert - Maximum clinical depth</option>
                        </select>
                        <p className="setting-description">
                            Choose how detailed you want the AI recommendations to be.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">Response Format</label>
                        <select
                            value={aiSettings.responseFormat}
                            onChange={(e) => handleSettingChange('responseFormat', e.target.value)}
                            className="setting-select"
                        >
                            <option value="structured">Structured JSON</option>
                            <option value="narrative">Natural Language</option>
                            <option value="hybrid">Hybrid (JSON + Narrative)</option>
                        </select>
                    </div>
                </div>

                {/* Clinical Configuration */}
                <div className="settings-section">
                    <h3>🏥 Clinical Configuration</h3>

                    <div className="setting-group">
                        <label className="setting-label">
                            <input
                                type="checkbox"
                                checked={aiSettings.clinicalMode}
                                onChange={(e) => handleSettingChange('clinicalMode', e.target.checked)}
                            />
                            Clinical Mode
                        </label>
                        <p className="setting-description">
                            Enable clinical mode for medical-grade recommendations following ADA/EASD guidelines.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">Patient Privacy Level</label>
                        <select
                            value={aiSettings.patientPrivacy}
                            onChange={(e) => handleSettingChange('patientPrivacy', e.target.value)}
                            className="setting-select"
                        >
                            <option value="strict">Strict - Anonymized data only</option>
                            <option value="moderate">Moderate - Limited patient info</option>
                            <option value="standard">Standard - Standard privacy</option>
                        </select>
                        <p className="setting-description">
                            Control how much patient information is included in AI analysis requests.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">Confidence Threshold</label>
                        <input
                            type="range"
                            min="0.3"
                            max="0.9"
                            step="0.1"
                            value={aiSettings.confidenceThreshold}
                            onChange={(e) => handleSettingChange('confidenceThreshold', parseFloat(e.target.value))}
                            className="setting-range"
                        />
                        <span className="range-value">{Math.round(aiSettings.confidenceThreshold * 100)}%</span>
                        <p className="setting-description">
                            Minimum confidence level required to display AI recommendations.
                        </p>
                    </div>
                </div>

                {/* Performance Configuration */}
                <div className="settings-section">
                    <h3>⚡ Performance Configuration</h3>

                    <div className="setting-group">
                        <label className="setting-label">Context Window</label>
                        <select
                            value={aiSettings.contextWindow}
                            onChange={(e) => handleSettingChange('contextWindow', parseInt(e.target.value))}
                            className="setting-select"
                        >
                            <option value="4096">4K tokens - Fast, basic context</option>
                            <option value="8192">8K tokens - Recommended</option>
                            <option value="16384">16K tokens - Extended context</option>
                            <option value="32768">32K tokens - Maximum context</option>
                        </select>
                        <p className="setting-description">
                            Amount of patient data context to include in AI analysis.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">
                            <input
                                type="checkbox"
                                checked={aiSettings.cacheEnabled}
                                onChange={(e) => handleSettingChange('cacheEnabled', e.target.checked)}
                            />
                            Enable Response Caching
                        </label>
                        <p className="setting-description">
                            Cache AI responses for 5 minutes to reduce API calls and improve performance.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">
                            <input
                                type="checkbox"
                                checked={aiSettings.fallbackEnabled}
                                onChange={(e) => handleSettingChange('fallbackEnabled', e.target.checked)}
                            />
                            Enable Fallback Mode
                        </label>
                        <p className="setting-description">
                            Use enhanced rule-based recommendations when AI is unavailable.
                        </p>
                    </div>

                    <div className="setting-group">
                        <label className="setting-label">
                            <input
                                type="checkbox"
                                checked={aiSettings.autoRefresh}
                                onChange={(e) => handleSettingChange('autoRefresh', e.target.checked)}
                            />
                            Auto-refresh Recommendations
                        </label>
                        <p className="setting-description">
                            Automatically refresh AI recommendations when patient data changes.
                        </p>
                    </div>
                </div>
            </div>

            {/* Configuration Summary */}
            <div className="settings-summary">
                <h3>📊 Current Configuration Summary</h3>
                <div className="summary-grid">
                    <div className="summary-item">
                        <strong>AI Status:</strong> {aiSettings.enabled ? '🟢 Enabled' : '🔴 Disabled'}
                    </div>
                    <div className="summary-item">
                        <strong>Model:</strong> {aiSettings.model}
                    </div>
                    <div className="summary-item">
                        <strong>Max Tokens:</strong> {aiSettings.maxTokens}
                    </div>
                    <div className="summary-item">
                        <strong>Clinical Mode:</strong> {aiSettings.clinicalMode ? '🏥 Enabled' : '❌ Disabled'}
                    </div>
                    <div className="summary-item">
                        <strong>Privacy Level:</strong> {aiSettings.patientPrivacy}
                    </div>
                    <div className="summary-item">
                        <strong>Fallback Mode:</strong> {aiSettings.fallbackEnabled ? '✅ Enabled' : '❌ Disabled'}
                    </div>
                </div>
            </div>

            {/* Usage Guidelines */}
            <div className="usage-guidelines">
                <h3>📋 Usage Guidelines & Best Practices</h3>
                <div className="guidelines-content">
                    <div className="guideline-section">
                        <h4>🔐 Security Best Practices</h4>
                        <ul>
                            <li>Never share your OpenAI API key with others</li>
                            <li>Use environment variables to store sensitive configuration</li>
                            <li>Enable strict patient privacy mode for HIPAA compliance</li>
                            <li>Regularly rotate your API keys</li>
                        </ul>
                    </div>

                    <div className="guideline-section">
                        <h4>⚕️ Clinical Recommendations</h4>
                        <ul>
                            <li>AI recommendations are suggestions only - not medical advice</li>
                            <li>Always consult healthcare providers for treatment decisions</li>
                            <li>Enable clinical mode for evidence-based guidelines</li>
                            <li>Review AI confidence levels before acting on recommendations</li>
                        </ul>
                    </div>

                    <div className="guideline-section">
                        <h4>💰 Cost Optimization</h4>
                        <ul>
                            <li>Use GPT-4o Mini for best cost-effectiveness</li>
                            <li>Enable caching to reduce redundant API calls</li>
                            <li>Set appropriate max token limits</li>
                            <li>Monitor your OpenAI usage dashboard regularly</li>
                        </ul>
                    </div>

                    <div className="guideline-section">
                        <h4>🎯 Performance Optimization</h4>
                        <ul>
                            <li>Lower temperature settings for consistent clinical advice</li>
                            <li>Use appropriate context window sizes</li>
                            <li>Enable fallback mode for reliability</li>
                            <li>Test connections regularly</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default AIControlPanel;
