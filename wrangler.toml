name = "healthhub-research-platform"
compatibility_date = "2024-10-01"
compatibility_flags = ["nodejs_compat"]

# Pages configuration
pages_build_output_dir = "./dist"

# Environment-specific configurations
[env.production]
name = "healthhub-research-platform"

[env.preview]
name = "healthhub-research-platform-preview"

# KV namespace bindings (if needed for caching)
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your_kv_namespace_id"
# preview_id = "your_preview_kv_id"

# D1 database bindings (if switching from Neo4j)
# [[d1_databases]]
# binding = "DB"
# database_name = "healthhub-db"
# database_id = "your_d1_database_id"

# Environment variables for Cloudflare Functions
# Set these using: wrangler pages secret put <SECRET_NAME> --project-name=healthhub-research-platform
