import { lazy, Suspense } from 'react'
import './Neo4jWorkbench.css'

const QueryRunner = lazy(() => import('./QueryRunner'))

function Neo4jWorkbench() {
    return (
        <div className="neo4j-workbench">
            <div className="workbench-panel single">
                <Suspense fallback={<div className="loading">Loading Query Runner...</div>}>
                    <QueryRunner />
                </Suspense>
            </div>
        </div>
    )
}

export default Neo4jWorkbench
