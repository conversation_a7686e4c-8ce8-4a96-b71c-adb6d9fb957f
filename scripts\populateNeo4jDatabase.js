// Neo4j Database Population Script
// Creates 7 patients with comprehensive healthcare data over 1 month
// Run this script against your Neo4j database to populate sample data

const neo4j = require('neo4j-driver');
require('dotenv').config();

// Database connection configuration
const uri = process.env.VITE_NEO4J_URI;
const username = process.env.VITE_NEO4J_USERNAME;
const password = process.env.VITE_NEO4J_PASSWORD;

if (!uri || !username || !password) {
  console.error('Missing Neo4j env vars. Please set VITE_NEO4J_URI, VITE_NEO4J_USERNAME, VITE_NEO4J_PASSWORD.');
  process.exit(1);
}

// Create driver instance
const driver = neo4j.driver(uri, neo4j.auth.basic(username, password));

// Patient data templates
const patients = [
  {
    patientId: 'Patient_1',
    name: '<PERSON>',
    age: 45,
    gender: 'Male',
    condition: 'Type 2 Diabetes',
    diagnosis_date: '2024-01-15',
    insurance: '<PERSON> Cross',
    phone: '555-0101',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_2',
    name: '<PERSON>',
    age: 38,
    gender: 'Female',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2023-08-20',
    insurance: 'Aetna',
    phone: '555-0102',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_3',
    name: 'Robert Chen',
    age: 52,
    gender: 'Male',
    condition: 'Prediabetes',
    diagnosis_date: '2024-03-10',
    insurance: 'Kaiser',
    phone: '555-0103',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_4',
    name: 'Sarah Johnson',
    age: 29,
    gender: 'Female',
    condition: 'Gestational Diabetes',
    diagnosis_date: '2024-06-01',
    insurance: 'Cigna',
    phone: '555-0104',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_5',
    name: 'David Williams',
    age: 67,
    gender: 'Male',
    condition: 'Type 2 Diabetes',
    diagnosis_date: '2022-11-08',
    insurance: 'Medicare',
    phone: '555-0105',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_6',
    name: 'Emily Davis',
    age: 34,
    gender: 'Female',
    condition: 'Type 1 Diabetes',
    diagnosis_date: '2023-12-15',
    insurance: 'UnitedHealth',
    phone: '555-0106',
    email: '<EMAIL>'
  },
  {
    patientId: 'Patient_7',
    name: 'Michael Brown',
    age: 41,
    gender: 'Male',
    condition: 'Type 2 Diabetes',
    diagnosis_date: '2024-02-28',
    insurance: 'Humana',
    phone: '555-0107',
    email: '<EMAIL>'
  }
];

// Doctors and medical staff
const doctors = [
  {
    doctorId: 'DOC001',
    name: 'Dr. Sarah Wilson',
    specialty: 'Endocrinology',
    license: 'MD12345',
    phone: '555-1001',
    email: '<EMAIL>'
  },
  {
    doctorId: 'DOC002',
    name: 'Dr. James Miller',
    specialty: 'Internal Medicine',
    license: 'MD12346',
    phone: '555-1002',
    email: '<EMAIL>'
  },
  {
    doctorId: 'DOC003',
    name: 'Dr. Lisa Thompson',
    specialty: 'Diabetes Care',
    license: 'MD12347',
    phone: '555-1003',
    email: '<EMAIL>'
  }
];

// Medications
const medications = [
  { name: 'Metformin', type: 'Oral', dosage: '500mg', frequency: 'Twice daily' },
  { name: 'Insulin Glargine', type: 'Injection', dosage: '20 units', frequency: 'Once daily' },
  { name: 'Insulin Lispro', type: 'Injection', dosage: '5-15 units', frequency: 'Before meals' },
  { name: 'Linagliptin', type: 'Oral', dosage: '5mg', frequency: 'Once daily' },
  { name: 'Glyburide', type: 'Oral', dosage: '2.5mg', frequency: 'Twice daily' }
];

// Hospital and departments
const hospitalData = {
  hospital: {
    name: 'Metro General Hospital',
    location: 'New York, NY',
    beds: 500,
    established: '1985'
  },
  departments: [
    { name: 'Endocrinology', head: 'Dr. Sarah Wilson', capacity: 50 },
    { name: 'Internal Medicine', head: 'Dr. James Miller', capacity: 75 },
    { name: 'Laboratory', head: 'Dr. Robert Lee', capacity: 30 },
    { name: 'Radiology', head: 'Dr. Karen Smith', capacity: 40 }
  ]
};

// Generate realistic glucose readings for a patient over 30 days
function generateGlucoseReadings(patientId, patientType = 'type2') {
  const readings = [];
  const now = new Date();
  const startDate = new Date(now);
  startDate.setDate(startDate.getDate() - 30);

  // Patient-specific glucose patterns
  const patterns = {
    type1: { base: 140, variance: 60, dawn: 30, meal: 40, night: -20 },
    type2: { base: 130, variance: 40, dawn: 25, meal: 35, night: -15 },
    prediabetes: { base: 115, variance: 25, dawn: 15, meal: 25, night: -10 },
    gestational: { base: 125, variance: 30, dawn: 20, meal: 30, night: -12 }
  };

  const pattern = patterns[patientType] || patterns.type2;

  for (let day = 0; day < 30; day++) {
    // 4-6 readings per day (fasting, pre-meal, post-meal)
    const readingsPerDay = Math.floor(Math.random() * 3) + 4;

    for (let reading = 0; reading < readingsPerDay; reading++) {
      const timestamp = new Date(startDate);
      timestamp.setDate(timestamp.getDate() + day);

      // Set realistic times
      const times = [7, 9, 12, 15, 18, 21]; // 7am, 9am, 12pm, 3pm, 6pm, 9pm
      timestamp.setHours(times[reading % times.length]);
      timestamp.setMinutes(Math.random() * 60);
      timestamp.setSeconds(0);

      let glucose = pattern.base;
      const hour = timestamp.getHours();

      // Add time-based variations
      if (hour >= 6 && hour <= 8) glucose += pattern.dawn; // Dawn phenomenon
      if ((hour >= 8 && hour <= 10) || (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20)) {
        glucose += pattern.meal; // Post-meal spikes
      }
      if (hour >= 22 || hour <= 5) glucose += pattern.night; // Night time

      // Add random variation
      glucose += (Math.random() - 0.5) * pattern.variance;

      // Ensure realistic bounds
      glucose = Math.max(60, Math.min(300, Math.round(glucose)));

      readings.push({
        timestamp: timestamp.toISOString(),
        glucose: glucose,
        readingType: getReadingType(hour),
        patientId: patientId
      });
    }
  }

  return readings.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
}

function getReadingType(hour) {
  if (hour >= 6 && hour <= 8) return 'Fasting';
  if ((hour >= 8 && hour <= 10) || (hour >= 12 && hour <= 14) || (hour >= 18 && hour <= 20)) return 'Post-meal';
  if ((hour >= 11 && hour <= 12) || (hour >= 17 && hour <= 18)) return 'Pre-meal';
  return 'Random';
}

// Generate HbA1c and other lab results
function generateLabResults(patientId) {
  const results = [];
  const now = new Date();

  // Monthly HbA1c
  for (let month = 0; month < 3; month++) {
    const date = new Date(now);
    date.setMonth(date.getMonth() - month);
    date.setDate(15); // Mid-month testing

    results.push({
      patientId: patientId,
      type: 'HbA1c',
      value: (Math.random() * 3 + 6.5).toFixed(1), // 6.5-9.5%
      unit: '%',
      timestamp: date.toISOString(),
      normal_range: '< 7.0%'
    });
  }

  // Quarterly cholesterol
  const cholesterolDate = new Date(now);
  cholesterolDate.setMonth(cholesterolDate.getMonth() - 1);
  results.push({
    patientId: patientId,
    type: 'Total Cholesterol',
    value: Math.floor(Math.random() * 100 + 180), // 180-280 mg/dL
    unit: 'mg/dL',
    timestamp: cholesterolDate.toISOString(),
    normal_range: '< 200 mg/dL'
  });

  return results;
}

// Generate weight measurements
function generateWeightMeasurements(patientId) {
  const measurements = [];
  const now = new Date();
  const baseWeight = Math.floor(Math.random() * 50 + 150); // 150-200 lbs

  for (let week = 0; week < 4; week++) {
    const date = new Date(now);
    date.setDate(date.getDate() - (week * 7));

    measurements.push({
      patientId: patientId,
      weight: baseWeight + (Math.random() - 0.5) * 10, // ±5 lbs variation
      unit: 'lbs',
      timestamp: date.toISOString()
    });
  }

  return measurements;
}

// Database population functions
async function clearDatabase(session) {
  console.log('Clearing existing data...');
  await session.run('MATCH (n) DETACH DELETE n');
  console.log('Database cleared.');
}

async function createPatients(session) {
  console.log('Creating patients...');

  for (const patient of patients) {
    await session.run(`
      CREATE (p:Patient {
        patientId: $patientId,
        name: $name,
        age: $age,
        gender: $gender,
        condition: $condition,
        diagnosis_date: $diagnosis_date,
        insurance: $insurance,
        phone: $phone,
        email: $email,
        created_at: datetime()
      })
    `, patient);
  }

  console.log(`Created ${patients.length} patients.`);
}

async function createDoctors(session) {
  console.log('Creating doctors...');

  for (const doctor of doctors) {
    await session.run(`
      CREATE (d:Doctor {
        doctorId: $doctorId,
        name: $name,
        specialty: $specialty,
        license: $license,
        phone: $phone,
        email: $email,
        created_at: datetime()
      })
    `, doctor);
  }

  console.log(`Created ${doctors.length} doctors.`);
}

async function createHospitalStructure(session) {
  console.log('Creating hospital structure...');

  // Create hospital
  await session.run(`
    CREATE (h:Hospital {
      name: $name,
      location: $location,
      beds: $beds,
      established: $established
    })
  `, hospitalData.hospital);

  // Create departments
  for (const dept of hospitalData.departments) {
    await session.run(`
      MATCH (h:Hospital {name: $hospitalName})
      CREATE (d:Department {
        name: $name,
        head: $head,
        capacity: $capacity
      })
      CREATE (h)-[:HAS_DEPARTMENT]->(d)
    `, { ...dept, hospitalName: hospitalData.hospital.name });
  }

  console.log('Created hospital structure.');
}

async function createGlucoseReadings(session) {
  console.log('Creating glucose readings...');

  const patientTypes = ['type2', 'type1', 'prediabetes', 'gestational', 'type2', 'type1', 'type2'];

  for (let i = 0; i < patients.length; i++) {
    const patient = patients[i];
    const readings = generateGlucoseReadings(patient.patientId, patientTypes[i]);

    console.log(`Creating ${readings.length} glucose readings for ${patient.name}...`);

    for (const reading of readings) {
      await session.run(`
        MATCH (p:Patient {patientId: $patientId})
        CREATE (g:GlucoseReading {
          glucose: $glucose,
          timestamp: $timestamp,
          readingType: $readingType,
          created_at: datetime()
        })
        CREATE (p)-[:HAD_READING]->(g)
      `, reading);
    }
  }

  console.log('Created all glucose readings.');
}

async function createLabResults(session) {
  console.log('Creating lab results...');

  for (const patient of patients) {
    const results = generateLabResults(patient.patientId);

    for (const result of results) {
      await session.run(`
        MATCH (p:Patient {patientId: $patientId})
        CREATE (l:LabResult {
          type: $type,
          value: $value,
          unit: $unit,
          timestamp: $timestamp,
          normal_range: $normal_range,
          created_at: datetime()
        })
        CREATE (p)-[:HAD_LAB_RESULT]->(l)
      `, result);
    }
  }

  console.log('Created lab results.');
}

async function createWeightMeasurements(session) {
  console.log('Creating weight measurements...');

  for (const patient of patients) {
    const measurements = generateWeightMeasurements(patient.patientId);

    for (const measurement of measurements) {
      await session.run(`
        MATCH (p:Patient {patientId: $patientId})
        CREATE (w:WeightMeasurement {
          weight: $weight,
          unit: $unit,
          timestamp: $timestamp,
          created_at: datetime()
        })
        CREATE (p)-[:HAD_MEASUREMENT]->(w)
      `, measurement);
    }
  }

  console.log('Created weight measurements.');
}

async function createMedications(session) {
  console.log('Creating medications and prescriptions...');

  // Create medications
  for (const med of medications) {
    await session.run(`
      CREATE (m:Medication {
        name: $name,
        type: $type,
        dosage: $dosage,
        frequency: $frequency
      })
    `, med);
  }

  // Create prescriptions
  for (const patient of patients) {
    // Each patient gets 1-3 medications
    const numMeds = Math.floor(Math.random() * 3) + 1;
    const patientMeds = medications.slice(0, numMeds);

    for (const med of patientMeds) {
      const prescriptionDate = new Date();
      prescriptionDate.setDate(prescriptionDate.getDate() - Math.floor(Math.random() * 30));

      await session.run(`
        MATCH (p:Patient {patientId: $patientId})
        MATCH (m:Medication {name: $medName})
        CREATE (pr:Prescription {
          prescribed_date: $prescriptionDate,
          dosage: $dosage,
          frequency: $frequency,
          prescribed_by: $doctorId
        })
        CREATE (p)-[:HAS_PRESCRIPTION]->(pr)
        CREATE (pr)-[:FOR_MEDICATION]->(m)
      `, {
        patientId: patient.patientId,
        medName: med.name,
        prescriptionDate: prescriptionDate.toISOString(),
        dosage: med.dosage,
        frequency: med.frequency,
        doctorId: doctors[Math.floor(Math.random() * doctors.length)].doctorId
      });
    }
  }

  console.log('Created medications and prescriptions.');
}

async function createDoctorPatientRelationships(session) {
  console.log('Creating doctor-patient relationships...');

  for (const patient of patients) {
    // Assign each patient to 1-2 doctors
    const numDoctors = Math.floor(Math.random() * 2) + 1;
    const assignedDoctors = doctors.slice(0, numDoctors);

    for (const doctor of assignedDoctors) {
      await session.run(`
        MATCH (p:Patient {patientId: $patientId})
        MATCH (d:Doctor {doctorId: $doctorId})
        CREATE (p)-[:TREATED_BY {
          relationship_start: $startDate,
          primary_care: $isPrimary
        }]->(d)
      `, {
        patientId: patient.patientId,
        doctorId: doctor.doctorId,
        startDate: patient.diagnosis_date,
        isPrimary: assignedDoctors.indexOf(doctor) === 0
      });
    }
  }

  console.log('Created doctor-patient relationships.');
}

// Main population function
async function populateDatabase() {
  const session = driver.session();

  try {
    console.log('Starting database population...');
    console.log('Connected to Neo4j at:', uri);

    // Clear existing data (optional - comment out if you want to keep existing data)
    await clearDatabase(session);

    // Create all entities and relationships
    await createPatients(session);
    await createDoctors(session);
    await createHospitalStructure(session);
    await createGlucoseReadings(session);
    await createLabResults(session);
    await createWeightMeasurements(session);
    await createMedications(session);
    await createDoctorPatientRelationships(session);

    // Create some additional research relationships
    console.log('Creating research relationships...');
    await session.run(`
      MATCH (d:Doctor)
      CREATE (r:Researcher {
        name: d.name + " Research",
        department: d.specialty,
        years_experience: toInteger(rand() * 15) + 5
      })
      CREATE (d)-[:CONDUCTS_RESEARCH]->(r)
    `);

    await session.run(`
      MATCH (r:Researcher)
      CREATE (s:Study {
        title: "Diabetes Management Study " + toString(id(r)),
        phase: "Phase " + toString(toInteger(rand() * 3) + 1),
        participants: toInteger(rand() * 500) + 100,
        start_date: date().year + "-" + toString(toInteger(rand() * 12) + 1) + "-01"
      })
      CREATE (r)-[:CONDUCTS]->(s)
    `);

    console.log('Database population completed successfully!');
    console.log('Summary:');
    console.log(`- ${patients.length} patients with comprehensive medical records`);
    console.log('- ~140-180 glucose readings per patient (30 days)');
    console.log('- Lab results (HbA1c, cholesterol) for all patients');
    console.log('- Weight measurements and medication prescriptions');
    console.log('- Hospital structure with departments and staff');
    console.log('- Research entities for academic queries');

  } catch (error) {
    console.error('Error populating database:', error);
  } finally {
    await session.close();
  }
}

// Helper function to test connection
async function testConnection() {
  const session = driver.session();
  try {
    const result = await session.run('RETURN "Hello Neo4j!" as message');
    console.log('Connection successful:', result.records[0].get('message'));
    return true;
  } catch (error) {
    console.error('Connection failed:', error);
    return false;
  } finally {
    await session.close();
  }
}

// Run the population script
async function main() {
  console.log('Neo4j Healthcare Database Population Script');
  console.log('=========================================');

  // Test connection first
  if (await testConnection()) {
    await populateDatabase();
  }

  await driver.close();
  console.log('Script completed.');
}

// Export for use in other modules
module.exports = {
  populateDatabase,
  testConnection,
  patients,
  doctors,
  hospitalData
};

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
