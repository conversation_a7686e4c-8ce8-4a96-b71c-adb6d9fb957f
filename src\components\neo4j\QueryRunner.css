/* Compact layout to fit one screen (no scroll) */
.query-runner {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  height: 100%;
  overflow: hidden;
}

.query-runner-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.query-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.query-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 1rem;
  flex: 1;
  min-height: 0;
}

.predefined-queries,
.agp-settings-panel,
.ai-control-panel {
  overflow: auto;
  max-height: 100%;
}

.query-editor {
  .query-card-actions.compact-actions {
    display: flex;
    gap: 0;
  }

  .query-card-actions.compact-actions .execute-button,
  .query-card-actions.compact-actions .view-button {
    border-radius: 6px 0 0 6px;
    margin: 0;
    padding: 0.4rem 0.8rem;
    font-size: 0.95em;
    min-width: 0;
    line-height: 1.2;
  }

  .query-card-actions.compact-actions .view-button {
    border-radius: 0 6px 6px 0;
    border-left: 1px solid rgba(235, 219, 178, 0.15);
  }

  .query-card {
    padding: 0.6rem 0.7rem 0.5rem 0.7rem;
    margin-bottom: 0.5rem;
    min-width: 0;
    font-size: 0.98em;
    background: rgba(235, 219, 178, 0.07);
    border-radius: 8px;
    border: 1px solid rgba(235, 219, 178, 0.13);
    box-shadow: none;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 0.7rem;
  }

  .query-card-content {
    flex: 1 1 auto;
    min-width: 0;
    margin-right: 0.5rem;
  }

  .query-card h5 {
    margin: 0 0 0.1em 0;
    font-size: 1em;
    font-weight: 600;
  }

  .query-card p {
    margin: 0 0 0.2em 0;
    font-size: 0.97em;
    opacity: 0.92;
  }

  .parameters-info {
    margin: 0.1em 0 0 0;
    font-size: 0.92em;
    opacity: 0.7;
  }

  gap: 0.75rem;
  overflow: auto;
}

.query-textarea {
  resize: vertical;
  min-height: 120px;
  max-height: 200px;
}

.query-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.results-section {
  border-top: 1px solid rgba(235, 219, 178, 0.2);
  padding-top: 0.75rem;
}

.raw-results {
  overflow: auto;
  max-height: 240px;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
}

.results-table th,
.results-table td {
  text-align: left;
  padding: 0.4rem 0.5rem;
}

.agp-section,
.agp-chart,
.agp-statistics,
.ai-recommendations-section {
  display: none !important;
}
