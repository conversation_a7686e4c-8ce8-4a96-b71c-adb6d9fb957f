/* Advanced Search Styles */
.advanced-search {
  padding: 1.5rem;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  min-height: 100vh;
}

.search-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid var(--gb-accent);
}

.search-header h2 {
  color: var(--gb-accent2);
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
}

.search-header p {
  color: #a89984;
  margin: 0;
  font-size: 1.1rem;
}

/* Search Layout */
.search-layout {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .search-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.search-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Basic Search */
.basic-search {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid var(--gb-bg0);
  transition: all 0.2s ease;
}

.basic-search:focus-within {
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 3px rgba(215, 153, 33, 0.2);
}

.search-input-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-input {
  width: 100%;
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 2px solid var(--gb-bg0);
  padding: 1rem;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 3px rgba(215, 153, 33, 0.2);
}

.search-input::placeholder {
  color: #928374;
}

.search-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.search-type-select {
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 1px solid var(--gb-accent);
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
}

.advanced-toggle {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.advanced-toggle:hover {
  background: var(--gb-accent2);
}

/* Advanced Filters */
.advanced-filters {
  background: var(--gb-bg1);
  border-radius: 8px;
  border: 2px solid var(--gb-bg0);
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
}

.advanced-filters.expanded {
  padding: 1.5rem;
  max-height: 2000px;
  opacity: 1;
  border-color: var(--gb-accent);
}

.filters-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  color: #a89984;
  font-size: 0.95rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--gb-accent);
}

.filter-group select,
.filter-group input[type="number"],
.filter-group input[type="date"] {
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 1px solid var(--gb-bg0);
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: var(--gb-accent);
  box-shadow: 0 0 0 2px rgba(215, 153, 33, 0.2);
}

.range-inputs,
.date-range-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.sort-controls {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 0.5rem;
}

.conditions-select {
  min-height: 80px;
}

.conditions-select option {
  padding: 0.25rem 0.5rem;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid var(--gb-bg0);
}

.clear-filters-btn,
.save-search-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-filters-btn {
  background: var(--gb-bg0);
  color: var(--gb-fg);
  border: 1px solid #fb4934;
}

.clear-filters-btn:hover {
  background: #fb4934;
  color: white;
}

.save-search-btn {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border: none;
}

.save-search-btn:hover {
  background: var(--gb-accent2);
}

/* Search Sidebar */
.search-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.search-history,
.saved-searches {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid var(--gb-bg0);
}

.search-history h4,
.saved-searches h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.history-item,
.saved-search-item {
  padding: 0.75rem;
  background: var(--gb-bg0);
  border-radius: 4px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: rgba(215, 153, 33, 0.1);
  border-left: 3px solid var(--gb-accent);
}

.history-query {
  display: block;
  color: var(--gb-fg);
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.history-meta {
  color: #928374;
  font-size: 0.75rem;
}

.saved-search-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.saved-search-info {
  flex: 1;
  cursor: pointer;
}

.saved-search-name {
  display: block;
  color: var(--gb-accent2);
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.saved-search-query {
  color: #928374;
  font-size: 0.8rem;
}

.delete-saved-search {
  background: none;
  border: none;
  color: #fb4934;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.delete-saved-search:hover {
  background: rgba(251, 73, 52, 0.1);
}

/* Search Results */
.search-content {
  background: var(--gb-bg1);
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid var(--gb-bg0);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--gb-bg0);
}

.results-header h3 {
  color: var(--gb-accent2);
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.results-meta {
  color: #a89984;
  font-size: 0.875rem;
}

.searching {
  color: var(--gb-accent);
  animation: pulse 1.5s ease-in-out infinite;
}

/* Results Sections */
.results-section {
  margin-bottom: 2rem;
}

.results-section h4 {
  color: var(--gb-accent2);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Patient Results */
.patient-results {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.patient-result-card {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-bg1);
  border-radius: 6px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.patient-result-card:hover {
  border-color: var(--gb-accent);
  background: rgba(215, 153, 33, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.patient-info h5 {
  color: var(--gb-accent2);
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.patient-condition {
  color: var(--gb-accent);
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 500;
}

.patient-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.patient-meta span {
  color: #928374;
  font-size: 0.8rem;
}

.patient-stats {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  color: #928374;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.stat-value {
  color: var(--gb-fg);
  font-weight: 600;
  font-size: 0.875rem;
}

/* Reading Results */
.reading-results {
  overflow-x: auto;
}

.readings-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.readings-table th,
.readings-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--gb-bg0);
}

.readings-table th {
  background: var(--gb-bg0);
  color: var(--gb-accent2);
  font-weight: 600;
  position: sticky;
  top: 0;
}

.readings-table tbody tr:hover {
  background: rgba(215, 153, 33, 0.05);
}

.glucose-value {
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.glucose-value.low {
  color: #fb4934;
  background: rgba(251, 73, 52, 0.1);
}

.glucose-value.high {
  color: #fe8019;
  background: rgba(254, 128, 25, 0.1);
}

.glucose-value.normal {
  color: #b8bb26;
  background: rgba(184, 187, 38, 0.1);
}

.results-truncated {
  text-align: center;
  color: #928374;
  font-style: italic;
  margin-top: 1rem;
  padding: 1rem;
  background: var(--gb-bg0);
  border-radius: 4px;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 3rem 1rem;
  color: #928374;
}

.no-results h4 {
  color: var(--gb-accent2);
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .advanced-search {
    padding: 1rem;
  }

  .search-header h2 {
    font-size: 1.5rem;
  }

  .search-layout {
    grid-template-columns: 1fr;
  }

  .search-controls {
    flex-direction: row;
    gap: 0.5rem;
  }

  .advanced-toggle {
    white-space: nowrap;
  }

  .patient-results {
    grid-template-columns: 1fr;
  }

  .patient-stats {
    justify-content: center;
  }

  .filter-actions {
    flex-direction: column;
  }

  .readings-table {
    font-size: 0.75rem;
  }

  .readings-table th,
  .readings-table td {
    padding: 0.5rem 0.25rem;
  }
}

/* Animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

/* Focus states for accessibility */
.search-input:focus,
.filter-group select:focus,
.filter-group input:focus {
  outline: 2px solid var(--gb-accent);
  outline-offset: 2px;
}

.patient-result-card:focus {
  outline: 2px solid var(--gb-accent);
  outline-offset: 2px;
}

/* Print styles */
@media print {

  .search-controls,
  .search-sidebar {
    display: none;
  }

  .search-content {
    background: var(--gb-bg1);
    border: 1px solid #000;
    box-shadow: none;
  }

  .patient-result-card {
    break-inside: avoid;
    border: 1px solid #ccc;
  }
}
