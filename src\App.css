/* App container respects global Gruvbox theme */
.app {
  min-height: 100vh;
  background: var(--gb-bg0);
  color: var(--gb-fg);
}

/* Login Layout */
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: var(--gb-bg1);
  padding: 2rem 1rem;
  text-align: center;
  border-bottom: 1px solid var(--gb-bg0);
}

.app-header h1 {
  color: var(--gb-accent2);
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.app-header p {
  color: var(--gb-fg);
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.8;
}

.app-main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.app-footer {
  background: var(--gb-bg1);
  padding: 1rem;
  text-align: center;
  border-top: 1px solid var(--gb-bg0);
  color: #928374;
  font-size: 0.875rem;
}

/* Authenticated Layout */
.app-authenticated {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Main Navigation */
.main-navigation {
  background: var(--gb-bg1);
  border-bottom: 2px solid var(--gb-accent);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.nav-brand h1 {
  color: var(--gb-accent2);
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
}

.nav-brand p {
  color: #a89984;
  margin: 0;
  font-size: 0.875rem;
}

.nav-menu {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.nav-menu button {
  background: transparent;
  border: 1px solid transparent;
  color: var(--gb-fg);
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.nav-menu button:hover {
  background: rgba(215, 153, 33, 0.1);
  border-color: var(--gb-accent);
  color: var(--gb-accent2);
}

.nav-menu button.nav-active {
  background: var(--gb-accent);
  color: var(--gb-bg0);
  border-color: var(--gb-accent);
  font-weight: 600;
}

.nav-menu button.nav-active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gb-accent2);
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  text-align: right;
}

.user-name {
  color: var(--gb-accent2);
  font-weight: 600;
  font-size: 0.95rem;
}

.user-role {
  color: #928374;
  font-size: 0.8rem;
}

.logout-btn {
  background: var(--gb-bg0);
  border: 1px solid var(--gb-accent);
  color: var(--gb-fg);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: var(--gb-accent);
  color: var(--gb-bg0);
}

/* App Content */
.app-content {
  flex: 1;
  background: var(--gb-bg0);
  overflow-y: auto;
}

/* Coming Soon Placeholder */
.coming-soon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
  color: #928374;
}

.coming-soon h2 {
  color: var(--gb-accent2);
  margin-bottom: 1rem;
  font-size: 2rem;
  font-weight: 600;
}

.coming-soon p {
  max-width: 600px;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-navigation {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .nav-brand {
    text-align: center;
  }

  .nav-menu {
    justify-content: center;
  }

  .nav-user {
    justify-content: center;
    flex-direction: column;
    gap: 0.5rem;
  }

  .user-info {
    text-align: center;
  }

  .nav-menu button {
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .nav-brand h1 {
    font-size: 1.5rem;
  }

  .nav-menu {
    flex-direction: column;
  }

  .nav-menu button {
    width: 100%;
  }
}
