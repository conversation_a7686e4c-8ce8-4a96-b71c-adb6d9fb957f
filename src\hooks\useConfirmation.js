import { useState } from 'react';

export const useConfirmation = () => {
    const [confirmationState, setConfirmationState] = useState({
        isOpen: false,
        title: '',
        message: '',
        confirmText: 'Confirm',
        cancelText: 'Cancel',
        confirmButtonStyle: 'danger',
        onConfirm: () => {},
        onCancel: () => {}
    });

    const showConfirmation = ({
        title,
        message,
        confirmText = 'Confirm',
        cancelText = 'Cancel',
        confirmButtonStyle = 'danger'
    }) => {
        return new Promise((resolve) => {
            setConfirmationState({
                isOpen: true,
                title,
                message,
                confirmText,
                cancelText,
                confirmButtonStyle,
                onConfirm: () => {
                    setConfirmationState(prev => ({ ...prev, isOpen: false }));
                    resolve(true);
                },
                onCancel: () => {
                    setConfirmationState(prev => ({ ...prev, isOpen: false }));
                    resolve(false);
                }
            });
        });
    };

    const hideConfirmation = () => {
        setConfirmationState(prev => ({ ...prev, isOpen: false }));
    };

    return {
        confirmationState,
        showConfirmation,
        hideConfirmation
    };
};

export const useInputModal = () => {
    const [inputModalState, setInputModalState] = useState({
        isOpen: false,
        title: '',
        message: '',
        placeholder: '',
        defaultValue: '',
        confirmText: 'Save',
        cancelText: 'Cancel',
        required: false,
        onConfirm: () => {},
        onCancel: () => {}
    });

    const showInputModal = ({
        title,
        message = '',
        placeholder = '',
        defaultValue = '',
        confirmText = 'Save',
        cancelText = 'Cancel',
        required = false
    }) => {
        return new Promise((resolve) => {
            setInputModalState({
                isOpen: true,
                title,
                message,
                placeholder,
                defaultValue,
                confirmText,
                cancelText,
                required,
                onConfirm: (value) => {
                    setInputModalState(prev => ({ ...prev, isOpen: false }));
                    resolve(value);
                },
                onCancel: () => {
                    setInputModalState(prev => ({ ...prev, isOpen: false }));
                    resolve(null);
                }
            });
        });
    };

    const hideInputModal = () => {
        setInputModalState(prev => ({ ...prev, isOpen: false }));
    };

    return {
        inputModalState,
        showInputModal,
        hideInputModal
    };
};